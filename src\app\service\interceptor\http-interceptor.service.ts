import {
  HttpInterceptor,
  HttpRequest,
  HttpHandler,
  HttpEvent,
  HttpErrorResponse,
  HttpResponse,
} from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Router } from '@angular/router';
import { Observable, catchError, tap, throwError } from 'rxjs';
import { ConfirmService } from '../utils/confirm.service';

@Injectable({
  providedIn: 'root',
})
export class HttpInterceptorService implements HttpInterceptor {
  private isDialogOpen: boolean = false;

  constructor(private confirmService: ConfirmService, private router: Router) {}

  intercept(
    request: HttpRequest<any>,
    next: HttpHandler
  ): Observable<HttpEvent<any>> {
    return next.handle(request).pipe(
      tap((event) => {
        // 這裡攔截成功回應
        if (event instanceof HttpResponse) {
          // 你可以在這裡加入任何成功回應時要執行的邏輯
        }
      }),
      catchError((error: HttpErrorResponse) => {
        // if (error.status === 401 && !this.isDialogOpen) {
        //   this.isDialogOpen = true;
        //   this.confirmService
        //     .showError('沒有權限', '錯誤')
        //     .afterClosed()
        //     .subscribe(() => {
        //       this.isDialogOpen = false;
        //       this.router.navigate(['/home']);
        //     });
        // }
        if (
          (error.status === 500 ||
            error.status === 503 ||
            error.status === 400) &&
          !this.isDialogOpen
        ) {
          this.isDialogOpen = true;
          this.confirmService
            .showError('伺服器錯誤', '錯誤')
            .afterClosed()
            .subscribe(() => {
              this.isDialogOpen = false;
              this.router.navigate(['home']);
            });
        }
        return throwError(() => error);
      })
    );
  }
}
