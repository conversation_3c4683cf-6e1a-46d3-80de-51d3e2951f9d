import { Component } from '@angular/core';
import { GetEthnicityService } from '../../../../service/utils/get-ethnicity.service';
import { DownloadService } from '../../../../service/curl/download.service';
import {
  downloadTextFileResp,
  fullTextItem,
} from '../../../../interface/download.interface';
import { SpinnerService } from '../../../../service/utils/spinner.service';
import { UtilsService } from '../../../../service/utils/utils.service';
import { RouterLink } from '@angular/router';
import { DatePipe } from '@angular/common';

@Component({
    selector: 'app-text-download',
    templateUrl: './text-download.component.html',
    styleUrl: './text-download.component.scss',
    imports: [RouterLink, DatePipe],
})
export class TextDownloadComponent {
  ethnicity: string | null = null;
  tribeId: string | null = null;
  list: {
    column: string;
    title: string;
    width: string;
  }[] = [
    {
      column: '',
      title: '項次',
      width: '5%',
    },
    {
      column: '',
      title: '文件',
      width: '',
    },
    {
      column: '',
      title: '格式',
      width: '20%',
    },
    {
      column: '',
      title: '時間',
      width: '20%',
    },
  ];

  fileList: fullTextItem[] = [];

  constructor(
    private getEthnicityService: GetEthnicityService,
    private downloadService: DownloadService,
    private spinnerService: SpinnerService,
    private utils:UtilsService
  ) {}

  ngOnInit(): void {
    this.ethnicity = this.getEthnicityService.GetEthnicityName();
    this.utils.setTitle(`${this.ethnicity}-全文下載`)
    this.tribeId = this.getEthnicityService.GetEthnicityId();
    this.downloadTextFile();
  }

  back(event:Event) {
    event.preventDefault();
    history.back();
  }

  downloadTextFile() {
    this.spinnerService.show();
    this.downloadService.downloadTextFile(this.tribeId as string).subscribe({
      next: (resp: downloadTextFileResp) => {
        this.spinnerService.hide();
        this.fileList = resp.data.fullTextItems;
      },
      error: () => {
        this.spinnerService.hide();
      },
    });
  }
}
