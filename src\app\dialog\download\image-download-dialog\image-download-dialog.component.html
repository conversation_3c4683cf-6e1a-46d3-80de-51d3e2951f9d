<div mat-dialog-title class="success-title">
    <mat-icon class="dialog-close-btn" tabindex="0" (click)="close()">close</mat-icon>
</div>
<mat-dialog-content>
    @if(isLoading){
    <div class="spinner-wrapper-index">
        <mat-spinner class="mat-spinner-color"></mat-spinner>
    </div>
    }@else{

    <div class="input-group2">
        <span class="card-download-item2">
            <span class="font_r">*</span>檔案格式設定
        </span>
        <!-- 字型設定 -->
        <form [formGroup]="form">
            <div class="checkbox-group">
                <ul class="checkbox-menu">
                    <label class="select_title">
                        <input type="radio" id="radio1" formControlName="fileType" value="1">
                        <label for="radio1">直式下載(PDF檔)</label>
                    </label>
                </ul>
                <ul class="checkbox-menu">
                    <label class="select_title">
                        <input type="radio" id="radio2" formControlName="fileType" value="2">
                        <label for="radio2">橫式下載(PDF檔)</label>
                    </label>
                </ul>
            </div>
        </form>
    </div>

    <div class="btn-group">
        <input class="btn-list btn-secondary-color" value="取消" type="button" [mat-dialog-close]="">
        <input class="btn-list btn-primary-color" value="確定" type="button" (click)="send()">
    </div>
    }
</mat-dialog-content>