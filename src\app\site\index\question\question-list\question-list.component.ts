import { Component } from '@angular/core';
import { Router, RouterLink } from '@angular/router';
import { LanguageService } from '../../../../service/curl/language.service';
import { getDialectAndLanguageListResp } from '../../../../interface/language.interface';
import { apiStatus } from '../../../../enum/apiStatus.enum';
import { QuestionService } from '../../../../service/curl/question.service';
import { ConfirmService } from '../../../../service/utils/confirm.service';
import {
  dictionaryQuestionItem,
  getDictionaryQuestionListReq,
  getDictionaryQuestionListResp,
} from '../../../../interface/question.interface';
import { UtilsService } from '../../../../service/utils/utils.service';
import { FormsModule } from '@angular/forms';
import { PaginatorComponent } from '../../../../utils/paginator/paginator.component';

@Component({
    selector: 'app-question-list',
    templateUrl: './question-list.component.html',
    styleUrl: './question-list.component.scss',
    imports: [
        RouterLink,
        FormsModule,
        PaginatorComponent,
    ],
})
export class QuestionListComponent {
  keyword: string = '';
  questionList: dictionaryQuestionItem[] = [];
  tribe: string | null = null;
  dialect: string | null = null;
  status: number | null = null;

  languageList: { id: string; name: string }[] = [];
  tempDialectList: {
    id: string;
    name: string;
    tribeId: string;
  }[] = [];
  dialectList: {
    id: string;
    name: string;
    tribeId: string;
  }[] = [];

  pageSize: number = 10; //一頁幾筆資料
  nowPage: number = 1;
  totalCount: number = 0; //總筆數
  pageShowCount: number = 5; //分頁器秀幾個

  list: {
    column: string;
    title: string;
    width: string;
    sort: boolean;
  }[] = [
    {
      column: 'index',
      title: '項次',
      width: '5%',
      sort: false,
    },
    {
      column: 'tribe',
      title: '族語',
      width: '8%',
      sort: false,
    },
    {
      column: 'dictionaryName',
      title: '詞項',
      width: '20%',
      sort: false,
    },
    {
      column: 'content',
      title: '回饋建議',
      width: '',
      sort: true,
    },
    {
      column: 'reply',
      title: '處理內容',
      width: '20%',
      sort: true,
    },
    {
      column: 'status',
      title: '處理狀態',
      width: '7%',
      sort: true,
    },
    {
      column: 'creator',
      title: '回饋者',
      width: '8%',
      sort: false,
    },
  ];

  constructor(
    private router: Router,
    private languageService: LanguageService,
    private confirmService: ConfirmService,
    private questionService: QuestionService,
    private utils: UtilsService
  ) {}

  ngOnInit(): void {
    this.utils.setTitle('詞項回饋');
    window.scrollTo({
      top: 0,
      behavior: 'smooth',
    });
    this.getDialectAndLanguageList();
    this.getQuestionList();
  }

  search() {
    this.nowPage = 1;
    this.getQuestionList();
  }

  getDialectAndLanguageList() {
    this.languageService.getDialectAndLanguageList().subscribe({
      next: (resp: getDialectAndLanguageListResp) => {
        if (resp.status === apiStatus.SUCCESS) {
          const { tribes } = resp.data;
          this.languageList = tribes;
          this.tempDialectList = tribes.flatMap((tribe) =>
            tribe.dialectList.map((dialect) => ({
              ...dialect,
            }))
          );
        }
      },
      error: () => {},
    });
  }

  selectLanguage() {
    this.dialect = null;
    const selectedDialects = this.tempDialectList.filter(
      (item) => item.tribeId === this.tribe
    );
    this.dialectList = selectedDialects.length > 0 ? selectedDialects : [];
  }

  getQuestionList() {
    let req: getDictionaryQuestionListReq = {
      page: this.nowPage,
      pageSize: this.pageSize,
      keyword: this.keyword,
      tribeId: this.tribe,
      dialectId: this.dialect,
      status: this.status,
      orderType: '',
      orderByColumn: '',
    };
    this.questionService.getDictionaryQuestionList(req).subscribe({
      next: (resp: getDictionaryQuestionListResp) => {
        if (resp.status === apiStatus.SUCCESS) {
          this.questionList = resp.data.opinionItems;
          this.totalCount = resp.data.itemTotalCount;
        } else {
          this.confirmService.showError(resp.message, '錯誤');
        }
      },
      error: () => {},
    });
  }

  /**
   * 第幾頁
   * @param item number
   */
  getPageFromPaginator(nowPage: number) {
    this.nowPage = nowPage;
    this.getQuestionList();
  }
  /**
   * 每頁資料數量
   * @param item number
   */
  getPageSizeFromPaginator(pageSize: number) {
    this.pageSize = pageSize;
    this.nowPage = 1;
    this.getQuestionList();
  }

  back(event: Event) {
    event.preventDefault();
    history.back();
  }

  detail(item: dictionaryQuestionItem) {
    this.router.navigate(['/singleQuestion'], {
      queryParams: { id: item.dictionaryId, tribeId: item.tribeId },
    });
  }
}
