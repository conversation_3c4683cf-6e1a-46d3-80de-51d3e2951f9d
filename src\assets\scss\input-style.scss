// Scss Document
button,
input,
select,
textarea {
	box-sizing: border-box;
	font-size: 1.5em;
	line-height: 1.875em;
    font-family: 'Times New Roman', Times, serif !important;
}
select {
	margin: 10px 0;
	padding: 14px 20px;
	border-radius: 5px;
	background-color: #fff;
	border: 1px solid #ccc;
	box-shadow: 0 1px 1px rgba(0, 0, 0, 0.075) inset;
	box-sizing: border-box;
	transition:
		border-color 0.15s ease-in-out 0s,
		box-shadow 0.15s ease-in-out 0s;
}

/*一般*/
.form-control {
	margin: 10px 0;
	padding: 10px 20px;
	background-color: #fff;
	background-image: none;
	border: 1px solid #ccc;
	border-radius: 5px;
	box-shadow: 0 1px 1px rgba(0, 0, 0, 0.075) inset;
	display: inline-block;
	transition:
		border-color 0.15s ease-in-out 0s,
		box-shadow 0.15s ease-in-out 0s;
	box-sizing: border-box;
	&:focus {
		border: 3px solid #4a7f42;
		outline: 0;
		box-shadow: 0 0 0 0.25rem #d8eed4;
	}
}

.select_control {
	margin: 2px;
	padding: 0;
	overflow: hidden;
	select {
		border: 1px solid #ccc;
		border-radius: 5px;
		padding: 35px 21px 35px 21px;
		margin: 0;
	}
}

.Wdate {
	margin: 2px 4px 4px 0px !important;
	padding: 8px 16px !important;
	display: table-cell;
	border-radius: 5px;
	border: 1px solid #ccc !important;
	box-shadow: 0 1px 1px rgba(0, 0, 0, 0.075) inset !important;
	color: #555;
	line-height: 1.8;
	transition:
		border-color 0.15s ease-in-out 0s,
		box-shadow 0.15s ease-in-out 0s;
	&:focus {
		border: 3px solid #00b4ff;
		outline: 0;
		box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 25%);
	}
}
@media screen and (max-width: 768px) {
	textarea {
		width: 100%;
	}
	.select_control {
		select {
			margin: 8px 0;
			width: 100% !important;
		}
	}
}
