<main class="master-pages-container-layout">
    <div class="master-pages-container-cont">
        <div class="cont pages-cont-layout">
            <div class="pages-cont-list-layout">
                <!--路徑列-->
                <div class="breadcrumb-layout">
                    <div>
                        <nav class="breadcrumb-cont">
                            <ol class="breadcrumb">
                                <li class="breadcrumb-item">
                                    <a href="" (click)="$event.defaultPrevented" [routerLink]="'/home'">
                                        <span>首頁</span>
                                        <span class="material-symbols-outlined">keyboard_arrow_right</span>
                                    </a>
                                </li>
                                @if(tab!==2){
                                <li class="breadcrumb-item active">書寫系統修訂</li>
                                }@else{
                                <li class="breadcrumb-item">
                                    <a href="" (click)="$event.defaultPrevented"
                                        [routerLink]="'/revisionNotice/writingSystem'" [queryParams]="{'tab':1}">
                                        <span>書寫系統修訂</span>
                                        @if(tab===2){
                                        <span class="material-symbols-outlined">keyboard_arrow_right</span>
                                        }
                                    </a>
                                </li>
                                @if(tab===2){
                                <li class="breadcrumb-item active">詞項</li>
                                }
                                }

                            </ol>
                        </nav>
                    </div>
                    <div>
                        <nav class="breadcrumb-cont">
                            <ol class="breadcrumb">
                                <li class="breadcrumb-item">
                                    <a hre="" (click)="back($event)">
                                        <span>&lt;&lt;回上一頁</span>
                                    </a>
                                </li>
                            </ol>
                        </nav>
                    </div>
                </div>
                <!--內容區-->
                <div class="word-description-container">
                    <div class="advanced-container">
                        <main class="second-search-layout">
                            <div class="search-group">
                                <div class="search-all" #searchAll>
                                    <div class="search-box">
                                        <input class="search-bar font_18" type="text" title="keyword"
                                            placeholder="請輸入族語或中文" [(ngModel)]="keyword"
                                            (ngModelChange)="onKeywordChange($event)"
                                            (keydown.enter)="search(); keywordInput.blur()" #keywordInput>
                                        <a class="search-a1" href=""
                                            (click)="$event.preventDefault();keyword='';autoInputList=[];">
                                            <span class="material-symbols-outlined">close</span>
                                        </a>
                                    </div>
                                    <!--搜尋框架-->
                                    @if(autoInputList.length>0){
                                    <div class="search-frame">
                                        @for (value of autoInputList; track value) {
                                        <div class="search-frame-info" (click)="selectValue(value)">
                                            <span class="search-frame-name">{{value}}</span>
                                        </div>
                                        }
                                    </div>
                                    }
                                </div>
                                <div class="btns">
                                    <input class="btn-box btn-primary-color" value="搜尋" type="button"
                                        (click)="search()">
                                </div>
                            </div>
                            <!--鍵盤-->
                            <div class="keyboard-group">
                                <div class="keyboard-box">
                                    @for (key of keyboardList; track key) {
                                    <button class="keyboard-btn" (click)="keyboardUp(key,keywordInput)">
                                        <span class="keyboard_font">{{key}}</span>
                                    </button>
                                    }
                                </div>
                            </div>
                            <!--下拉選單1 -->
                            <div class="second-search-list">
                                <div class="second-search-box">
                                    <div class="second-search-title" (click)="isLanguard=!isLanguard">
                                        <div class="second-search-h4 font_w">請選擇族語 (可多選)</div>
                                    </div>
                                    @if(isLanguard){
                                    <mat-icon class="material-symbols-outlined font_w"
                                        (click)="isLanguard=false">keyboard_arrow_down</mat-icon>
                                    }@else{
                                    <mat-icon class="isDialect-symbols-outlined font_w"
                                        (click)="isLanguard=true">keyboard_arrow_up</mat-icon>
                                    }
                                </div>
                                @if(isLanguard){
                                <div class="second-search-box2">
                                    <div class="advanced-search-cont-layout">
                                        <ul class="advanced-search-cont">
                                            @for ( item of languageList; let i=$index; track i) {
                                            <li class="advanced-search-item">
                                                <span class="checkbox-list">
                                                    <input id="languageCheckbox{{i}}" type="checkbox" [value]="item.id"
                                                        (change)="languageListChange($event, item.id)"
                                                        [checked]="item.active">
                                                    <label for="languageCheckbox{{i}}">{{item.name}}</label>
                                                </span>
                                            </li>
                                            }
                                        </ul>
                                    </div>
                                </div>
                                }
                            </div>
                            <!--下拉選單2 -->
                            <div class="second-search-list">
                                <div class="second-search-box">
                                    <div class="second-search-title" (click)="isDialect=!isDialect">
                                        <div class="second-search-h4 font_w">請選擇語別 (可多選)</div>
                                    </div>
                                    @if(isDialect){
                                    <mat-icon class="material-symbols-outlined font_w"
                                        (click)="isDialect=false">keyboard_arrow_down</mat-icon>
                                    }@else{
                                    <mat-icon class="isDialect-symbols-outlined font_w"
                                        (click)="isDialect=true">keyboard_arrow_up</mat-icon>
                                    }
                                </div>
                                @if(isDialect){
                                <div class="second-search-box2">
                                    <div class="advanced-search-cont-layout">
                                        <ul class="advanced-search-cont">
                                            @for ( item of dialectList; let i=$index; track i) {
                                            <li class="advanced-search-item">
                                                <span class="checkbox-list">
                                                    <input id="dialectCheckbox{{i}}" type="checkbox" [value]="item.id"
                                                        (change)="dialectListChange($event, item.id,item.tribeId)"
                                                        [checked]="item.active" [disabled]="item.disabled">
                                                    <label for="dialectCheckbox{{i}}">{{item.name}}</label>
                                                </span>
                                                <!-- <span class="theme-count">({{item.count}})</span> -->
                                            </li>
                                            }
                                        </ul>
                                    </div>
                                </div>
                                }
                            </div>
                        </main>
                    </div>
                    @if(tab===1){
                    <div class="word-description-list-layout">
                        <div style="width: 100%;">
                            <main class="query-download-layout">
                                <table class="table-list-layout rwd-table01">
                                    <tbody>
                                        <tr class="th-no">
                                            <th class="th_no">
                                                修訂時間
                                            </th>
                                            <th class="th_no">族別</th>
                                            <th class="th_no">語別</th>
                                            <th class="th_no">詞項中文</th>
                                            <th class="th_no">修訂後</th>
                                            <th class="th_no">修訂前</th>
                                            <th class="th_no">說明</th>
                                        </tr>
                                        @for (item of homeRevisionList; track item) {
                                        <tr>
                                            <td class="text_c">
                                                <span class="rwd-th">修訂時間</span>
                                                {{item.revisionTime|date:'yyyy.MM.dd'}}
                                            </td>
                                            <td class="text_c">
                                                <span class="rwd-th">族別</span>
                                                {{item.tribeName}}
                                            </td>
                                            <td class="text_c">
                                                <span class="rwd-th">語別</span>
                                                {{item.dialectName}}
                                            </td>
                                            <td class="text_c">
                                                <span class="rwd-th">詞項中文</span>
                                                {{item.explanation}}
                                            </td>
                                            <td class="text_c">
                                                <span class="rwd-th">修訂後</span>
                                                <a class="link-font bottom-border font-w" href=""
                                                    (click)="linkTo(item.dictionaryId,item.after);$event.preventDefault()">{{item.after}}</a>
                                            </td>
                                            <td class="text_c">
                                                <span class="rwd-th">修訂前</span>
                                                {{item.before}}
                                            </td>
                                            <td class="text_c">
                                                <span class="rwd-th">說明</span>
                                                {{item.note}}
                                            </td>
                                        </tr>
                                        }
                                        @empty{
                                        <tr>
                                            <td colspan="6" style="text-align: center;">沒有找到符合條件的資料</td>
                                        </tr>
                                        }
                                    </tbody>
                                </table>
                            </main>
                            @if(homeRevisionList.length>0){
                            <app-paginator [pageSize]="revisionListPageSize" [totalRecords]="revisionListTotalCount"
                                [pageShowCount]="pageShowCount" [nowPage]="revisionListNowPage"
                                currentPageReportTemplate="第 {first} 到 {last} 筆，共 {totalRecords} 筆"
                                (clickPageEvent)="getRevisionListPageFromPaginator($event)"
                                (pageSizeChangeEvent)="getRevisionListPageSizeFromPaginator($event)"></app-paginator>
                            }
                        </div>
                    </div>
                    }@else{
                    <div class="word-description-list-layout">
                        <mat-tab-group (selectedTabChange)="selectTab($event)" #tabGroup>
                            @for ( item of searchGroup; track $index) {
                            <mat-tab [label]="item" [disabled]="!keyword && !dictionaryId">
                                @if(tabGroup.selectedIndex === $index){
                                <ng-template mat-tab-label>{{item}}</ng-template>
                                <div class="word-description-layout-title">
                                    {{item}}查詢條件：所有族語「{{showKeyword}}」 之查詢結果
                                    (共{{totalCount}}筆)
                                </div>
                                <!--單詞內容區-->
                                @if(dictionaryList.length>0){
                                <div class="word-description-layout">
                                    <!--族語單詞-->
                                    <mat-accordion multi>
                                        @for (item of dictionaryList; track $index) {
                                        <mat-expansion-panel id="dictionary" [expanded]="item.isOpenPanel"
                                            (opened)="onPanelOpened(item)" (closed)="onPanelClose(item)"
                                            [style.font-size.em]="1">
                                            <mat-expansion-panel-header class="panel-header">
                                                <div class="panel-header-group">
                                                    <div class="panel-header-name" style="padding-right: 10px;">
                                                        <div class="panel-header-name-group"
                                                            style="display: flex;align-items: center;">
                                                            <div class="panel-header-tag-group"
                                                                style="display: flex;align-items: center;">
                                                                <span
                                                                    class="material-symbols-outlined font_g text_r">language</span>
                                                                <span
                                                                    class="panel-header-tag">{{item.dialect?item.dialect:item.tribe}}
                                                                </span>
                                                            </div>
                                                            <div class="word-description-cont-font name-box">
                                                                <span>{{item.name}} </span>
                                                            </div>
                                                            @for (audioItem of item.audioItems; track audioItem) {
                                                            <div style="display: flex;align-items: center;">
                                                                <span
                                                                    class="material-symbols-outlined word-description-cont-icon"
                                                                    (click)="play(audioItem)"
                                                                    (click)="$event.stopPropagation()">
                                                                    volume_up</span>{{audioItem.audioClass}}
                                                            </div>
                                                            }
                                                        </div>
                                                    </div>
                                                    <div class="panel-header-explanation">
                                                        <div style="display: flex; align-items:center; line-height: 1;">
                                                            <span class="panel-header-description-tag tag">解釋</span>
                                                            <span class="word-description-cont-font"
                                                                [innerHTML]="sanitizeExplanation(item.chineseExplanation)                                                                ">
                                                            </span>
                                                        </div>
                                                    </div>
                                                </div>
                                            </mat-expansion-panel-header>
                                            @if(item.loading){
                                            <div class="spinner-wrapper-index">
                                                <mat-spinner class="mat-spinner-color"></mat-spinner>
                                            </div>
                                            }@else{
                                            <!--功能區塊-->
                                            <div class="function-block-background" [style.font-size.em]="fontSize">
                                                <!--詞根-->
                                                <div class="term-list-box2 bg_0">
                                                    <div class="term-list-title text_r100">
                                                        <span class="material-symbols-outlined text_r">emergency</span>
                                                        <div class="term-list-h4 font_bold">詞根：</div>
                                                        <div class="term-list-h4 font_bold">
                                                            {{item.derivativeRoot?item.derivativeRoot:"無"}}
                                                        </div>
                                                    </div>
                                                    <div class="term-list-title">
                                                        <span class="material-symbols-outlined text_r">book_4</span>
                                                        <div class="term-list-h4 font_bold">收錄來源：</div>
                                                        <div class="term-list-h4 font_bold">
                                                            {{item.wordItem?.sources&&item.wordItem?.sources!.length>0?item.wordItem?.sources!.join('|'):'無'}}
                                                        </div>
                                                    </div>
                                                </div>
                                                <!--社群-->
                                                <div class="term-list-box2">
                                                    <div class="term-list-title">
                                                        <div class="term-list-btns bg_fb font_w"
                                                            (click)="share(shareType.FB,item.id)">
                                                            <img src="icons/facebook-icon.svg" alt="fb">
                                                            <h4>Facebook</h4>
                                                        </div>
                                                        <div class="term-list-btns bg_line font_w"
                                                            (click)="share(shareType.LINE,item.id)">
                                                            <img src="icons/line-icon.svg" alt="line">
                                                            <h4>LINE</h4>
                                                        </div>
                                                        <div class="term-list-btns bg_opinion font_w"
                                                            (click)="question(item)">
                                                            <span class="material-symbols-outlined">chat</span>
                                                            <h4>意見回饋</h4>
                                                        </div>
                                                        @if(item.isDerivativeRoot){
                                                        <div class="term-list-btns bg_root font_w"
                                                            (click)="getRootStructure(item.id)">
                                                            <span class="material-symbols-outlined">device_hub</span>
                                                            <h4>詞根結構</h4>
                                                        </div>
                                                        }
                                                        @if(item.isImage){
                                                        <div class="term-list-btns bg_picture font_w"
                                                            (click)="openImage(item)">
                                                            <span class="material-symbols-outlined">imagesmode</span>
                                                            <h4>圖片</h4>
                                                        </div>
                                                        }
                                                    </div>
                                                </div>
                                            </div>
                                            <!--單詞說明-->
                                            <div class="word-description-explain-layout">
                                                @for (wordItem of item.wordItem?.explanationItems; track $index) {
                                                <!--解釋1-->
                                                <div class="word-description-explain-list">
                                                    <!--說明-->
                                                    <div class="word-description-explain-item"
                                                        [style.font-size.em]="fontSize">
                                                        <!--說明文-->
                                                        <div class="word-description-explain">
                                                            <div>
                                                                <span
                                                                    class="word-description-explain-tag tag">解釋{{$index+1}}</span>
                                                                <span
                                                                    class="word-description-explain-text">{{wordItem.chineseExplanation}}</span>&nbsp;&nbsp;
                                                            </div>
                                                            <div>
                                                                <label>
                                                                    焦點 :&nbsp;
                                                                    {{wordItem.focus.length>0?wordItem.focus!.join('|'):'無'}}&nbsp;|&nbsp;
                                                                    範疇 :
                                                                    {{wordItem.category.length>0?wordItem.category.join('|'):'無'}}&nbsp;|&nbsp;
                                                                    詞類 :
                                                                    {{wordItem.partOfSpeech.length>0?wordItem.partOfSpeech.join('|'):'無'}}
                                                                </label>
                                                            </div>
                                                        </div>
                                                        <!--範例-->
                                                        @for ( sentenceItems of wordItem.sentenceItems; track $index) {
                                                        <div class="word-description-sentence">
                                                            <span class="word-description-sentence-text">
                                                                @for ( anaphoraSentenceItem of
                                                                sentenceItems.anaphoraSentence;
                                                                track $index) {
                                                                <div style="position: relative;"
                                                                    (copy)="onCopy($event)">
                                                                    <span
                                                                        [ngClass]="{ 'bottom-border': anaphoraSentenceItem.id!== null,
                                                                        'bottom-border-hidden': anaphoraSentenceItem.id=== null,
                                                                        'highlighted': isNameMatched(anaphoraSentenceItem.name, item.name) }"
                                                                        (click)="clickAnaphoraSentence(anaphoraSentenceItem.id)">
                                                                        <span
                                                                            [innerHTML]="sanitizeExplanation(anaphoraSentenceItem.name)"></span>
                                                                    </span>
                                                                </div>&nbsp;
                                                                }
                                                                @for ( audioItems of sentenceItems.audioItems; track
                                                                audioItems) {
                                                                <span
                                                                    class="material-symbols-outlined word-description-cont-icon"
                                                                    (click)="play(audioItems)">volume_up</span>{{audioItems.audioClass}}
                                                                }
                                                            </span>
                                                            <span
                                                                class="word-description-sentence-translate">{{sentenceItems.chineseSentence}}</span>
                                                        </div>
                                                        }
                                                    </div>
                                                    @if(wordItem.isImage){
                                                    <div class="carousel-block">
                                                        <owl-carousel-o [options]="customOptions">
                                                            @for (item of wordItem.imageUrl; track item;let i=$index) {
                                                            <ng-template carouselSlide>
                                                                <img [src]="item" [alt]="'圖'+i">
                                                            </ng-template>
                                                            }
                                                        </owl-carousel-o>
                                                    </div>
                                                    }
                                                </div>
                                                }
                                            </div>
                                            }
                                            <div>
                                                <mat-accordion>
                                                    <mat-expansion-panel id="revision"
                                                        (opened)="onRevisionPanelOpened(item)">
                                                        <mat-expansion-panel-header class="panel-header">
                                                            <div class="panel-header-group">
                                                                <div style="display: flex; align-items:center;">
                                                                    <span class="panel-header-description-tag"
                                                                        style="font-size: 1.5em;font-weight: bold;">修訂表</span>
                                                                </div>
                                                            </div>
                                                        </mat-expansion-panel-header>
                                                        @if(item.loading){
                                                        <div class="spinner-wrapper-index">
                                                            <mat-spinner class="mat-spinner-color"></mat-spinner>
                                                        </div>
                                                        }@else{
                                                        <!--功能區塊-->
                                                        <div class="web-box">
                                                            <div class="description-explain"
                                                                [style.font-size.em]="fontSize">
                                                                <div class="description-item">
                                                                    <div class="description-title">
                                                                        修訂前
                                                                    </div>
                                                                    <div class="description-title">
                                                                        修訂內容
                                                                    </div>
                                                                    <div class="description-title">
                                                                        修訂日期
                                                                    </div>
                                                                </div>
                                                                @for (item of item.revisionList; track item) {
                                                                <div class="description-item">
                                                                    <div class="description-text">
                                                                        {{item.after}}
                                                                    </div>
                                                                    <div class="description-note">
                                                                        {{item.note}}
                                                                    </div>
                                                                    <div class="description-time">
                                                                        {{item.creationTime|date:"yyyy/MM/dd"}}
                                                                    </div>
                                                                </div>
                                                                }@empty {
                                                                <div class="description-notfound">
                                                                    <div class="description-title">
                                                                        目前尚有修訂資料
                                                                    </div>
                                                                </div>
                                                                }
                                                            </div>
                                                        </div>
                                                        <div class="phone-box">
                                                            <div class="description-explain"
                                                                [style.font-size.em]="fontSize">
                                                                @for (item of item.revisionList; track item) {
                                                                <div class="description-item">
                                                                    <div class="description-text">
                                                                        修訂前: {{item.before}}
                                                                    </div>
                                                                    <div class="description-note">
                                                                        修訂內容: {{item.note}}
                                                                    </div>
                                                                    <div class="description-time">
                                                                        修訂日期: {{item.creationTime|date:"yyyy/MM/dd"}}
                                                                    </div>
                                                                </div>
                                                                }@empty {
                                                                <div class="description-notfound">
                                                                    <div class="description-title">
                                                                        目前尚有修訂資料
                                                                    </div>
                                                                </div>
                                                                }
                                                            </div>
                                                        </div>
                                                        }
                                                    </mat-expansion-panel>
                                                </mat-accordion>
                                            </div>
                                            <div>
                                                <mat-accordion>
                                                    <mat-expansion-panel id="revision"
                                                        (opened)="onWritingSystemPanelOpened(item)">
                                                        <mat-expansion-panel-header class="panel-header">
                                                            <div class="panel-header-group">
                                                                <div style="display: flex; align-items:center;">
                                                                    <span class="panel-header-description-tag"
                                                                        style="font-size: 1.5em;font-weight: bold;">書寫系統</span>
                                                                </div>
                                                            </div>
                                                        </mat-expansion-panel-header>
                                                        @if(item.loading){
                                                        <div class="spinner-wrapper-index">
                                                            <mat-spinner class="mat-spinner-color"></mat-spinner>
                                                        </div>
                                                        }@else{
                                                        <!--功能區塊-->
                                                        <div class="web-box">
                                                            <div class="description-explain"
                                                                [style.font-size.em]="fontSize">
                                                                <div class="description-item">
                                                                    <div class="description-title">
                                                                        修訂前
                                                                    </div>
                                                                    <div class="description-title">
                                                                        修訂內容
                                                                    </div>
                                                                    <div class="description-title">
                                                                        修訂日期
                                                                    </div>
                                                                </div>
                                                                @for (item of item.writingSystemList; track item) {
                                                                <div class="description-item">
                                                                    <div class="description-text">
                                                                        {{item.after}}
                                                                    </div>
                                                                    <div class="description-note">
                                                                        {{item.note}}
                                                                    </div>
                                                                    <div class="description-time">
                                                                        {{item.creationTime|date:"yyyy/MM/dd"}}
                                                                    </div>
                                                                </div>
                                                                }@empty {
                                                                <div class="description-notfound">
                                                                    <div class="description-title">
                                                                        目前查無符合條件之資料！
                                                                    </div>
                                                                </div>
                                                                }
                                                            </div>
                                                        </div>
                                                        <div class="phone-box">
                                                            <div class="description-explain"
                                                                [style.font-size.em]="fontSize">
                                                                @for (item of item.writingSystemList; track item) {
                                                                <div class="description-item">
                                                                    <div class="description-text">
                                                                        修訂前: {{item.before}}
                                                                    </div>
                                                                    <div class="description-note">
                                                                        修訂內容:{{item.note}}
                                                                    </div>
                                                                    <div class="description-time">
                                                                        修訂日期: {{item.creationTime|date:"yyyy/MM/dd"}}
                                                                    </div>
                                                                </div>
                                                                }@empty {
                                                                <div class="description-notfound">
                                                                    <div class="description-title">
                                                                        目前查無符合條件之資料！
                                                                    </div>
                                                                </div>
                                                                }
                                                            </div>
                                                        </div>
                                                        }
                                                    </mat-expansion-panel>
                                                </mat-accordion>
                                            </div>

                                        </mat-expansion-panel>
                                        }
                                    </mat-accordion>
                                </div>
                                }@else{
                                <div class="notfound-group">
                                    <span class="notfound-text">目前查無符合條件之資料！
                                    </span>
                                </div>
                                }
                                @if(dictionaryList.length>0){
                                <app-paginator [pageSize]="pageSize" [totalRecords]="totalCount"
                                    [pageShowCount]="pageShowCount" [nowPage]="nowPage"
                                    currentPageReportTemplate="第 {first} 到 {last} 筆，共 {totalRecords} 筆"
                                    (clickPageEvent)="getPageFromPaginator($event)"
                                    (pageSizeChangeEvent)="getPageSizeFromPaginator($event)"></app-paginator>
                                }
                                }
                            </mat-tab>
                            }
                        </mat-tab-group>
                    </div>
                    }

                </div>
            </div>
        </div>
    </div>
</main>