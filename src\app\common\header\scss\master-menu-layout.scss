.master-menu-layout {
  margin: 0;
  padding: 0 0 0 250px;
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-sizing: border-box;
  z-index: 1;
  .master-menu-icon {
    a {
      color: #ffffff79;
    }
  }
  //第一層
  .master-menu-cont {
    margin: 0;
    padding: 0;
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    list-style: none;
    .master-menu-cont-item {
      margin: 0;
      padding: 0;
      display: flex;
      align-items: center;
      justify-content: center;
      text-align: center;
      position: relative;
      .master-menu-drawer-item {
        margin: 0;
        padding: 10px 15px;
        text-decoration: none;
        // font-size: 1.563em;
        font-size: 1.5em;
        font-weight: bold;
        border-bottom-style: solid;
        border-bottom-width: 6px;
      }
      &:hover {
        > .master-dropdown-menu-layout {
          display: flex;
        }
      }
    }
  }
  //第二層
  //第二層
  .master-dropdown-menu-layout {
    display: none;
    flex-direction: row;
    flex-wrap: wrap;
    align-items: flex-start;
    justify-content: space-between;
    position: fixed;
    top: 105px;
    opacity: 1;
    min-width: 140px;
    box-shadow: 0px 15px 20px #36425029;
    .master-dropdown-menu-cont {
      margin: 0;
      padding: 0;
      list-style: none;
      .master-dropdown-menu-item {
        display: block;
        overflow: hidden;
        color: #364250;
        padding: 24px 10px;
        min-width: 140px;
        font-weight: bold;
        text-decoration: none;
        font-size: 1.2em;
      }
    }
    .master-dropdown-menu-item:hover {
      color: #4a7f42 !important;
      // background-color: #4a7f42;
    }
    .master-dropdown-menu-item.active {
      color: #4a7f42 !important;
      // background-color: #4a7f42 !important;
    }
  }
}
.master-menu-left {
  .master-menu-cont {
    justify-content: left;
  }
}
.master-menu-center {
  .master-menu-cont {
    justify-content: center;
    max-width: 1200px;
    min-width: auto;
  }
}
.master-menu-right {
  .master-menu-cont {
    justify-content: right;
  }
}

.master-menu-layout2 {
  margin: 0;
  padding: 0;
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  // z-index: 9999;
  .master-menu-icon {
    a {
      color: #ffffff79;
    }
  }
  //第一層
  .master-menu-cont {
    margin: 0;
    padding: 0;
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    list-style: none;
    padding-right: 25px;
    .master-menu-cont-item {
      margin: 0;
      padding: 0;
      display: flex;
      align-items: center;
      justify-content: center;
      text-align: center;
      position: relative;
      .master-menu-drawer-item {
        cursor: pointer;
        margin: 0;
        padding: 10px 15px;
        text-decoration: none;
        font-size: 1.5em;
        font-weight: bold;
        // width: 100%;
        background-color: #4a7f42;
        color: white;
      }
      &:hover {
        .master-menu-drawer-item {
          background-color: #4a7f42;
          color: #d8eed4;
        }
        > .master-dropdown-menu-layout {
          display: flex;
          justify-content: center;
        }
      }
    }
  }
  //第二層
  .master-dropdown-menu-layout {
    display: none;
    flex-direction: row;
    flex-wrap: wrap;
    align-items: flex-start;
    justify-content: space-between;
    position: fixed;
    top: 161px;
    opacity: 1;
    min-width: 140px;
    box-shadow: 0px 15px 20px #36425029;
    .master-dropdown-menu-cont {
      margin: 0;
      padding: 0;
      list-style: none;
      .master-dropdown-menu-item {
        display: block;
        overflow: hidden;
        color: #364250;
        padding: 24px 10px;
        min-width: 140px;
        font-weight: bold;
        text-decoration: none;
        font-size: 1.2em;
        background-color: white;
      }
    }
    .master-dropdown-menu-item:hover {
      color: #4a7f42 !important;
    }

    .master-dropdown-menu-item.active {
      color: #4a7f42 !important;
    }
  }
}

.master-menu-left {
  .master-menu-cont {
    justify-content: left;
  }
}
.master-menu-center {
  .master-menu-cont {
    justify-content: center;
    max-width: 1200px;
    min-width: auto;
  }
}
.master-menu-right {
  .master-menu-cont {
    justify-content: right;
  }
}
