import { defaultItem } from './share.interface';

export interface getAutoCompleteQuestionResp extends defaultItem {
  data: {
    tribe: string; // 族
    dialect: string; // 方言，NULL IF NULL
    dictionaryId: string; // 詞ID
    dictionaryName: string; // 詞
    chineseExplanation: string; // 解釋
    originalSentence: string; // 句子
  };
}

export interface getDictionaryQuestionListReq {
  page: number;
  pageSize: number;
  keyword: string; // 關鍵字，可NULL，全搜 IF NULL
  tribeId: string | null; // 單選族
  dialectId: string | null; // 單選方言
  status: number | null; // 處理狀況，可NULL，0 = 審查中、1 = 已處理、 NULL = 全搜
  orderType: string;
  orderByColumn: string;
}
export interface getDictionaryQuestionListResp extends defaultItem {
  data: {
    opinionItems: dictionaryQuestionItem[];
    orderType: string;
    orderByColumn: string;
    page: number;
    pageSize: number;
    pageTotalCount: number;
    itemTotalCount: number;
  };
}

export interface dictionaryQuestionItem {
  id: string; // 意見 ID
  tribeId: string; //族ID
  tribe: string; // 族
  dialect: string; // 方言，”無” IF NULL
  dictionaryId: string; // 詞ID
  dictionaryName: string; // 詞名稱
  chineseExplanation: string; // 當時的解釋
  content: string; // 意見內文
  reply: string; // 回覆內文
  status: string; // 處理狀況，”審查中” OR “已處理”
  creator: string; // 意見回饋者
  creationTime: string; // 回饋時間
  replyCreationTime: string; // 處理時間，NULL IF NULL
}

export interface getSysQuestionListReq {
  page: number;
  pageSize: number;
  status: number | null; // 處理狀況，可NULL，0 = 審查中、1 = 已處理、 NULL = 全搜
  orderType?: string;
  orderByColumn?: string;
}
export interface getSysQuestionListResp extends defaultItem {
  data: {
    opinionItems: sysQuestionItem[];
    orderType: string;
    orderByColumn: string;
    page: number;
    pageSize: number;
    pageTotalCount: number;
    itemTotalCount: number;
  };
}

export interface sysQuestionItem {
  id: string;
  content: string; // 內文
  reply: string; // 處理回覆
  status: string; // 狀態
  creator: string; // 回饋者
  creationTime: string; // 回饋時間
  replyCreationTime: string; // 處理時間
}

export interface addQuestionReq {
  dictionaryId: string; // 對應單字ID
  chineseExplanation: string; // 挑選的解釋，字串
  originalSentence: string; // 挑選的句子，字串
  name: string; // 發表者
  email: string; // 發表者信箱
  content: string; // 發表內文
  verifyCode: string; // 驗證碼
  sessionId: string;
}
export interface addSysQuestionReq {
  name: string; // 發表者
  email: string; // 發表者信箱
  content: string; // 發表內文
  verifyCode: string; // 驗證碼
  sessionId: string;
}

export interface getQuestionListResp extends defaultItem {
  data: {
    opinionItems: opinionItems[];
  };
}

export interface opinionItems {
  id: string; // 意見交流ID
  name: string; // 發表者名稱
  content: string; // 發表內容
  creationTime: string; // 創建時間
  loading?: boolean;
  answerReply: opinionItems;
}

export interface getAnswerListResp extends defaultItem {
  data: {
    opinionReplyItem: opinionItems;
  };
}

export interface getDictionaryWordListReq {
  page: number;
  pageSize: number;
  keyword: string; // 關鍵字，全搜 IF NULL
  tribeId: string; // 族
  dialectId: string; // 方言(語別)
}
export interface getDictionaryWordListResp extends defaultItem {
  data: {
    wordItems: {
      id: string; // 詞ID
      name: string; // 詞
    }[];
    itemTotalCount: number;
    page: number;
    pageSize: number;
    pageTotalCount: number;
  };
}

export interface getDictionaryChineseExplanationResp extends defaultItem {
  data: {
    explanationItems: {
      id: string; // 解釋ID
      chineseExplanation: string; // 解釋
    }[];
    // 項目
  };
}
export interface getDictionarySentenceResp extends defaultItem {
  data: {
    originalSentence: string[];
  };
}
