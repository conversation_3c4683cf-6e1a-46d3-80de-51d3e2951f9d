<main class="master-pages-container-layout">
    <div class="master-pages-container-cont">
        <div class="cont pages-cont-layout">
            <div class="pages-cont-list-layout">

                <!--路徑列-->
                <div class="breadcrumb-layout">
                    <div>
                        <nav class="breadcrumb-cont">
                            <ol class="breadcrumb">
                                <li class="breadcrumb-item">
                                    <a href="" (click)="$event.defaultPrevented" [routerLink]="'/home'">
                                        <span>首頁</span>
                                        <span class="material-symbols-outlined">keyboard_arrow_right</span>
                                    </a>
                                </li>
                                <li class="breadcrumb-item active">詞項回饋</li>
                            </ol>
                        </nav>
                    </div>
                    <div>
                        <nav class="breadcrumb-cont">
                            <ol class="breadcrumb">
                                <li class="breadcrumb-item">
                                    <a hre="" (click)="back($event)">
                                        <span>&lt;&lt;回上一頁</span>
                                    </a>
                                </li>
                            </ol>
                        </nav>
                    </div>
                </div>



                <div style="width: 100%;">
                    <!--內容區-->
                    <div class="row">
                        <div class="col-2 col-12 gutter-16px">
                            <div class="mb-12px w-full relative select-style">
                                <select class="select w-full" title="族語" [(ngModel)]="tribe"
                                    (change)="selectLanguage()">
                                    <!-- 預設選項 -->
                                    <option [ngValue]="null" disabled>請選族語</option>

                                    <!-- 動態渲染語言列表 -->
                                    @for (option of languageList; track option.id) {
                                    <option [ngValue]="option.id">{{ option.name }}</option>
                                    }
                                </select>

                                <!-- 下拉選單樣式裝飾 -->
                                <div class="block-select-bg absolute">
                                    <div class="row items-stretch">
                                        <div class="col">
                                            <div class="block-select-bg-rect w-full radius-card"></div>
                                        </div>
                                        <div class="col-auto shrink-0">
                                            <div class="button-dot">
                                                <span class="material-symbols-outlined">keyboard_arrow_down</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-2 col-12 gutter-16px">
                            <div class="mb-12px w-full relative select-style">
                                <select class="select w-full" title="語別" [(ngModel)]="dialect">
                                    <!-- 預設無效選項 -->
                                    <option [ngValue]="null" disabled>請選語別</option>

                                    <!-- 當列表長度大於 1 時，顯示「不拘」 -->
                                    @if (dialectList.length > 1) {
                                    <option [ngValue]="null">不拘</option>
                                    }

                                    <!-- 語別選項渲染 -->
                                    @for (option of dialectList; track option.id) {
                                    <option [ngValue]="option.id">{{ option.name }}</option>
                                    }
                                </select>

                                <!-- 下拉選單樣式裝飾 -->
                                <div class="block-select-bg absolute">
                                    <div class="row items-stretch">
                                        <div class="col">
                                            <div class="block-select-bg-rect w-full radius-card"></div>
                                        </div>
                                        <div class="col-auto shrink-0">
                                            <div class="button-dot">
                                                <span class="material-symbols-outlined">keyboard_arrow_down</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-2 col-12 gutter-16px">
                            <div class="mb-12px w-full relative select-style">
                                <select class="select w-full" title="處理狀態" [(ngModel)]="status">
                                    <!-- 預設提示選項 -->
                                    <option [ngValue]="null" disabled>請選擇處理狀態</option>

                                    <!-- 選項 -->
                                    <option [ngValue]="0">審查中</option>
                                    <option [ngValue]="1">已處理</option>
                                </select>

                                <!-- 下拉樣式裝飾 -->
                                <div class="block-select-bg absolute">
                                    <div class="row items-stretch">
                                        <div class="col">
                                            <div class="block-select-bg-rect w-full radius-card"></div>
                                        </div>
                                        <div class="col-auto shrink-0">
                                            <div class="button-dot">
                                                <span class="material-symbols-outlined">keyboard_arrow_down</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <input type="text" title="keyword" class="form-control col-12" placeholder="請輸入關鍵字"
                            [(ngModel)]="keyword"> &nbsp;&nbsp;
                        <button class="btn-list btn-primary-solid btn-search" (click)="search()">搜尋</button>
                        <button class="btn-list btn-primary-solid btn-search" style="margin-left: auto;"
                            [routerLink]="'/question/addQuestion'">新增</button>
                    </div>




                    <div class="table-box">
                        <table class="table-list-layout  table-list-style rwd-table01">
                            <tbody>
                                <tr class="th-no">
                                    @for (item of list; track $index) {
                                    <th [width]="item.width">
                                        {{item.title}}
                                    </th>
                                    }
                                </tr>
                                @if(questionList.length>0){
                                @for (item of questionList; let index= $index;track index) {
                                <tr>
                                    <td class="text-c">
                                        <span class="rwd-th">項次</span>
                                        {{index+1+(nowPage>1?(nowPage-1)*pageSize:0)}}
                                    </td>
                                    <td class="text-c">
                                        <span class="rwd-th">族語</span>
                                        {{item.tribe?item.tribe:'無'}}
                                    </td>
                                    <td class="text-c">
                                        <span class="rwd-th">詞項</span>
                                        {{item.dictionaryName?item.dictionaryName:'無'}}
                                    </td>
                                    <td class="text-l">
                                        <span class="rwd-th">回饋建議</span>
                                        {{item.content}}
                                    </td>
                                    <td class="text-c">
                                        <span class="rwd-th">處理內容</span>
                                        <!-- {{item.reply?item.reply:'-'}} -->
                                        <button class="btn-list btn-primary-solid" (click)="detail(item)">檢閱內容</button>
                                    </td>
                                    <td class="text-c">
                                        <span class="rwd-th">處理狀態</span>
                                        {{item.status}}
                                    </td>
                                    <td class="text-c">
                                        <span class="rwd-th">回饋者</span>
                                        {{item.creator}}
                                    </td>
                                </tr>
                                }
                                }@else{
                                <tr>
                                    <td colspan="7" style="text-align: center;">沒有找到符合條件的資料</td>
                                </tr>
                                }
                            </tbody>
                        </table>
                        @if(questionList.length>0){
                        <div>
                            <app-paginator [pageSize]="pageSize" [nowPage]="nowPage" [totalRecords]="totalCount"
                                [pageShowCount]="pageShowCount"
                                currentPageReportTemplate="第 {first} 到 {last} 筆，共 {totalRecords} 筆"
                                (clickPageEvent)="getPageFromPaginator($event)"
                                (pageSizeChangeEvent)="getPageSizeFromPaginator($event)"></app-paginator>
                        </div>
                        }
                    </div>
                </div>
            </div>
        </div>
    </div>
</main>