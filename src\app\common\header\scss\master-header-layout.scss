//頁首
.master-header-layout {
  margin: 0 auto;
  padding: 0;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  align-items: stretch;
  width: 100%;
  box-sizing: border-box;
  position: fixed;
  top: 0;
  z-index: 3;
  background: #fff url("../../../../assets/image/background.png") 0 0 repeat-y;
  //LOGO
  h1.logo-list {
    margin: 0;
    padding: 2px 10px;
    position: absolute;
    top: 50px;
    z-index: 3;
    a {
      padding: 5px 0;
      display: flex;
      overflow: hidden;
      align-items: center;
      justify-content: center;
      img {
        height: 100%;
        max-height: 48px;
        max-width: 250px;
        width: 100%;
      }
    }
  }
}
//頁首主內容區
.master-header-cont-layout {
  margin: 0 auto;
  padding: 0;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  align-items: flex-end;
  width: 100%;
  box-sizing: border-box;
  background-color: #ffffffbf;
  box-shadow: 2px 2px 5px 1px rgba(0, 0, 0, 0.1);
  border-bottom: 1px solid #e7e8e9;
}
//頁首主選單區
.master-header-menu-layout {
  margin: 0 auto;
  padding: 0;
  display: flex;
  flex-direction: row;
  justify-content: flex-end;
  width: 100%;
}
//族語
.language-item {
  width: 20%;
  display: flex;
  flex-direction: row;
  align-items: center;
  background-color: #4a7f42;
  padding-left: 20px;
  img {
    // width: 100%;
  }
  .language-title {
    width: 100%;
    display: flex;
    flex-direction: row;
    align-items: center;
    color: #fff;
    font-size: 1.5em;
    font-weight: bold;
    padding-left: 10px;
  }
}
.second {
  background-color: #4a7f42;
  color: #fff;
}
//右上方快速選單
.master-top-info {
  padding: 10px 30px 0 30px;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: flex-end;
  font-size: 1em;
  line-height: 1.5em;
  .mat-icon {
    padding-right: 2px;
    width: 24px;
    height: 24px;
  }
  //上方功能列-第一層
  .master-top-info-cont {
    margin: 0;
    padding: 0;
    display: flex;
    flex-direction: row;
    align-items: center;
    list-style: none;
    .master-top-info-item {
      margin: 0;
      padding: 0;
      position: relative;
      display: flex;
      flex-direction: row;
      align-items: center;
      list-style: none;
      .master-top-info-item-more {
        padding: 4px 0 0;
        width: 18px;
        height: 18px;
        display: flex;
        font-size: 1.125em;
        flex-direction: column;
        justify-content: center;
        align-items: center;
      }
      &:last-child {
        padding-left: 5px;
      }
      a {
        padding: 5px 6px;
        box-sizing: border-box;
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        align-items: center;
        list-style: none;
        text-decoration: none;
        border-radius: 16px;
        span {
          display: flex;
          flex-direction: row;
          justify-content: center;
          align-items: center;
        }
      }
      &:hover {
        > .master-top-info-dropdown-cont {
          display: block;
        }
      }
      //上方功能列-第二層
      .master-top-info-dropdown-cont {
        margin: 0;
        padding: 0;
        display: none;
        box-sizing: border-box;
        position: absolute;
        top: 100%;
        z-index: 2;
        list-style: none;
        box-shadow: 0px 2px 20px #36425029;
        border-radius: 8px;
        opacity: 1;
        width: 100%;
        min-width: 180px;
        .master-top-info-dropdown-item {
          padding: 0;
          width: 100%;
          box-sizing: border-box;
          border-bottom-style: solid;
          border-bottom-width: 1px;
          a {
            padding: 24px 18px;
          }
        }
      }
    }
  }
}
//網站導覽
.font-Change-lsit {
  margin: 0;
  padding: 0;
  display: flex;
  flex-direction: row;
  align-items: center;
  line-height: 1em;
  //標題
  .font-Change-title {
    margin: 0;
    padding: 0;
    font-size: 1em;
  }
  //內容
  .font-Change-about {
    margin: 0;
    padding: 0;
    display: flex;
    list-style: none;
    .font-Change-no {
      padding: 0 5px;
      display: flex;
      flex-direction: row;
      align-items: center;
      &:hover {
        > span,
        a.a-link {
          color: #4a7f42 !important;
        }
      }
      a {
        margin: 0;
        padding: 0 !important;
        display: flex;
        flex-direction: row;
        align-items: center;
        justify-content: center;
        text-decoration: none;
        border-radius: 16px;
        min-width: 30px;
        min-height: 30px;
        color: #364250 !important;
      }
      .a-link {
        margin: 0;
        padding: 0 5px !important;
        display: flex;
        flex-direction: row;
        align-items: center;
        justify-content: center;
        text-decoration: none;
        border-radius: 16px;
        min-width: 30px;
        min-height: 30px;
      }
    }
  }
}
//文字大中小
.font-Change-lsit {
  margin: 0;
  padding: 0;
  display: flex;
  flex-direction: row;
  align-items: center;
  line-height: 1em;
  //標題
  .font-Change-title {
    margin: 0;
    padding: 0;
    font-size: 1em;
  }
  //內容
  .font-Change-cont {
    margin: 0;
    padding: 0;
    display: flex;
    list-style: none;
    .font-Change-item {
      padding: 0 5px;
      a {
        margin: 0;
        padding: 0 !important;
        display: flex;
        flex-direction: row;
        align-items: center;
        justify-content: center;
        text-decoration: none;
        border-radius: 16px;
        min-width: 30px;
        min-height: 30px;
        //小字
        &.font_s {
          font-size: 0.8em;
        }
        //中字
        &.font_m {
          font-size: 1em;
        }
        //大字
        &.font_l {
          font-size: 1.4em;
        }
      }
      // .a-link{
      //   margin: 0;
      //   padding: 0 15px !important;
      //   display: flex;
      //   flex-direction: row;
      //   align-items: center;
      //   justify-content: center;
      //   text-decoration: none;
      //   border-radius: 16px;
      //   min-width: 30px;
      //   min-height: 30px;
      // }
    }
  }
}

//漢堡選單
.burger-menu-list-icon {
  width: 62px;
  height: 62px;
  cursor: pointer;
  display: none;
  padding: 24px;
  box-sizing: border-box;
  position: fixed;
  right: 0;
  top: 0;
  z-index: 2;
  .burger-menu-icon {
    background: #222;
    content: "";
    display: block;
    height: 1px;
    position: absolute;
    transition:
      background ease 0.3s,
      top ease 0.3s 0.3s,
      transform ease 0.3s;
    width: 20px;
    left: 18px;
    top: 27px;
    &::before,
    &::after {
      background: #222;
      content: "";
      display: block;
      height: 1px;
      position: absolute;
      transition:
        background ease 0.3s,
        top ease 0.3s 0.3s,
        transform ease 0.3s;
      width: 20px;
    }
    &::before {
      top: -6px;
    }
    &::after {
      top: 6px;
    }
  }
}
//選單變叉叉
#burger-menu {
  display: none;
  &:checked {
    + {
      .burger-menu-list-icon {
        .burger-menu-icon {
          background: transparent;
          &::before {
            transform: rotate(45deg);
            top: 0;
            transition:
              top ease 0.3s,
              transform ease 0.3s 0.3s;
          }
          &::after {
            transform: rotate(-45deg);
            top: 0;
            transition:
              top ease 0.3s,
              transform ease 0.3s 0.3s;
          }
        }
      }
    }
  }
}

@media (max-width: 1160px) {
  //頁首
  .master-header-layout {
    position: fixed;
    //LOGO
    h1.logo-list {
      position: initial;
      bottom: 0;
      width: calc(100% - 87px);
      a {
        justify-content: flex-start;
      }
    }
  }
  .master-header-cont-layout {
    display: none;
    .master-header-cont {
      .master-header-item {
        display: flex;
        flex-direction: column-reverse;
        justify-content: flex-end;
        flex-wrap: nowrap;
      }
    }
    .master-header-menu-layout {
      display: flex;
      flex-direction: column;
      .master-menu-layout {
        padding: 0;
        display: flex;
        flex-direction: column;
      }
    }
  }

  .burger-menu-list-icon {
    display: block;
  }
  #burger-menu:checked ~ .master-header-cont-layout {
    display: block;
    width: 100%;
    height: calc(100vh - 53px);
    background: #fff;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    margin-top: 53px;
    position: fixed;
    top: 0;
    right: 0;
    z-index: 1;
    transition: all 0.6s;
    overflow: auto;
    .master-top-info-cont,
    .master-menu-layout .master-menu-cont {
      display: flex;
      flex-direction: column;
      width: 100%;
      .master-menu-cont-item {
        flex-direction: column;
        border-bottom: 1px solid #e7e7e9;
        // min-width: 1160px;
        width: 100%;
        // .master-menu-drawer-item{
        //   width: 100%;
        // }
        &:hover {
          > a {
            border-bottom-color: #fff;
          }
        }
        .master-dropdown-menu-layout {
          position: static;
          box-shadow: none;
          // min-width: 1160px;
          width: 100%;
          display: flex;
          justify-content: center;
          background-color: #d8eed4;
          .master-dropdown-menu-cont {
            width: 100%;
            .master-dropdown-menu-item {
              border-bottom: 1px solid #b0c2ad;
              // min-width: 1160px;
            }
          }
        }
      }
    }
    .master-top-info-cont,
    .master-menu-layout2 .master-menu-cont {
      display: flex;
      flex-direction: column;
      width: 100%;
      .master-menu-cont-item {
        flex-direction: column;
        width: 100%;
        .master-dropdown-menu-layout {
          position: static;
          box-shadow: none;
          // min-width: 1160px;
          width: 100%;
          display: flex;
          justify-content: center;
          background-color: #232127;
          .master-dropdown-menu-cont {
            width: 100%;
            .master-dropdown-menu-item {
              color: #fff;
              border-bottom: 1px solid #4c4c4c;
              // min-width: 1160px;
            }
          }
        }
      }
    }
    .master-header-menu-layout {
      display: flex;
      flex-direction: column;
      .language-item {
        width: 100%;
        width: 100%;
        display: flex;
        justify-content: center;
        padding: 0;
        .language-title {
          width: 100px;
        }
      }
      .master-menu-layout2 .master-menu-cont {
        display: flex;
        flex-direction: column;
        padding: 0;
        .master-dropdown-menu-item {
          background-color: black;
        }
      }
    }
  }

  //右上方快速選單
  .master-top-info {
    //上方功能列-第一層
    .master-top-info-cont {
      .master-top-info-item {
        width: 100%;
        flex-direction: column;
        border-bottom-style: solid;
        border-bottom-width: 1px;
        &:last-child {
          padding-left: 0;
        }
        a {
          padding: 24px 18px;
          border-radius: 0;
          width: 100%;
        }

        //上方功能列-第二層
        .master-top-info-dropdown-cont {
          display: block;
          position: inherit;
          top: inherit;
          left: inherit;
          z-index: 1;
          box-shadow: none;
          border-radius: 0;
          min-width: 100%;
          .master-top-info-dropdown-item {
            width: 100%;
          }
        }
      }
    }
  }
  //文字大中小
  .font-Change-lsit {
    // padding: 24px 18px;
    display: flex;
    flex-direction: column;
    //內容
    .font-Change-cont {
      padding: 5px 0;
      .font-Change-item {
        a {
          border-radius: 16px;
        }
      }
    }
  }
}
@media (max-width: 420px) {
  //網站導覽
  .font-Change-lsit {
    .font-Change-about {
      display: flex;
      flex-direction: column;
      align-items: flex-start;
    }
  }
}

.master-menu-layout2 {
  .master-menu-cont {
    background-color: #4a7f42;
  }
}
