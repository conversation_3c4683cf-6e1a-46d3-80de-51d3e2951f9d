import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { subscribeReq, subscribeResp } from '../../interface/subscribe.interface';

@Injectable({
  providedIn: 'root',
})
export class SubscribeService {
  constructor(private httpClient: HttpClient) {}

  subscribe(req: subscribeReq):Observable<subscribeResp> {
    return this.httpClient.post<subscribeResp>('api/app/subscription/send-subscription', req);
  }

  unSubscribe(req: subscribeReq):Observable<subscribeResp> {
    return this.httpClient.post<subscribeResp>('api/app/subscription/send-subscription', req);
  }
}
