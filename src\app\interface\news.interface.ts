import { defaultItem } from './share.interface';

export interface getNewsResp extends defaultItem {
  data: {
    newsItems: { id: string; title: string; date: string }[];
  };
}

export interface getNewsListReq {
  page: number;
  pageSize: number;
  keyword: string; // 關鍵字
}

export interface getNewsListResp extends defaultItem {
  data: {
    newsItems: { id: string; title: string; date: string; creator: string }[];
    page: number;
    pageSize: number;
    pageTotalCount: number;
    itemTotalCount: number;
  };
}

export interface getNewsDetailResp extends defaultItem {
  data: {
    id: string;
    title: string; // 標題
    content: string; // 內文
    date: string; // 日期
    creator:string
  };
}
