import { Component, Inject, OnDestroy } from '@angular/core';
import { MAT_DIALOG_DATA, MatDialogRef, MatDialogTitle, MatDialogContent } from '@angular/material/dialog';
import { ConfirmService } from '../../service/utils/confirm.service';
import { MatIcon } from '@angular/material/icon';
import { CdkScrollable } from '@angular/cdk/scrolling';

@Component({
    selector: 'app-recording-dialog',
    templateUrl: './recording-dialog.component.html',
    styleUrl: './recording-dialog.component.scss',
    imports: [
        MatDialogTitle,
        MatIcon,
        CdkScrollable,
        MatDialogContent,
    ],
})
export class RecordingDialogComponent implements OnDestroy {
  recordingStatus: boolean = true;
  timeString: string = '00:00:00';
  private timer: any;
  private mediaRecorder!: MediaRecorder;
  private audioChunks: Blob[] = [];
  constructor(
    @Inject(MAT_DIALOG_DATA)
    public data: {
      title: string;
    },
    private dialogRef: MatDialogRef<RecordingDialogComponent>,
    private confirmService: ConfirmService
  ) {}

  ngOnDestroy(): void {
    this.stopTimer();
  }

  async start() {
    const devices = await navigator.mediaDevices.enumerateDevices();
    const audioInput = devices.find((device) => device.kind === 'audioinput');

    if (!audioInput) {
      this.confirmService.showWARN('未檢測到麥克風設備');
      return;
    }

    this.recordingStatus = !this.recordingStatus;
    this.startTimer();
    this.startRecording();
  }

  async startRecording() {
    try {
      const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
      this.mediaRecorder = new MediaRecorder(stream);

      this.mediaRecorder.ondataavailable = (event) => {
        if (event.data.size > 0) {
          this.audioChunks.push(event.data);
        }
      };

      this.mediaRecorder.onstop = () => {
        const audioBlob = new Blob(this.audioChunks, { type: 'audio/mpeg' });
        const file = new File([audioBlob], 'recording.mp3', {
          type: 'audio/mpeg',
        });
        // this.downloadAudio(file);
        this.dialogRef.close({ file: file });
      };
      this.mediaRecorder.start();
      console.log(this.mediaRecorder.state); // 應該是 "recording"
    } catch (err) {
      console.error(err);
    }
  }

  startTimer() {
    let [hours, minutes, seconds] = this.timeString.split(':').map(Number);

    this.timer = setInterval(() => {
      seconds++;
      if (seconds === 60) {
        seconds = 0;
        minutes++;
      }
      if (minutes === 60) {
        minutes = 0;
        hours++;
      }

      this.timeString =
        `${String(hours).padStart(2, '0')}:` +
        `${String(minutes).padStart(2, '0')}:` +
        `${String(seconds).padStart(2, '0')}`;
      if (hours === 0 && minutes === 10 && seconds === 1) {
        this.stop();
        this.confirmService.showWARN('已達到錄音時數上限(10分鐘)').afterClosed()
          .subscribe;
      }
    }, 1000);
  }

  stopTimer() {
    clearInterval(this.timer);
  }

  stop() {
    if (this.mediaRecorder && this.mediaRecorder.state !== 'inactive') {
      this.mediaRecorder.stop();
    }
    this.stopTimer();
  }

  close() {
    this.stopTimer();
    this.dialogRef.close();
  }

  private downloadAudio(audioFile: File) {
    const url = URL.createObjectURL(audioFile);
    const a = document.createElement('a');
    a.href = url;
    a.download = audioFile.name;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  }
}
