import { Injectable } from '@angular/core';
import { Title } from '@angular/platform-browser';
import { BehaviorSubject } from 'rxjs';

@Injectable({
  providedIn: 'root',
})
export class UtilsService {
  constructor(private titleService: Title) {}

  setTitle(title: string) {
    this.titleService.setTitle('原住民族語言線上辭典-' + title);
  }

  private fontSizeSubject = new BehaviorSubject<number>(this.loadFontSize());
  fontSize$ = this.fontSizeSubject.asObservable();

  private loadFontSize(): number {
    return sessionStorage.getItem('fontSize')
      ? parseInt(sessionStorage.getItem('fontSize')!)
      : 1;
  }

  setFontSize(size: number) {
    const validSize = size === 0 ? 1 : size;
    sessionStorage.setItem('fontSize', validSize.toString());
    this.fontSizeSubject.next(validSize);
  }
}
