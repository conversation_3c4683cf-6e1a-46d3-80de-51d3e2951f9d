.mat-mdc-dialog-title {
    display: flex;
    align-items: self-end;
    margin: 0;
}

.input-group2 {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-top: 15px;

    .want-contribute-item2 {
        font-size: 1.5em;
        font-weight: bold;
        margin: 10px 0;
    }

    .want-contribute-time {
        font-size: 1em;
        color: #949494;
    }
}

.btn-group {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin: 20px 0;
}

.btn-list3 {
    // margin: 10px 0 10px 10px;
    padding: 14px 30px;
    border-radius: 5px;
    -moz-user-select: none;
    background-image: none;
    border: 1px solid transparent;
    border-radius: 5px;
    cursor: pointer;
    display: inline-block;
    font-weight: 400;
    line-height: 1.2;
    text-align: center;
    font-size: 1.125em;
    white-space: nowrap;
    display: flex;
    align-content: center;
    align-items: flex-end;

    .material-symbols-outlined {
        color: #fff;
        font-variation-settings:
            "FILL" 0,
            "wght" 400,
            "GRAD" 0,
            "opsz" 24;
    }

    .btn_font {
        padding-left: 5px;
    }
}

.play-btn {
    color: #fff;
    background-color: #255d1c;
    border-color: #255d1c;
}

.stop-btn {
    color: #fff;
    background-color: #e41e3f;
    border-color: #e41e3f;
}
