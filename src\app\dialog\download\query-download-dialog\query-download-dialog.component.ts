import { Compo<PERSON>, Inject, <PERSON><PERSON><PERSON><PERSON>, OnInit } from '@angular/core';
import {
  FormBuilder,
  FormGroup,
  Validators,
  FormsModule,
  ReactiveFormsModule,
} from '@angular/forms';
import {
  MAT_DIALOG_DATA,
  MatDialogRef,
  MatDialogTitle,
  MatDialogContent,
  MatDialogClose,
} from '@angular/material/dialog';
import { SafeHtml } from '@angular/platform-browser';
import { QuestionService } from '../../../service/curl/question.service';
import { ProcessingBlobFilesService } from '../../../service/utils/processing-blob-files.service';
import { DownloadService } from '../../../service/curl/download.service';
import {
  downloadQueryFileReq,
  getQueryDownloadListReq,
} from '../../../interface/download.interface';
import { ConfirmService } from '../../../service/utils/confirm.service';
import { SpinnerService } from '../../../service/utils/spinner.service';
import { HttpErrorResponse, HttpResponse } from '@angular/common/http';
import { defaultItem } from '../../../interface/share.interface';
import { MatIcon } from '@angular/material/icon';
import { CdkScrollable } from '@angular/cdk/scrolling';
import { MatProgressSpinner } from '@angular/material/progress-spinner';

@Component({
  selector: 'app-query-download-dialog',
  templateUrl: './query-download-dialog.component.html',
  styleUrl: './query-download-dialog.component.scss',
  imports: [
    MatDialogTitle,
    MatIcon,
    CdkScrollable,
    MatDialogContent,
    MatProgressSpinner,
    FormsModule,
    ReactiveFormsModule,
    MatDialogClose,
  ],
})
export class QueryDownloadDialogComponent implements OnInit, OnDestroy {
  isLoading: boolean = false;
  form: FormGroup;
  img!: SafeHtml;
  audio!: string;
  sessionId: string = '';

  formatList: { id: number; name: string }[] = [
    { id: 2, name: '變體' },
    { id: 3, name: '詞類' },
    { id: 4, name: '焦點' },
    { id: 5, name: '例句' },
    { id: 6, name: '範疇分類' },
    { id: 7, name: '構詞' },
    { id: 8, name: '相關焦點' },
    { id: 9, name: '備註' },
    { id: 10, name: '相關詞' },
    { id: 11, name: '圖片' },
    { id: 12, name: '書寫系統異動' },
  ];

  selectContentList: number[] = [];
  format: number = 1;
  showDerivedDetail: boolean = false;
  column: number = 1;
  showWordRoot: boolean = true;
  fontSize: number = 1;
  fontFamily: number = 1;
  fileFormat: number = 2;

  private currentMediaElement: HTMLAudioElement | null = null;
  constructor(
    @Inject(MAT_DIALOG_DATA)
    public data: {
      req: getQueryDownloadListReq;
      isAll: boolean;
      selectList: string[];
      deleteList: string[];
    },
    private dialogRef: MatDialogRef<QueryDownloadDialogComponent>,
    private formBuilder: FormBuilder,
    private questionService: QuestionService,
    private processingBlobFilesService: ProcessingBlobFilesService,
    private downloadService: DownloadService,
    private confirmService: ConfirmService,
    private spinnerService: SpinnerService
  ) {
    this.form = this.formBuilder.group({
      captcha: ['', Validators.required],
    });
  }

  ngOnInit(): void {
    this.getCaptcha();
  }
  ngOnDestroy(): void {
    this.stopMusic();
  }

  getCaptcha() {
    this.stopMusic();
    this.questionService.getCaptcha().subscribe({
      next: (resp: Blob) => {
        const reader = new FileReader();

        // 使用 FileReader 读取 Blob 数据
        reader.onload = () => {
          const data = reader.result as ArrayBuffer;

          // 转换为 Uint8Array 以便处理二进制数据
          const uint8Array = new Uint8Array(data);

          // 定义边界标记
          const boundary = new TextEncoder().encode('--file_boundary\r\n');

          // 分割多部分内容
          const parts = this.processingBlobFilesService.splitByBoundary(
            uint8Array,
            boundary
          );

          // 处理每一部分内容
          parts.forEach((part) => {
            const contentType =
              this.processingBlobFilesService.extractContentType(part);
            const contentData =
              this.processingBlobFilesService.extractContentData(part);

            if (contentType === 'image/png') {
              // 创建图片 Blob 并显示图片
              const imageBlob = new Blob([contentData], { type: 'image/png' });
              this.img =
                this.processingBlobFilesService.getSafeImageUrl(imageBlob);
            } else if (contentType === 'audio/mpeg') {
              // 创建音频 Blob 并播放音频
              const audioBlob = new Blob([contentData], { type: 'audio/mpeg' });
              this.audio = URL.createObjectURL(audioBlob);
            } else {
              // 嘗試解析 sessionId
              const sessionText = new TextDecoder().decode(contentData);
              if (
                sessionText.trim().startsWith('{') &&
                sessionText.trim().endsWith('}')
              ) {
                // 嘗試解析 JSON
                const json = JSON.parse(sessionText);
                if (json.sessionId) {
                  this.sessionId = json.sessionId;
                }
              }
            }
          });
        };

        reader.readAsArrayBuffer(resp);
      },
      error: () => {},
    });
  }

  play() {
    this.stopMusic();

    let mediaElement: HTMLAudioElement = document.createElement('audio');

    mediaElement.style.display = 'none'; // 這行讓音頻播放器隱藏
    mediaElement.setAttribute('src', this.audio); // 設置音頻源
    mediaElement.setAttribute('controls', 'true'); // 加入控制條
    document.body.appendChild(mediaElement);
    mediaElement.play();

    this.currentMediaElement = mediaElement; // 儲存當前的音樂播放器
  }

  stopMusic() {
    if (this.currentMediaElement) {
      this.currentMediaElement.pause();
      this.currentMediaElement.remove();
      this.currentMediaElement = null;
    }
  }

  /**
   * 取得checkbox
   * @param event
   * @param id
   */
  selectContent(event: Event, id: number) {
    const target = event.target as HTMLInputElement;
    let selectStatus = target.checked;
    const index = this.selectContentList.indexOf(id);
    if (selectStatus && index === -1) {
      this.selectContentList.push(id);
    } else if (!selectStatus && index !== -1) {
      this.selectContentList.splice(index, 1);
    }
  }

  selectFormat(format: number) {
    this.format = format;
    this.showDerivedDetail = false;
  }

  selectShowDerivedDetail(event: Event) {
    const target = event.target as HTMLInputElement;
    let selectStatus = target.checked;
    this.showDerivedDetail = selectStatus;
  }

  selectColumn(column: number) {
    this.column = column;
  }

  selectShowWordRoot(isShowWordRoot: boolean) {
    this.showWordRoot = isShowWordRoot;
  }
  selectFontSize(size: number) {
    this.fontSize = size;
  }

  selectFontFamily(fontFamily: number) {
    this.fontFamily = fontFamily;
  }

  selectFileFormat(fileFormat: number) {
    this.fileFormat = fileFormat;
  }

  close() {
    this.dialogRef.close();
  }

  download() {
    if (!this.form.valid) {
      this.confirmService.showWARN('驗證碼尚未填寫');
      return;
    }
    let req: downloadQueryFileReq = {
      dictionaryIds: this.data.selectList,
      fileType: this.fileFormat,
      selectedContent: this.selectContentList,
      format: this.format,
      derivedDetail: {
        showDerivedDetail: this.showDerivedDetail,
      },
      symbolSortDetail: {
        column: this.column,
        showWordRoot: this.showWordRoot,
      },
      fontSize: this.fontSize,
      fontType: this.fontFamily,
      isAll: this.data.isAll,
      deleteDictionaryIds: this.data.deleteList,
      dictionarySearchRequest: this.data.req,
      verifyCode: this.form.value.captcha,
      sessionId: this.sessionId,
    };

    this.downloadService.downloadQueryFile(req).subscribe({
      next: (resp: HttpResponse<Blob>) => {
        this.spinnerService.hide();
        const contentDisposition = resp.headers.get('content-disposition');
        const filename =
          this.downloadService.getFilenameFromContentDisposition(
            contentDisposition
          );
        const link = document.createElement('a');
        const url = window.URL.createObjectURL(resp.body as Blob);
        link.href = url;
        link.download = filename || 'downloaded_file.pdf';
        link.click();
        window.URL.revokeObjectURL(url);
        this.dialogRef.close();
      },
      error: (err: HttpErrorResponse) => {
        this.spinnerService.hide();
      },
    });
  }
}
