.title {
    font-size: 1.8em;
    font-weight: bold;
    background-color: #4a7f42;
    color: white;
    margin: 1em 0;
    padding: 0.2em;
}
span {
    font-size: 1.2em;
}

.question-group {
    display: flex;
    flex-direction: column;
    justify-content: center;
    padding: 20px 50px;
    .question-dec {
        display: flex;
        flex-direction: column;
        label {
            font-size: 1.4em;
            font-weight: bold;
        }
    }
    .question-input-box {
        margin-top: 15px;
        span {
            font-weight: 700;
            font-size: 1.13em;
            span {
                color: red;
            }
        }
        display: flex;
        flex-direction: column;
    }
}

textarea {
    resize: vertical; /* 只允許垂直調整 */
    min-height: 200px; /* 最小高度 */
    line-height: 2;
    font-size: 1.2em;
}

.captcha-box {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    mat-icon {
        cursor: pointer;
        padding: 20px 0.2em;
        font-size: 2em;
        color: green;
    }
}

.search-box {
    width: 100%;
    position: relative;
    display: inline-flex;
    margin: 10px 0 10px 0;
}
.search-box .search-a1 {
    position: absolute;
    top: 16px;
    right: 0;
    display: block;
    width: 40px;
    height: 40px;
    color: #000;
}

.search-bar {
    width: 100%;
    padding: 11px 20px;
    background-color: #fff;
    background-image: none;
    border: 1px solid #ccc;
    border-radius: 5px;
    display: inline-block;
    transition:
        border-color 0.15s ease-in-out 0s,
        box-shadow 0.15s ease-in-out 0s;
    box-sizing: border-box;
}
.search-bar:focus {
    border: 3px solid #4a7f42;
    outline: 0;
    box-shadow: 0 0 0 0.25rem #d8eed4;
}
