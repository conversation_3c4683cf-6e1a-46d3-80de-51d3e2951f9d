import { SafeUrl } from '@angular/platform-browser';
import { defaultItem } from './share.interface';
import { revisionItem } from './revisionList.interface';

export interface getDialectAndLanguageListResp extends defaultItem {
  data: {
    tribes: {
      id: string;
      name: string;
      dialectList: {
        id: string;
        name: string;
        active: boolean;
        tribeId: string;
        disabled?: boolean;
      }[];
    }[];
  };
}

export interface searchDictionaryReq {
  page: number;
  pageSize: number;
  keyword: string;
  tribeDialectId?: string; // 族ID判斷是不是跨族語搜尋(有=族內，無=跨)
  dictionaryId?: string | null;
  advanceSearch: {
    tribeId?: string[]; // 給我主要族ID(需要在跨族語模式下才有用)
    dialectId: string[]; // 給方言ID
    startSymbolId?: string | null; // 符號起始ID
    endSymbolId?: string | null; // 符號結束 ID
    categoryId?: string | null; // 範疇ID
    partOfSpeechId?: string | null;
    searchRestrict?: number;
    sources?: string[];
  };
  // orderType: string; // ASC DESC
  // orderByColumn: string; // COLUMN of response body (EX.name)
}

export interface searchDictionaryResp extends defaultItem {
  data: {
    wordItems: dictionaryItem[];
    orderType: string;
    orderByColumn: string;
    page: number;
    pageSize: number;
    pageTotalCount: number;
    itemTotalCount: number;
  };
}

export interface dictionaryItem {
  // 單字層級
  id: string;
  tribeId: string; // 族Id
  tribe: string; // 單字族
  dialect: string; // 單字方言(如果有)
  name: string; // 單字名稱
  pinyin: string; // 拼音
  variant: string; // 變體
  formationWord: string; // 構詞
  derivativeRoot: string; // 衍生字詞根(如果是衍生字)
  frequency: number; // 詞頻
  hit: number; // 點閱數
  chineseExplanation: string; // 我幫你挑解釋(解釋STRING)
  audioItems: audioItem[];
  isDerivativeRoot?: boolean;
  isImage?: boolean;
  wordItem?: wordItem;
  loading?: boolean;
  isOpenPanel?: boolean;
  revisionList?: revisionItem[];
  writingSystemList?: revisionItem[];
}

export interface audioItem {
  id: string;
  fileId: string; // 檔案名稱(用這個取檔案)
  audioClass: string; //語音類別: 可顯示在撥放按鈕旁
}

export interface searchExampleResp extends defaultItem {
  data: {
    relateSentenceItems: relateSentenceItem[];
    orderType: string;
    orderByColumn: string;
    page: number;
    pageSize: number;
    pageTotalCount: number;
    itemTotalCount: number;
  };
}
export interface relateSentenceItem {
  id: string; // 句子ID
  originalSentence: string; // 原句(已標紅底)
  chineseSentence: string; // 中文句子(已標紅底)
  word: string; // 來源單字
  loading?: boolean;
  isOpenPanel?: boolean;
}

export interface searchDictionaryDetailResp extends defaultItem {
  data: {
    isImage: boolean;
    isDerivativeRoot: boolean;
    word: wordItem;
  };
}

export interface getShareDetailResp extends defaultItem {
  data: {
    isImage: boolean;
    isDerivativeRoot: boolean;
    word: shareDetailWordItem;
  };
}

export interface shareDetailWordItem extends wordItem {
  audioItems: audioItem[];
  dialect: string;
  tribe: string;
}

export interface wordItem {
  id: string;
  tribeId: string;
  name: string;
  pinyin: string;
  variant: string;
  formationWord: string;
  derivativeRoot: string;
  isDerivativeRoot: boolean;
  frequency: number;
  hit: number;
  dictionaryNote: string; //備註
  sources: string[]; //收錄來源
  explanationItems: {
    id: string;
    chineseExplanation: string;
    englishExplanation: string;
    category: string[];
    partOfSpeech: string[];
    focus: string[];
    isImage: boolean;
    imageUrl: string[];
    sentenceItems: {
      id: string;
      originalSentence: string;
      audioItems: audioItem[];
      anaphoraSentence: {
        id: string;
        name: string;
        show?: boolean;
      }[];
      chineseSentence: string;
      englishSentence: string;
    }[];
  }[];
}

export interface getEthnicityKeyboardResp extends defaultItem {
  data: {
    symbolList: string[];
  };
}
export interface getEthnicityLanguageResp extends defaultItem {
  data: {
    items: { id: string; name: string }[];
  };
}
export interface getEthnicitySourceResp extends defaultItem {
  data: {
    symbolList: string[];
  };
}
export interface getEthnicityCategoryResp extends defaultItem {
  data: {
    items: { id: string; name: string }[];
  };
}

export interface getAdvanceSearchResp extends defaultItem {
  data: {
    symbolItems: {
      id: string;
      name: string;
    }[]; // 符號選單
    categoryItems: {
      id: string;
      name: string;
    }[]; // 範疇選單
    partOfSpeechItems: {
      id: string;
      name: string;
    }[]; // 詞類選單
    restrictSearchItems: {
      value: number;
      name: string;
    }[]; // 僅搜尋
    sourceItems: {
      id: string;
      name: string;
    }[]; // 來源CB
  };
}

export interface getAnaphoraDetailResp extends defaultItem {
  data: AnaphoraDetail;
}
export interface AnaphoraDetail {
  tribeId: string;
  id: string; // 有回指ID (跳轉用)
  name: string; // 有回指名稱
  explanationItems: [
    // 解釋層級
    {
      id: string;
      chineseExplanation: string; // 中文
      englishExplanation: string; // 英文(先不接?)
      category: string[]; // 範疇
      partOfSpeech: string[]; // 詞類
      focus: string[]; // 焦點
    }
  ];
}

export interface getWordCommentResp extends defaultItem {
  data: {
    annotations: annotationsItem[];
  };
}

export interface annotationsItem {
  name: string; // 註解的單字名稱
  explanations: string[]; // 該單字解釋
}

export interface getMarkListResp extends defaultItem {
  data: {
    items: { id: string; name: string }[];
  };
}

export interface getWordMarkResp extends defaultItem {
  data: {
    wordItems: { id: string; name: string }[];
  };
}

export interface getRootStructureResp extends defaultItem {
  data: rootStructureItem;
}
export interface rootStructureItem {
  id: string;
  tribeId: string;
  tribeName: string;
  root: string; // 詞根 (上面的那個)
  rootExplanation: string[];
  // 詞根的解釋
  derivativeItems: {
    derivative: string; // 衍伸字 (當前單字，下面的那個)
    derivativeExplanation: string[];
    tribeId: string;
    tribeName: string;
  }[];
}
