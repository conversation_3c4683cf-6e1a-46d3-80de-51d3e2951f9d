@use "./scss/text-download.scss";

.breadcrumb-layout {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
}

.breadcrumb-item {
    cursor: pointer;
}

::ng-deep {
    .mdc-tab__text-label {
        font-size: 2em;
    }
    .mdc-tab-indicator__content--underline {
        border-color: #4a7f42 !important;
        border-bottom: 4px solid;
    }
}

.disabled {
    opacity: 0.5; /* 變淡 */
    pointer-events: none; /* 禁止點擊 */
    filter: grayscale(100%); /* 變成灰階 */
}
