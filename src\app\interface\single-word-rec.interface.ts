import { defaultItem } from './share.interface';

export interface postMyRecReq {
  dictionaryName: string; // 單字
  chineseExplanation: string; // 單字解釋
  tribeId: string; // 族ID *
  dialectId: string; // 方言
  dictionaryNote: string; // 備註
  name: string; // 發表者
  email: string; // 他的EMAIL
  phone: string; // 他的 電話
  verifyCode: string; // 驗證碼
  sessionId: string;
}

export interface getRecListReq {
  page: number;
  pageSize: number;
  keyword: string; // 關鍵字
  tribeId: string; // 族
  dialectId: string | null; // 語別(方言)
  status: number | null; // 0 = 未收錄、1 = 已收錄、2 = 審查中(包括初審 & 複審)， NULL = 無條件
}

export interface getRecListResp extends defaultItem {
  data: {
    recommendItems: recommendItem[];
    page: number;
    pageSize: number;
    pageTotalCount: number;
    itemTotalCount: number;
  };
}

export interface recommendItem {
  id: string; // ID
  dialect: string; // 方言
  dictionaryName: string; // 單字
  chineseExplanation: string; // 解釋
  statusNote: string; // 未收錄原因(審核備註)
  creator: string; // 發表者
  creationTime: string; // 發表時間
}
