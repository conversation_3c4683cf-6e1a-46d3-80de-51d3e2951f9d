// Scss Document

.news-layout {
	margin: 0;
	padding: 30px;
	.news-title {
		margin: 0;
		padding: 0 0 35px;
		font-size: 2.5em;
		font-weight: bold;
	}
	.news-cont {
		margin: 0;
		padding: 0;
		list-style: none;
		.news-cont-list {
			margin: 0;
			padding: 0 0 12px;
			&:last-child {
				padding: 0;
			}
			.news-cont-item {
				margin: 0;
				padding: 10px;
				display: flex;
				flex-direction: row;
				align-items: center;
				justify-content: flex-start;
				text-decoration: none;
				.news-cont-item-date {
					margin: 0 10px 0 0;
					padding: 0px 15px;
					font-size: 1.25em;
					border-radius: 60px;
				}
				.news-cont-item-text {
					font-size: 1.625em;
					width: 100%;
					max-width: 684px;
					font-weight: bold;
					overflow: hidden;
					white-space: nowrap;
					text-overflow: ellipsis;
				}
			}
		}
	}
}
@media (max-width: 905px) {
	.news-layout {
		padding: 30px;
		width: 100%;
		box-sizing: border-box;
		.news-cont-list {
			.news-cont-item {
				padding: 10px 0;
			}
		}
	}
}
