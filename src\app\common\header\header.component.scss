//頁首
@use "./scss/master-header-layout.scss";
//主選單
@use "./scss/master-menu-layout.scss";

.font-Change-item {
    cursor: pointer;
}

a.sr-only {
    padding: 0rem;
    background: #000;
    color: #fff;
    position: absolute;
    top: -2.5rem;
    left: 0;
    -webkit-transition: top 1s ease-out;
    transition: top 1s ease-out;
    z-index: 200;
}

a.sr-only:focus {
    position: absolute;
    left: 0px;
    top: 0px;
    outline-color: transparent;
    -webkit-transition: top 0.1s ease-in;
    transition: top 0.1s ease-in;
    border: #5f41a7 solid 0.4rem;
    background: #5f41a7;
    color: #ffffff;
    font-weight: 700;
}

.accesskey {
    position: absolute;
    color: black;
    pointer-events: none;
    text-decoration: none;
}
