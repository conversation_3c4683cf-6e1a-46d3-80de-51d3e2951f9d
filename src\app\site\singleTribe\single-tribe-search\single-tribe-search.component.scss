@use "./scss/word-description-layout.scss";
@use "./scss/second-search.scss";
@use "./scss/term-list.scss";

.breadcrumb-layout {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
}

.breadcrumb-item {
    cursor: pointer;
}

//進階搜尋
.advanced-search-cont {
    margin: 0;
    padding: 0;
    display: flex;
    flex-wrap: wrap; // 允許多行排列
    list-style: none;
    align-items: center;
    .advanced-search-item {
        box-sizing: border-box;
        padding: 5px 0; // 調整間距以確保上下對齊
        display: flex;
        align-items: center;
        .checkbox-list {
            padding: 0;
            display: flex;
            align-items: baseline; // 讓 checkbox 和 label 垂直對齊
        }
        .theme-count {
            color: #0a91d4;
        }
        label {
            font-size: 1.2em;
            margin-left: 5px; // 調整 checkbox 和 label 之間的間距
            // white-space: nowrap;
        }
    }
}

@media (max-width: 3000px) {
    .advanced-search-cont {
        .advanced-search-item {
            width: 50%; // 每行顯示 2 個 checkbox，留一些空白間距
        }
    }
}

@media (max-width: 1200px) {
    .advanced-search-cont {
        .advanced-search-item {
            width: 20%; // 每行顯示 2 個 checkbox，留一些空白間距
        }
    }
    .default {
        display: none;
    }
}
@media (max-width: 850px) {
    .advanced-search-cont {
        .advanced-search-item {
            width: 25%; // 每行顯示 2 個 checkbox，留一些空白間距
        }
    }
}
@media (max-width: 750px) {
    .advanced-search-cont {
        .advanced-search-item {
            width: 50%; // 每行顯示 2 個 checkbox，留一些空白間距
        }
    }
}
@media (max-width: 410px) {
    .advanced-search-cont {
        .advanced-search-item {
            width: 100%; // 每行顯示 2 個 checkbox，留一些空白間距
        }
    }
}

.panel-header {
    height: auto !important;
    background-color: white !important;
}
.panel-header:hover {
    background-color: white !important;
}
.panel-header:focus {
    background-color: white !important;
}

mat-expansion-panel {
    margin-bottom: 1em;
}

.word-description-container {
    display: flex;
    align-items: flex-start;
    max-width: 68vw;
}

.advanced-container {
    min-width: 22vw;
    margin-right: 2em;
}

@media (max-width: 1200px) {
    .word-description-container {
        display: flex;
        flex-direction: column;
        max-width: 100%;
        width: 100%;
    }
    .advanced-container {
        width: 90%;
        margin: 0 10px;
        padding: 10px;
    }
    .word-description-list-layout {
        width: 90%;
        margin: 0 10px;
        padding: 10px;
    }
}

.tag {
    width: 55px;
    padding: 8px 20px;
}
.tag2 {
    width: auto;
    padding: 8px 20px;
}
@media (max-width: 1310px) {
    .tag {
        width: 65px;
    }
}
@media (max-width: 830px) {
    .tag2 {
        width: 35px;
    }
}
@media (max-width: 680px) {
    .tag {
        width: 70px;
    }
}

.function-block-background {
    background-color: #e7e8e9; /* 灰色背景 */
    padding: 1em 0;
    .function-block {
        padding: 10px 30px;
        .function-gropu {
            display: flex;
            align-items: center;
            img {
                cursor: pointer;
                display: flex;
                align-items: center;
            }
        }
        .block {
            margin-bottom: 1em;
        }
    }
}

.spinner-wrapper-index {
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 998;
    app-spinner {
        width: 6rem;
        height: 6rem;
    }
}

.mat-spinner-color ::ng-deep circle {
    stroke: green;
}

.more-description-group {
    display: flex;
    align-items: center;
    justify-content: center;
    .more-description-tag {
        display: flex;
        cursor: pointer;
        align-items: center;
        justify-content: center;
        margin-right: 10px;
        padding: 10px 20px;
        border-radius: 100px;
        font-size: 1.5em;
        color: black;
        background-color: white;
        box-shadow:
            0 4px 6px rgba(0, 0, 0, 0.2),
            0 1px 3px rgba(0, 0, 0, 0.1); /* 添加外部陰影 */
        transition: box-shadow 0.3s ease; /* 添加過渡效果 */
    }

    .more-description-tag:hover {
        box-shadow:
            0 6px 8px rgba(0, 0, 0, 0.3),
            0 2px 4px rgba(0, 0, 0, 0.2); /* 滑鼠懸停時增加陰影效果 */
    }
}

.panel-header-group {
    width: 100%;
    margin: 10px 0;
    line-height: 2;
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    .panel-header-tag {
        margin-right: 10px;
        border-radius: 100px;
        font-size: 1em;
        color: #4a7f42;
        font-weight: bold;
        width: 150px;
    }

    .panel-header-description-tag {
        margin-right: 10px;
        padding: 8px 20px;
        border-radius: 100px;
        font-size: 1em;
        color: white;
        background-color: #4a7f42;
    }

    .panel-header-name {
        // width: 40%;
    }
    .panel-header-explanation {
        width: 60%;
        .word-description-cont-font {
            width: 80%;
            display: block;
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
        }
    }
}

.notfound-group {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    margin-top: 10vh;

    .notfound-text {
        font-size: 2em;
        font-weight: bold;
    }
}

::ng-deep {
    .mdc-tab__text-label {
        font-size: 2em;
    }
    .mdc-tab-indicator__content--underline {
        border-color: #4a7f42 !important;
        border-bottom: 6px solid;
    }
}

.advanced-item {
    display: flex;
    flex-direction: column;
    .range-group {
        display: flex;
        flex-direction: row;
        align-items: center;
        span {
            font-size: 1.6em;
        }
        select {
            width: 50%;
        }
    }
    span {
        font-size: 1.2em;
    }
    select {
        width: 100%;
    }
}

.word-description-explain {
    label {
        color: gray;
    }
}

.word-description-sentence-text {
    span {
        cursor: pointer;
    }
}

.carousel-block {
    width: 30%;
    margin: 1em;
}

.material-symbols-outlined {
    font-variation-settings:
        "FILL" 1,
        "wght" 400,
        "GRAD" 0,
        "opsz" 24;
}

.bottom-border {
    border-bottom: 3px solid; /* 底線顏色 & 粗細 */
    padding-bottom: 2px; /* 控制底線與文字的距離 */
}
.bottom-border-hidden {
    border-bottom: 3px solid transparent; /* 底線顏色 & 粗細 */
    padding-bottom: 2px; /* 控制底線與文字的距離 */
}

.search-bar {
    padding-right: 45px;
}

@media (max-width: 1600px) and (min-width: 1201px) {
    .search-group {
        flex-wrap: wrap;
    }
}

.name-box {
    display: flex;
    flex-wrap: wrap;
    max-width: 300px;
}
.search-group {
    width: 100%;
    display: flex;
    flex-direction: row;
    align-items: flex-start;
    //搜尋樣式
    .search-all {
        width: 100%;
        margin: 2px 20px 10px 0;
        display: flex;
        flex-direction: column;
        align-items: center;
        position: relative;
        .search-box {
            width: 100%;
            position: relative;
            display: inline-flex;
            flex-direction: column;
            align-items: center;
            min-width: 225px;
            // margin-right: 20px;
            // margin: 10px 20px 10px 0;
            .search-a1 {
                position: absolute;
                top: 14px;
                right: 0;
                display: block;
                width: 45px;
                height: 45px;
                color: #000;
            }
        }
        //搜尋框架
        .search-frame {
            padding: 20px 10px;
            // max-width: 900px;
            width: 97%;
            box-sizing: border-box;
            background-color: #ffffff;
            border-radius: 10px;
            box-shadow: 0px 0px 5px 2px rgba(0, 0, 0, 0.2);
            position: absolute;
            top: 100%;
        }
        .search-frame-info {
            cursor: pointer;
            font-size: 1.125em;
            // padding: 5px 10px;
            border-radius: 10px;
            width: 100%;
            &:hover,
            &:focus {
                background: #e4e6eb;
                // opacity: 0.5;
            }
            &:active,
            &.active {
                background-color: #e4e6eb;
                border-color: #e4e6eb;
            }
            .search-frame-name {
                padding-left: 10px;
            }
        }
    }
}

@media (max-width: 400px) {
    .search-group {
        flex-wrap: wrap;
        .search-all {
            margin: 0;
            .search-box {
                margin: 10px 0 0 0;
            }
        }
        .btns {
            width: 100%;
            .btn-box {
                width: 100%;
            }
        }
    }
}

@media (max-width: 640px) {
    .panel-header-group {
        align-items: flex-start !important;
        flex-direction: column !important;
    }
    .panel-header-name {
        width: 100% !important;
    }
    .panel-header-name-group {
        display: flex !important;
        flex-direction: column !important;
        align-items: flex-start !important;
    }
    .panel-header-explanation {
        width: 100% !important;
        margin: 10px 0 !important;
    }
    .panel-header-tag-group {
        display: flex !important;
        align-items: center !important;
        width: 100% !important;
        margin: 10px 0 !important;
    }
    .panel-header {
        // height: 20vh !important;
    }
    .panel-header-description-tag {
        min-width: 40px !important;
    }
    .name-box {
        max-width: 100% !important;
        width: 100% !important;
        margin: 10px 0 !important;
    }
}
mat-icon {
    cursor: pointer;
}
