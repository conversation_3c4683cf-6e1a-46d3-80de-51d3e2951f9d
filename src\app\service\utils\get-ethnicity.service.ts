import { Injectable } from '@angular/core';
import { ActivatedRoute } from '@angular/router';

@Injectable({
  providedIn: 'root',
})
export class GetEthnicityService {
  constructor(private activatedRoute: ActivatedRoute) {}

  GetEthnicityId(): string | null {
    const tribeId = this.activatedRoute.snapshot.queryParams['tribeId'];
    return tribeId || null;
  }

  GetEthnicityName(): string | null {
    const tribeName = this.activatedRoute.snapshot.queryParams['tribeName'];
    return tribeName || null;
  }
}
