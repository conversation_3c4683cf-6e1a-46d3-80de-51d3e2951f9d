import { Component, OnInit } from '@angular/core';
import { ActivatedRoute, RouterLink } from '@angular/router';
import { GetEthnicityService } from '../../../service/utils/get-ethnicity.service';
import { QuestionService } from '../../../service/curl/question.service';
import {
  addQuestionReq,
  getAnswerListResp,
  getAutoCompleteQuestionResp,
  getQuestionListResp,
  opinionItems,
} from '../../../interface/question.interface';
import { ProcessingBlobFilesService } from '../../../service/utils/processing-blob-files.service';
import { SafeHtml } from '@angular/platform-browser';
import { apiStatus } from '../../../enum/apiStatus.enum';
import { defaultItem } from '../../../interface/share.interface';
import { ConfirmService } from '../../../service/utils/confirm.service';
import { UtilsService } from '../../../service/utils/utils.service';
import {
  MatExpansionPanel,
  MatExpansionPanelHeader,
} from '@angular/material/expansion';
import { MatProgressSpinner } from '@angular/material/progress-spinner';
import { FormsModule } from '@angular/forms';
import { MatIcon } from '@angular/material/icon';
import { DatePipe } from '@angular/common';

@Component({
  selector: 'app-single-tribe-question',
  templateUrl: './single-tribe-question.component.html',
  styleUrl: './single-tribe-question.component.scss',
  imports: [
    RouterLink,
    MatExpansionPanel,
    MatExpansionPanelHeader,
    MatProgressSpinner,
    FormsModule,
    MatIcon,
    DatePipe,
  ],
})
export class SingleTribeQuestionComponent implements OnInit {
  id: string = '';
  ethnicity: string | null = '';

  tribe: string = '';
  dialect: string = '';
  chineseExplanation: string = '';
  originalSentence: string = '';
  name: string = '';
  email: string = '';
  content: string = '';
  captcha: string = '';

  questionList: opinionItems[] = [];
  dictionaryId: string = '';
  dictionaryName: string = '';
  type: boolean = false;
  sessionId: string = '';
  img!: SafeHtml;
  audio!: string;
  private currentMediaElement: HTMLAudioElement | null = null;

  constructor(
    private activatedRouteacr: ActivatedRoute,
    private processingBlobFilesService: ProcessingBlobFilesService,
    private getEthnicityService: GetEthnicityService,
    private questionService: QuestionService,
    private confirmService: ConfirmService,
    private utils: UtilsService
  ) {
    this.activatedRouteacr.queryParamMap.subscribe((queryParams) => {
      this.id = queryParams.get('id') as string;
      if (this.id) {
        this.type = true;
        this.getAutoCompleteQuestion();
      }
    });
  }

  ngOnInit(): void {
    this.getCaptcha();
    this.ethnicity = this.getEthnicityService.GetEthnicityName();
    this.utils.setTitle(`${this.ethnicity}-意見回饋`);
  }

  getAutoCompleteQuestion() {
    this.questionService.getAutoCompleteQuestion(this.id).subscribe({
      next: (resp: getAutoCompleteQuestionResp) => {
        this.tribe = resp.data.tribe;
        this.dialect = resp.data.dialect ? resp.data.dialect : '無';
        this.chineseExplanation = resp.data.chineseExplanation;
        this.originalSentence = resp.data.originalSentence;
        this.dictionaryId = resp.data.dictionaryId;
        this.dictionaryName = resp.data.dictionaryName;
        this.getQuestionList();
      },
      error: () => {},
    });
  }

  getQuestionList() {
    this.questionService.getQuestionList(this.dictionaryId).subscribe({
      next: (resp: getQuestionListResp) => {
        this.questionList = resp.data.opinionItems;
      },
      error: () => {},
    });
  }

  onPanelOpened(item: opinionItems) {
    item.loading = true;
    this.questionService.getAnswerReply(item.id).subscribe({
      next: (resp: getAnswerListResp) => {
        if (resp.status === apiStatus.SUCCESS) {
          item.loading = false;
          item.answerReply = resp.data.opinionReplyItem;
        } else {
          this.confirmService.showError(resp.message, '錯誤');
        }
      },
    });
  }

  getCaptcha() {
    this.questionService.getCaptcha().subscribe({
      next: (resp: Blob) => {
        const reader = new FileReader();

        // 使用 FileReader 读取 Blob 数据
        reader.onload = () => {
          const data = reader.result as ArrayBuffer;

          // 转换为 Uint8Array 以便处理二进制数据
          const uint8Array = new Uint8Array(data);

          // 定义边界标记
          const boundary = new TextEncoder().encode('--file_boundary\r\n');

          // 分割多部分内容
          const parts = this.processingBlobFilesService.splitByBoundary(
            uint8Array,
            boundary
          );

          // 处理每一部分内容
          parts.forEach((part) => {
            const contentType =
              this.processingBlobFilesService.extractContentType(part);
            const contentData =
              this.processingBlobFilesService.extractContentData(part);

            if (contentType === 'image/png') {
              // 创建图片 Blob 并显示图片
              const imageBlob = new Blob([contentData], { type: 'image/png' });
              this.img =
                this.processingBlobFilesService.getSafeImageUrl(imageBlob);
            } else if (contentType === 'audio/mpeg') {
              // 创建音频 Blob 并播放音频
              const audioBlob = new Blob([contentData], { type: 'audio/mpeg' });
              this.audio = URL.createObjectURL(audioBlob);
            } else {
              // 嘗試解析 sessionId
              const sessionText = new TextDecoder().decode(contentData);
              if (
                sessionText.trim().startsWith('{') &&
                sessionText.trim().endsWith('}')
              ) {
                // 嘗試解析 JSON
                const json = JSON.parse(sessionText);
                if (json.sessionId) {
                  this.sessionId = json.sessionId;
                }
              }
            }
          });
        };

        reader.readAsArrayBuffer(resp);
      },
      error: () => {},
    });
  }

  play() {
    if (this.currentMediaElement) {
      this.currentMediaElement.pause();
      this.currentMediaElement.remove();
    }

    let mediaElement: HTMLAudioElement = document.createElement('audio');

    mediaElement.style.display = 'none'; // 這行讓音頻播放器隱藏
    mediaElement.setAttribute('src', this.audio); // 設置音頻源
    mediaElement.setAttribute('controls', 'true'); // 加入控制條
    document.body.appendChild(mediaElement);
    mediaElement.play();

    this.currentMediaElement = mediaElement; // 儲存當前的音樂播放器
  }

  checkValid(): boolean {
    if (
      this.tribe &&
      this.chineseExplanation &&
      this.originalSentence &&
      this.dictionaryId &&
      this.name &&
      this.email &&
      this.content &&
      this.captcha
    ) {
      return true; // 所有屬性都有值
    }
    return false; // 至少有一個屬性為空
  }

  send() {
    if (!this.checkValid()) {
      this.confirmService.showWARN('必填欄位尚未填寫');
      return;
    }
    let req: addQuestionReq = {
      dictionaryId: this.dictionaryId,
      chineseExplanation: this.chineseExplanation,
      originalSentence: this.originalSentence,
      name: this.name,
      email: this.email,
      content: this.content,
      verifyCode: this.captcha,
      sessionId: this.sessionId,
    };
    this.questionService.addQuestion(req).subscribe({
      next: (resp: defaultItem) => {
        if (resp.status === apiStatus.SUCCESS) {
          this.confirmService
            .showSUCCESS('您的意見回覆將收錄在上方意見回饋頁面，您可進行查看')
            .afterClosed()
            .subscribe(() => {
              location.reload();
            });
        } else {
          this.confirmService.showError(resp.message, '錯誤');
        }
      },
    });
  }

  back(event: Event) {
    event.preventDefault();
    history.back();
  }
}
