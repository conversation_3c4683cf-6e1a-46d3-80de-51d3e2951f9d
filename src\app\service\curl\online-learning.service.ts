import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable, retry } from 'rxjs';
import {
  getLearningDataReq,
  getLearningDataResp,
  getOnlineTestLinkResp,
  getQuestionResp,
  getSearchOptionResp,
  sendAnswerResp,
} from '../../interface/onlineLearning.interface';

@Injectable({
  providedIn: 'root',
})
export class OnlineLearningService {
  constructor(private httpClient: HttpClient) {}

  /**
   * 取得線上學習搜尋條件
   * @param tribeId
   * @returns
   */
  getSearchOption(tribeId: string): Observable<getSearchOptionResp> {
    return this.httpClient.post<getSearchOptionResp>(
      `api/app/dictionary-learning/get-search-option`,
      {
        tribeId: tribeId,
      }
    );
  }

  /**
   * 取得學習資料
   * @param req
   * @returns
   */
  getLearningData(req: getLearningDataReq): Observable<getLearningDataResp> {
    return this.httpClient.post<getLearningDataResp>(
      `api/app/dictionary-learning/search`,
      req
    );
  }

  /**
   * 取得線上學習牛刀小試問題
   * @param tribeId
   * @returns
   */
  getQuestion(tribeId: string): Observable<getQuestionResp> {
    return this.httpClient.post<getQuestionResp>(
      `api/app/dictionary-learning/get-quiz`,
      {
        tribeId: tribeId,
        questionCount: 3,
      }
    );
  }

  /**
   * 送出線上學習牛刀小試回答
   * @param answer
   * @returns
   */
  sendAnswer(answer: string): Observable<sendAnswerResp> {
    return this.httpClient.post<sendAnswerResp>(
      `api/app/dictionary-learning/answer`,
      {
        answer: answer,
      }
    );
  }

  /**
   * 取得線上測驗連結
   * @param tribeId
   * @returns
   */
  getOnlineTestLink(tribeId: string): Observable<getOnlineTestLinkResp> {
    return this.httpClient.post<getOnlineTestLinkResp>(
      'api/app/dictionary-learning/games',
      {
        tribeId: tribeId,
      }
    );
  }
}
