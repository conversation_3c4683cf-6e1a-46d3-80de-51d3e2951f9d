@use "@angular/material" as mat;

:root {
    @include mat.progress-spinner-overrides(
        (
            active-indicator-color: orange
        )
    );
}

.breadcrumb-layout {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
}

.breadcrumb-item {
    cursor: pointer;
}

.title {
    font-size: 1.8em;
    font-weight: bold;
    background-color: #4a7f42;
    color: white;
    margin: 1em 0;
    padding: 0.5em;
}

textarea {
    border: green solid;
    resize: vertical; /* 只允許垂直調整 */
    min-height: 400px; /* 最小高度 */
    width: 500px; /* 最小高度 */
    outline: none;
    line-height: 2;
    font-weight: bold;
    font-size: 1.5em;
}
.textarea-group {
    margin: 0.5em 1em;
}

button {
    min-width: 150px;
    font-size: 1.3em;
}

@media (max-width: 500px) {
    textarea {
        width: 100%;
    }
}
