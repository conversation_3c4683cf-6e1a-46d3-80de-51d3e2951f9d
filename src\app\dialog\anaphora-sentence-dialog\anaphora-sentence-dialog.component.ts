import { Component, Inject, OnInit } from '@angular/core';
import { MAT_DIALOG_DATA, MatDialogRef, MatDialogTitle, MatDialogContent, MatDialogActions, MatDialogClose } from '@angular/material/dialog';
import { LanguageService } from '../../service/curl/language.service';
import { ConfirmService } from '../../service/utils/confirm.service';
import { HttpErrorResponse } from '@angular/common/http';
import {
  AnaphoraDetail,
  getAnaphoraDetailResp,
} from '../../interface/language.interface';
import { environment } from '../../../environments/environment';
import { MatIcon } from '@angular/material/icon';
import { CdkScrollable } from '@angular/cdk/scrolling';
import { MatProgressSpinner } from '@angular/material/progress-spinner';

@Component({
    selector: 'app-anaphora-sentence-dialog',
    templateUrl: './anaphora-sentence-dialog.component.html',
    styleUrl: './anaphora-sentence-dialog.component.scss',
    imports: [
        MatDialogTitle,
        MatIcon,
        CdkScrollable,
        MatDialogContent,
        MatProgressSpinner,
        MatDialogActions,
        MatDialogClose,
    ],
})
export class AnaphoraSentenceDialogComponent implements OnInit {
  anaphoraDetail?: AnaphoraDetail;
  isLoading: boolean = false;
  constructor(
    @Inject(MAT_DIALOG_DATA)
    public data: {
      id: string;
    },
    private dialogRef: MatDialogRef<AnaphoraSentenceDialogComponent>,
    private languageService: LanguageService,
    private confirmService: ConfirmService
  ) {}

  ngOnInit(): void {
    this.getAnaphora(this.data.id);
  }

  getAnaphora(id: string) {
    this.isLoading = true;
    this.languageService.getAnaphoraDetail(id).subscribe({
      next: (resp: getAnaphoraDetailResp) => {
        this.isLoading = false;
        this.anaphoraDetail = resp.data;
      },
      error: (err: HttpErrorResponse) => {
        this.isLoading = false;
        this.confirmService.showError('錯誤', err.message);
      },
    });
  }
  close() {
    this.dialogRef.close();
  }

  goto(id: string | undefined, word: string | undefined) {
    window.open(
      `${environment.sitePath}/singleSearch?tribeId=${id}&keyword=${word}`,
      '_blank'
    );
  }
}
