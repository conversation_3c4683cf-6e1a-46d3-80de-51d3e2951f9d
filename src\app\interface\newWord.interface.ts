import { defaultItem } from './share.interface';

export interface getNewWordListReq {
  tribeId: string;
  year: number;
  mainCategoryId: string;
  subCategoryId: string;
  keyword: string;
  page: number;
  pageSize: number;
}

export interface getNewWordListResp extends defaultItem {
  data: {
    items: newWordItem[];
    page: number;
    pageSize: number;
    pageTotalCount: number;
    itemTotalCount: number;
  };
}

export interface newWordItem {
  id: string;
  year: number; // 年度
  mainCategory: string; // 主類別
  subCategory: string; // 次類別
  dictionaryName: string; // 族語
  chineseExplanation: string; // 中文
  audioFileId: string; // 音檔ID(現在都沒有很正常)
  tribeName: string;
}

export interface getNewWordYearListResp extends defaultItem {
  data: {
    yearItems: number[];
  };
}

export interface getNewWordDetailResp extends defaultItem {
  data: newWordDetailItem;
}

export interface newWordDetailItem {
  creationConcept: string; // 創詞概念
  originalSentence: string; // 原句
  chineseSentence: string; // 中文句子
  note: string; // 備註
  bywordNote: string[];
  // 逐詞解釋
}
