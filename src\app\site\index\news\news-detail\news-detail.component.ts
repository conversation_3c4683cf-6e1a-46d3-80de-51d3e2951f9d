import { Component, OnInit } from '@angular/core';
import { ActivatedRoute, RouterLink } from '@angular/router';
import { NewsService } from '../../../../service/curl/news.service';
import { HttpErrorResponse } from '@angular/common/http';
import { getNewsDetailResp } from '../../../../interface/news.interface';
import { DomSanitizer, SafeHtml } from '@angular/platform-browser';
import { UtilsService } from '../../../../service/utils/utils.service';
import { DatePipe } from '@angular/common';
import { FroalaViewModule } from 'angular-froala-wysiwyg';

@Component({
  selector: 'app-news-detail',
  templateUrl: './news-detail.component.html',
  styleUrl: './news-detail.component.scss',
  imports: [RouterLink, DatePipe, FroalaViewModule],
})
export class NewsDetailComponent implements OnInit {
  id: string = '';
  newsItem: {
    id: string;
    title: string;
    content: string;
    date: string;
    creator: string;
  } = {
    id: '',
    title: '',
    content: '',
    date: '',
    creator: '',
  };
  constructor(
    private activatedRoute: ActivatedRoute,
    private newsService: NewsService,
    private utils: UtilsService,
    private sanitizer: DomSanitizer
  ) {
    this.activatedRoute.queryParamMap.subscribe((queryParams) => {
      this.id = queryParams.get('id') as string;
    });
  }
  ngOnInit(): void {
    this.getNewsDetail();
  }

  getNewsDetail() {
    this.newsService.getNewsDetail(this.id).subscribe({
      next: (resp: getNewsDetailResp) => {
        this.utils.setTitle(resp.data.title);
        this.newsItem = {
          ...resp.data,
          content: resp.data.content,
        };
      },
      error: (err: HttpErrorResponse) => {},
    });
  }

  back(event: Event) {
    event.preventDefault();
    history.back();
  }
}
