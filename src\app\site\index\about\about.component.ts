import { Component, ViewEncapsulation } from '@angular/core';
import { ShareService } from '../../../service/curl/share.service';
import { getAboutResp } from '../../../interface/share.interface';
import { SafeHtml, DomSanitizer } from '@angular/platform-browser';
import { ActivatedRoute, RouterLink } from '@angular/router';
import { UtilsService } from '../../../service/utils/utils.service';
import { FroalaViewModule } from 'angular-froala-wysiwyg';

@Component({
  selector: 'app-about',
  templateUrl: './about.component.html',
  styleUrl: './about.component.scss',
  imports: [RouterLink, FroalaViewModule],
  // encapsulation: ViewEncapsulation.None,
})
export class AboutComponent {
  id: string = '';
  title: string = '';
  content: string = '';

  constructor(
    private shareService: ShareService,
    private activatedRoute: ActivatedRoute,
    private sanitizer: DomSanitizer,
    private utils: UtilsService
  ) {
    this.activatedRoute.queryParamMap.subscribe((queryParamMap) => {
      this.id = queryParamMap.get('id') as string;
      this.getAbout();
    });
  }

  getAbout() {
    this.shareService.getAbout(this.id).subscribe({
      next: (resp: getAboutResp) => {
        this.title = resp.data.title;
        this.utils.setTitle(this.title);
        this.content = resp.data.content;
      },
      error: () => {},
    });
  }

  back(event: Event) {
    event.preventDefault();
    history.back();
  }
}
