// Scss Document
.word-description-list-layout {
	margin: 0.5em 0;
	padding: 0;
	display: block;
	min-width: 62vw;
}
//標題
.word-description-layout-title {
	font-size: 1.625em;
	margin: 0.5em 0;
}
//單詞內容區
.word-description-layout {
	margin: 0;
	padding: 0;
	display: block;
	width: 100%;
}
//單詞內容區
//族語單詞
.word-description-cont-font {
	font-size: 1.5em;
	font-weight: bold;
	
}
.word-description-cont-icon {
	color: #4a7f42;
	padding-left: 10px;
}
//單詞說明
.word-description-explain-layout {
	padding: 10px 30px;
	.word-description-explain-list {
		padding: 10px 0;
		display: flex;
		flex-direction: column;
		flex-wrap: nowrap;
		align-items: flex-start;
		justify-content: space-between;
		border-bottom-style: solid;
		border-bottom-width: 1px;
		&:last-child {
			border-bottom-width: 0;
		}
		.word-description-explain-item {
			padding-right: 10px;
			width: 100%;
			//說明文
			.word-description-explain {
				display: flex;
				align-items: center;
				justify-content: space-between;
				flex-wrap: wrap;
				padding: 10px 0;
				.word-description-explain-tag {
					margin-right: 10px;
					padding: 6px 20px;
					border-radius: 100px;
					font-size: 1.125em;
				}
				.word-description-explain-text {
					font-size: 1.5em;
					font-weight: bold;
					line-height: 2;
				}
			}
			//範例
			.word-description-sentence {
				padding: 10px 0;
				display: flex;
				flex-direction: column;
				align-items: flex-start;
				justify-content: flex-start;
				.word-description-sentence-text {
					display: flex;
					flex-direction: row;
					flex-wrap: wrap;
					align-items: center;
					justify-content: flex-start;
					font-size: 1.5em;
					font-weight: bold;
					line-height: 1.6;
				}
				.word-description-sentence-translate {
					font-size: 1.125em;
					line-height: 3;
				}
			}
		}
		.word-description-explain-img {
			img {
				width: 271px;
				height: auto;
			}
		}
	}
}
