<main class="master-pages-container-layout">
    <div class="master-pages-container-cont">
        <div class="cont pages-cont-layout">
            <div class="pages-cont-list-layout">
                <!--路徑列-->
                <div class="breadcrumb-layout">
                    <div>
                        <nav class="breadcrumb-cont">
                            <ol class="breadcrumb">
                                <li class="breadcrumb-item">
                                    <a href="" (click)="$event.defaultPrevented" [routerLink]="'/home'">
                                        <span>首頁</span>
                                        <span class="material-symbols-outlined">keyboard_arrow_right</span>
                                    </a>
                                </li>
                                <li class="breadcrumb-item">
                                    <a href="/question/list">
                                        <span>詞項回饋</span>
                                        <span class="material-symbols-outlined">keyboard_arrow_right</span>
                                    </a>
                                </li>
                                <li class="breadcrumb-item">
                                    <span>新增</span>
                                </li>
                            </ol>
                        </nav>
                    </div>
                    <div>
                        <nav class="breadcrumb-cont">
                            <ol class="breadcrumb">
                                <li class="breadcrumb-item">
                                    <a hre="" (click)="back($event)">
                                        <span>&lt;&lt;回上一頁</span>
                                    </a>
                                </li>
                            </ol>
                        </nav>
                    </div>
                </div>
                <div style="width: 100%;">

                    <div class="title">意見回饋</div>
                    <div class="question-group">
                        <div class="question-dec">
                            <label>原住民族族語辭典平台本著對每一位發言人的尊重，希望網友能遵守以下規則：</label>
                            <span>1.尊重他人的發言權，並禁止利用留言板做為傳送或發表具威脅性、猥褻性、攻擊性的資料及文章。 </span>
                            <span>2.請勿張貼廣告或任何非關的商業行為。 </span>
                            <span>3.避免在公眾討論區發表非關族語交流之私人言論。 </span>
                            <span>4.原住民族族語辭典管理處有權刪除任何不當的發言。 </span>
                        </div>
                        <div class="question-input-box">
                            <span><span>*</span>族語</span>
                            <select title="tribe" class="form-control" [(ngModel)]="tribe" (change)="selectTribe()">
                                <option [ngValue]="null" disabled selected>請選擇族語</option>
                                @for (option of languageList; track option) {
                                <option [ngValue]="option.id">{{option.name}}</option>
                                }
                            </select>
                        </div>
                        <div class="question-input-box">
                            <span>語別</span>
                            <select title="dialect" [(ngModel)]="dialect" [disabled]="!tribe||dialectList.length<1"
                                (change)="selectDialect()">
                                <option [ngValue]="null" disabled selected>請選擇語別</option>
                                @if(dialectList.length>1){
                                <option [ngValue]="null" selected>不拘</option>
                                }
                                @for (option of dialectList; track option) {
                                <option [ngValue]="option.id">{{option.name}}</option>
                                }
                            </select>
                        </div>
                        <div class="question-input-box">
                            <span><span>*</span>詞項</span>
                            <div>
                                <button class="btn-list"
                                    [ngClass]="{ 'btn-default-solid': !tribe, 'btn-primary-solid': tribe }"
                                    [disabled]="!tribe" (click)="selectTerm()">選擇詞項</button>
                                <span>{{dictionaryName}}</span>
                            </div>
                        </div>
                        <div class="question-input-box">
                            <span><span>*</span>解釋</span>
                            <select title="chineseExplanationGroup" [(ngModel)]="chineseExplanationGroup" [disabled]="!dictionaryId"
                                (change)="selectChineseExplanation()">
                                <option [ngValue]="null" disabled selected>請選擇解釋</option>
                                @for (option of chineseExplanationList; track option) {
                                <option [ngValue]="option">{{option.chineseExplanation}}</option>
                                }
                            </select>
                        </div>
                        <div class="question-input-box">
                            <span><span>*</span>例句</span>
                            <select title="sentence" [(ngModel)]="sentence" [disabled]="!chineseExplanationGroup">
                                <option [ngValue]="null" disabled selected>請選擇例句</option>
                                @for (option of sentenceList; track option) {
                                <option [ngValue]="option">{{option}}</option>
                                }
                            </select>
                        </div>
                        <div class="question-input-box">
                            <span><span>*</span>回饋者</span>
                            <input class="form-control" type="text" title="name" placeholder="請輸入姓名或暱稱"
                                [(ngModel)]="name">
                        </div>
                        <div class="question-input-box">
                            <span><span>*</span>Email</span>
                            <input class="form-control" type="text" title="email" placeholder="請輸入Email"
                                [(ngModel)]="email">
                        </div>
                        <div class="question-input-box">
                            <span><span>*</span>內容</span>
                            <textarea class="form-control" placeholder="發表內容字數限制500字元" [(ngModel)]="content"
                                maxlength="500"></textarea>
                            <div style="display: flex;">
                                <span style="margin-left: auto;">
                                    <label style="color:red">{{content?content.length:0}}</label>/500</span>
                            </div>
                        </div>
                        <div class="question-input-box">
                            <span><span>*</span>驗證碼</span>
                            <div class="captcha-box">
                                <input class="form-control" type="text" title="captcha" [(ngModel)]="captcha"
                                    placeholder="請輸入驗證碼">
                                <img [src]="img" alt="captcha">
                                <mat-icon (click)="getCaptcha()">refresh</mat-icon>
                                <mat-icon (click)="play()">volume_up</mat-icon>
                            </div>
                        </div>
                        <div class="btn-group">
                            <button class="btn-list btn-primary-solid" (click)="send()">送出</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</main>