import { Component, Inject } from '@angular/core';
import {
  MAT_DIALOG_DATA,
  MatDialogRef,
  MatDialogTitle,
  MatDialogContent,
} from '@angular/material/dialog';
import { LanguageService } from '../../service/curl/language.service';
import { ConfirmService } from '../../service/utils/confirm.service';
import { QuestionService } from '../../service/curl/question.service';
import {
  getDictionaryWordListReq,
  getDictionaryWordListResp,
} from '../../interface/question.interface';
import { apiStatus } from '../../enum/apiStatus.enum';
import { MatIcon } from '@angular/material/icon';
import { CdkScrollable } from '@angular/cdk/scrolling';
import { MatProgressSpinner } from '@angular/material/progress-spinner';
import { PaginatorComponent } from '../../utils/paginator/paginator.component';
import {
  Form<PERSON>uilder,
  FormGroup,
  FormsModule,
  ReactiveFormsModule,
} from '@angular/forms';

@Component({
  selector: 'app-select-term-dialog',
  templateUrl: './select-term-dialog.component.html',
  styleUrl: './select-term-dialog.component.scss',
  imports: [
    MatDialogTitle,
    MatIcon,
    FormsModule,
    ReactiveFormsModule,
    CdkScrollable,
    MatDialogContent,
    MatProgressSpinner,
    PaginatorComponent,
  ],
})
export class SelectTermDialogComponent {
  form: FormGroup;
  isLoading: boolean = false;
  tribeId: string = '';
  dialectId: string = '';
  pageSize: number = 10; //一頁幾筆資料
  nowPage: number = 1;
  totalCount: number = 0; //總筆數
  pageShowCount: number = 5; //分頁器秀幾個
  termList: { id: string; name: string }[] = [];
  constructor(
    @Inject(MAT_DIALOG_DATA)
    public data: {
      tribeId: string;
      dialectId: string;
    },
    private dialogRef: MatDialogRef<SelectTermDialogComponent>,
    private questionService: QuestionService,
    private confirmService: ConfirmService,
    private formBuilder: FormBuilder
  ) {
    this.tribeId = data.tribeId;
    this.dialectId = data.dialectId;
    this.form = this.formBuilder.group({
      keyword: [],
    });
  }

  ngOnInit(): void {
    this.getTermList();
  }

  search() {
    this.nowPage = 1;
    this.getTermList();
  }

  /**
   * 第幾頁
   * @param item number
   */
  getPageFromPaginator(nowPage: number) {
    this.nowPage = nowPage;
    this.getTermList();
  }
  /**
   * 每頁資料數量
   * @param item number
   */
  getPageSizeFromPaginator(pageSize: number) {
    this.pageSize = pageSize;
    this.nowPage = 1;
    this.getTermList();
  }

  getTermList() {
    this.isLoading = true;
    let req: getDictionaryWordListReq = {
      page: this.nowPage,
      pageSize: this.pageSize,
      keyword: this.form.value.keyword,
      tribeId: this.tribeId,
      dialectId: this.dialectId,
    };

    this.questionService.getDictionaryWordList(req).subscribe({
      next: (resp: getDictionaryWordListResp) => {
        this.isLoading = false;
        if (resp.status === apiStatus.SUCCESS) {
          this.termList = resp.data.wordItems;
          this.totalCount = resp.data.itemTotalCount;
        } else {
          this.confirmService.showError(resp.message, '錯誤');
        }
      },
      error: () => {
        this.isLoading = false;
      },
    });
  }
  close() {
    this.dialogRef.close();
  }
  select(item: { id: string; name: string }) {
    this.dialogRef.close({ id: item.id, name: item.name });
  }
}
