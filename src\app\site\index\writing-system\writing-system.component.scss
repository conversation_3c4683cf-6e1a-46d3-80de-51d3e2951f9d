@use "./scss/word-description-layout.scss";
@use "./scss/second-search.scss";
@use "./scss/term-list.scss";
@use "./scss/query-download.scss";

.breadcrumb-layout {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
}

.breadcrumb-item {
    cursor: pointer;
}

//進階搜尋
.advanced-search-title-group {
    display: flex;
    justify-content: space-between;
    align-items: center;
    mat-icon {
        width: 30px;
        font-size: 2em;
        cursor: pointer;
    }
}

.advanced-search-title {
    margin: 10px 0 0 10px;
    padding: 5px 5px;
    font-size: 1.6em;
    text-align: left;
    font-weight: bold;
    border-left: #4a7f42 10px solid;
    span {
        color: red;
    }
}
.advanced-search-cont-layout {
    padding: 10px 10px;
    .advanced-search-cont {
        margin: 0;
        padding: 0;
        display: flex;
        flex-wrap: wrap; // 允許多行排列
        list-style: none;
        align-items: center;
        .advanced-search-item {
            box-sizing: border-box;
            padding: 5px 0; // 調整間距以確保上下對齊
            display: flex;
            align-items: center;
            .checkbox-list {
                padding: 0;
                display: flex;
                align-items: baseline; // 讓 checkbox 和 label 垂直對齊
            }
            .theme-count {
                color: #0a91d4;
            }
            label {
                font-size: 1em;
                margin-left: 5px; // 調整 checkbox 和 label 之間的間距
                // white-space: nowrap;
            }
        }
    }
}

@media (max-width: 3000px) {
    .advanced-search-cont-layout {
        .advanced-search-cont {
            .advanced-search-item {
                width: 50%; // 每行顯示 2 個 checkbox，留一些空白間距
            }
        }
    }
}

@media (max-width: 1200px) {
    .advanced-search-cont-layout {
        .advanced-search-cont {
            .advanced-search-item {
                width: 20%; // 每行顯示 2 個 checkbox，留一些空白間距
            }
        }
    }
}
@media (max-width: 850px) {
    .advanced-search-cont-layout {
        .advanced-search-cont {
            .advanced-search-item {
                width: 25%; // 每行顯示 2 個 checkbox，留一些空白間距
            }
        }
    }
}

@media (max-width: 750px) {
    .advanced-search-cont-layout {
        .advanced-search-cont {
            .advanced-search-item {
                width: 50%; // 每行顯示 2 個 checkbox，留一些空白間距
            }
        }
    }
}
@media (max-width: 410px) {
    .advanced-search-cont-layout {
        .advanced-search-cont {
            .advanced-search-item {
                width: 100%; // 每行顯示 2 個 checkbox，留一些空白間距
            }
        }
    }
}

.panel-header {
    height: auto !important;
    background-color: white !important;
}
.panel-header:hover {
    background-color: white !important;
}
.panel-header:focus {
    background-color: white !important;
}
:host ::ng-deep {
    #revision {
        .panel-header {
            height: 10vh !important;
            background-color: #4a7f42 !important;
        }
        .panel-header:hover {
            background-color: #4a7f42 !important;
        }
        .panel-header:focus {
            background-color: #4a7f42 !important;
        }

        .mat-expansion-indicator {
            svg {
                fill: white;
            }
        }
    }
}

mat-expansion-panel {
    margin-bottom: 1em;
}

.word-description-container {
    display: flex;
    align-items: flex-start;
    max-width: 68vw;
}

.advanced-container {
    min-width: 22vw;
    margin-right: 2em;
}

@media (max-width: 1200px) {
    .word-description-container {
        display: flex;
        flex-direction: column;
        max-width: 100%;
        width: 100%;
    }
    .advanced-container {
        width: 90%;
        margin: 0 10px;
        padding: 10px;
    }
    .word-description-list-layout {
        width: 90%;
        margin: 0 10px;
        padding: 10px;
    }
}

.tag {
    width: 60px;
    padding: 8px 20px;
}

@media (max-width: 1310px) {
    .tag {
        width: 70px;
    }
}
@media (max-width: 680px) {
    .tag {
        width: 80px;
    }
}

.function-block-background {
    background-color: #e7e8e9; /* 灰色背景 */
    padding: 1em 0;
    .function-block {
        padding: 10px 30px;
        .function-gropu {
            display: flex;
            align-items: center;
            img {
                cursor: pointer;
                display: flex;
                align-items: center;
            }
        }
        .block {
            margin-bottom: 1em;
        }
    }
}

.spinner-wrapper-index {
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 998;

    app-spinner {
        width: 6rem;
        height: 6rem;
    }
}

.more-description-group {
    display: flex;
    align-items: center;
    justify-content: center;
    .more-description-tag {
        display: flex;
        cursor: pointer;
        align-items: center;
        justify-content: center;
        margin-right: 10px;
        padding: 10px 20px;
        border-radius: 100px;
        font-size: 1.5em;
        color: black;
        background-color: white;
        box-shadow:
            0 4px 6px rgba(0, 0, 0, 0.2),
            0 1px 3px rgba(0, 0, 0, 0.1); /* 添加外部陰影 */
        transition: box-shadow 0.3s ease; /* 添加過渡效果 */
    }

    .more-description-tag:hover {
        box-shadow:
            0 6px 8px rgba(0, 0, 0, 0.3),
            0 2px 4px rgba(0, 0, 0, 0.2); /* 滑鼠懸停時增加陰影效果 */
    }
}

.panel-header-group {
    width: 100%;
    display: flex;
    margin: 10px 0;
    line-height: 2;
    align-items: center;
    flex-wrap: wrap;
    .panel-header-tag {
        margin-right: 10px;
        font-size: 1.125em;
        color: #4a7f42;
        font-weight: bold;
        min-width: 30%;
        width: 150px;
    }

    .panel-header-description-tag {
        margin-right: 10px;
        padding: 8px 20px;
        border-radius: 100px;
        font-size: 1.125em;
        color: white;
        background-color: #4a7f42;
    }

    .panel-header-name {
    }
    .panel-header-explanation {
        width: 50%;
        .word-description-cont-font {
            width: 80%;
            display: block;
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
        }
    }
}

.notfound-group {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-top: 10vh;

    .notfound-text {
        font-size: 2em;
        font-weight: bold;
    }
}

::ng-deep {
    .mdc-tab__text-label {
        font-size: 2em;
    }
    .mdc-tab-indicator__content--underline {
        border-color: #4a7f42 !important;
        border-bottom: 6px solid;
    }
}
.word-description-explain {
    label {
        color: gray;
    }
}

.word-description-sentence-text {
    span {
        cursor: pointer;
    }
}

.carousel-block {
    width: 30%;
    margin: 1em;
}

mat-icon {
    cursor: pointer;
}

.material-symbols-outlined {
    font-variation-settings:
        "FILL" 1,
        "wght" 400,
        "GRAD" 0,
        "opsz" 24;
}

.description-explain {
    display: flex;
    flex-direction: column;
    padding: 1em;
    width: 100%;

    .description-item {
        display: flex;
        flex-wrap: wrap; //  允許換行
        justify-content: space-between;
        align-items: flex-start; //  對齊上方比較穩定
        border-bottom: 1px solid;
        &:last-child {
            border-bottom-width: 0;
        }

        .description-text,
        .description-note,
        .description-time,
        .description-title {
            flex: 1 1 30%;
            box-sizing: border-box;
            word-wrap: break-word;
            overflow-wrap: break-word;
            line-height: 2;
        }

        .description-text {
            font-size: 1.5em;
            font-weight: bold;
        }

        .description-note {
            margin: 0 1em;
            font-size: 1.125em;
            color: #4a7f42;
        }

        .description-time {
            display: flex;
            align-items: flex-end;
            font-size: 1em;
            color: gray;
        }

        .description-title {
            display: flex;
            align-items: flex-end;
            font-size: 1em;
        }

        @media (max-width: 768px) {
            flex-direction: column;
            .description-text,
            .description-note,
            .description-time,
            .description-title {
                width: 100%;
                margin: 0.3em 0;
            }
        }
    }
}
.description-explain {
    display: flex;
    flex-direction: column;
    padding: 1em;
    width: 100%;

    .description-item {
        display: flex;
        flex-wrap: wrap; //  允許換行
        justify-content: space-between;
        align-items: flex-start; //  對齊上方比較穩定
        padding: 10px 30px;
        border-bottom: 1px solid;

        &:last-child {
            border-bottom-width: 0;
        }

        .description-text,
        .description-note,
        .description-time,
        .description-title {
            flex: 1 1 30%;
            box-sizing: border-box;
            word-wrap: break-word;
            overflow-wrap: break-word;
            line-height: 2;
        }

        .description-text {
            font-size: 1.5em;
            font-weight: bold;
        }

        .description-note {
            margin: 0 1em;
            font-size: 1.125em;
            color: #4a7f42;
        }

        .description-time {
            display: flex;
            align-items: flex-end;
            font-size: 1em;
            color: gray;
        }

        .description-title {
            display: flex;
            align-items: flex-end;
            font-size: 1em;
        }

        @media (max-width: 768px) {
            flex-direction: column;
            .description-text,
            .description-note,
            .description-time,
            .description-title {
                width: 100%;
                margin: 0.3em 0;
            }
        }
    }
}

.description-notfound {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 10px 30px;
    border-bottom-style: solid;
    border-bottom-width: 1px;
}

.bottom-border {
    border-bottom: 3px solid; /* 底線顏色 & 粗細 */
    padding-bottom: 2px; /* 控制底線與文字的距離 */
}
.bottom-border-hidden {
    border-bottom: 3px solid transparent; /* 底線顏色 & 粗細 */
    padding-bottom: 2px; /* 控制底線與文字的距離 */
}

.search-bar {
    padding-right: 45px;
}

@media (max-width: 1600px) and (min-width: 1201px) {
    .search-group {
        flex-wrap: wrap;
    }
}

.name-box {
    display: flex;
    flex-wrap: wrap;
    max-width: 300px;
}
.search-group {
    width: 100%;
    display: flex;
    flex-direction: row;
    align-items: flex-start;
    //搜尋樣式
    .search-all {
        width: 100%;
        margin: 2px 20px 10px 0;
        display: flex;
        flex-direction: column;
        align-items: center;
        position: relative;
        .search-box {
            width: 100%;
            position: relative;
            display: inline-flex;
            flex-direction: column;
            align-items: center;
            min-width: 225px;
            // margin-right: 20px;
            // margin: 10px 20px 10px 0;
            .search-a1 {
                position: absolute;
                top: 14px;
                right: 0;
                display: block;
                width: 45px;
                height: 45px;
                color: #000;
            }
        }
        //搜尋框架
        .search-frame {
            padding: 20px 10px;
            // max-width: 900px;
            width: 97%;
            box-sizing: border-box;
            background-color: #ffffff;
            border-radius: 10px;
            box-shadow: 0px 0px 5px 2px rgba(0, 0, 0, 0.2);
            position: absolute;
            top: 100%;
        }
        .search-frame-info {
            cursor: pointer;
            font-size: 1.125em;
            // padding: 5px 10px;
            border-radius: 10px;
            width: 100%;
            &:hover,
            &:focus {
                background: #e4e6eb;
                // opacity: 0.5;
            }
            &:active,
            &.active {
                background-color: #e4e6eb;
                border-color: #e4e6eb;
            }
            .search-frame-name {
                padding-left: 10px;
            }
        }
    }
}

@media (max-width: 400px) {
    .search-group {
        flex-wrap: wrap;
        .search-all {
            margin: 0;
            .search-box {
                margin: 10px 0 0 0;
            }
        }
        .btns {
            width: 100%;
            .btn-box {
                width: 100%;
            }
        }
    }
}

@media (max-width: 640px) {
    .panel-header-group {
        align-items: flex-start !important;
        flex-direction: column !important;
    }
    .panel-header-name {
        width: 100% !important;
    }
    .panel-header-name-group {
        display: flex !important;
        flex-direction: column !important;
        align-items: flex-start !important;
    }
    .panel-header-explanation {
        width: 100% !important;
        margin: 10px 0 !important;
    }
    .panel-header-tag-group {
        display: flex !important;
        align-items: center !important;
        width: 100% !important;
        margin: 10px 0 !important;
    }
    .panel-header {
        // height: 20vh !important;
    }
    .panel-header-description-tag {
        min-width: 40px !important;
    }
    .name-box {
        max-width: 100% !important;
        width: 100% !important;
        margin: 10px 0 !important;
    }
}

.font-w {
    font-weight: bold;
}

.web-box {
    display: block;
}
.phone-box {
    display: none;
}
@media (max-width: 768px) {
    .web-box {
        display: none;
    }
    .phone-box {
        display: block;
    }
}
