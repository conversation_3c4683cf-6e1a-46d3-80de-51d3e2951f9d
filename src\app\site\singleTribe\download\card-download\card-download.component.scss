@use "./scss/card-download.scss";
@use "../../../../../../public/scss/selectHama";

.breadcrumb-layout {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
}

.breadcrumb-item {
    cursor: pointer;
}

::ng-deep {
    .mdc-tab__text-label {
        font-size: 2em;
    }
    .mdc-tab-indicator__content--underline {
        border-color: #4a7f42 !important;
        border-bottom: 4px solid;
    }
}

img{
    width: 250px;
    height: 150px;
    object-fit: cover;
    
}



.form-control {
    border-top-width: 1px !important;
    margin-top: 0px !important;
}

.btn-list {
    border-top-width: 0px !important;
    margin-top: 0px !important;
}