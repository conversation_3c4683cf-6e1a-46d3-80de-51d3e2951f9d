import { defaultItem } from './share.interface';

export interface getSubmissionListReq {
  tribeId: string; // 族ID
  keyword: string; // 關鍵字
  status: number | null; // 狀態
  dialectId: string | null; // 語別ID
  page: number;
  pageSize: number;
}
export interface getSubmissionListResp extends defaultItem {
  data: {
    creationItems: creationItem[];
    page: number;
    pageSize: number;
    pageTotalCount: number;
    itemTotalCount: number;
  };
}

export interface creationItem {
  id: string;
  dialect: string; // 語別
  dictionaryName: string; // 族語創詞
  chineseExplanation: string; // 華語創詞
  creator: string; // 投稿者
  status: string; // 狀態
  like: string; // 讚數
  checkTime: string; // 處理日
}

export interface getWantToKnowListResp extends defaultItem {
  data: {
    wantToKnowItems: string[]; // 我想知道的列表;
  };
}

export interface sendWantToKnowReq {
  tribeId: string;
  wantToKnowName: string; // 我想知道內容
  name?: string; // 發表者
  email?: string; // 發表者信箱
}

export interface getWantContributeCategoryListResp extends defaultItem {
  data: {
    mainCategoryList: mainCategoryList[];
  };
}

export interface mainCategoryList {
  id: string;
  name: string;
  subCategoryList: subCategoryList[];
}

export interface subCategoryList {
  id: string;
  name: string;
  mainCategoriesId: string; // 主類別ID(前端功能需求)
}

export interface getWantContributeMethodListResp extends defaultItem {
  data: {
    methodItems: {
      id: string;
      name: string;
      tip: string;
    }[];
  };
}

export interface addWantContributeReq {
  dictionaryName: string; // 族語創詞
  chineseExplanation: string; // 華語創詞
  audioFile: Blob; // 錄音 上傳 (MP3 only)
  categoryId: string; // 主類別
  subCategoryId: string; // 次類別
  tribeId: string; // 族ID
  dialectId: string; // 語別ID
  creationMethodId: string[]; // 創詞方法 ID
  creationMethodOther: string; // 創詞方法其他說明欄
  creationByWordList: string[]; // 逐詞註解(創詞說明)
  email: string; // 信箱
  name: string; // 投稿者名稱
  dictionaryNote: string; // 詞備註
  creationConcept: string; // 語意
  originalSentence: string; // 原文例句
  chineseSentence: string; // 中文例句
  verifyCode: string; // 驗證碼
  sessionId: string; // sessionID
}
