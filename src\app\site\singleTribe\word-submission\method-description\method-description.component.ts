import { Component, Input } from '@angular/core';
import { MatTabGroup } from '@angular/material/tabs';

@Component({
    selector: 'app-method-description',
    templateUrl: './method-description.component.html',
    styleUrl: './method-description.component.scss',
})
export class MethodDescriptionComponent {
  @Input() tabGroup!: MatTabGroup;

  goLink(index: number) {
    this.tabGroup.selectedIndex = index;
  }
}
