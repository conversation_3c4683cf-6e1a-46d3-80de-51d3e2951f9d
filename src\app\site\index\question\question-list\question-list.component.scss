@use "../../../../../../public/scss/selectHama";

.breadcrumb-layout {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
}

.breadcrumb-item {
    cursor: pointer;
}

.search-group {
    display: flex;
    align-items: center;

    select {
        margin: 0 0.5em;
    }
}

// select,
// input {
//     min-width: 180px;
//     max-width: 180px;
// }

// input {
//     margin: 0 0.5em;
// }




.form-control {
    border-top-width: 1px !important;
    margin-top: 0px !important;
}

.row {
    display: flex;
    flex-wrap: wrap;
}

.btn-search {

    margin-top: 0px !important;
    margin-bottom: 10px !important;

}

@media (min-width: 992px) {

    .col-2 {
        width: 20% !important;
        position: relative;
        max-width: 20%;
        flex: 0 0 20%;
    }
}