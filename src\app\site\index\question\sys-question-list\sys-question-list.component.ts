import { Component } from '@angular/core';
import { Router, RouterLink } from '@angular/router';
import { QuestionService } from '../../../../service/curl/question.service';
import {
  getSysQuestionListReq,
  getSysQuestionListResp,
  sysQuestionItem,
} from '../../../../interface/question.interface';
import { apiStatus } from '../../../../enum/apiStatus.enum';
import { ConfirmService } from '../../../../service/utils/confirm.service';
import { UtilsService } from '../../../../service/utils/utils.service';
import { FormsModule } from '@angular/forms';
import { PaginatorComponent } from '../../../../utils/paginator/paginator.component';
import { DatePipe } from '@angular/common';

@Component({
    selector: 'app-sys-question-list',
    templateUrl: './sys-question-list.component.html',
    styleUrl: './sys-question-list.component.scss',
    imports: [
        RouterLink,
        FormsModule,
        PaginatorComponent,
        DatePipe,
    ],
})
export class SysQuestionListComponent {
  status: number | null = null;
  questionList: sysQuestionItem[] = [];

  pageSize: number = 10; //一頁幾筆資料
  nowPage: number = 1;
  totalCount: number = 0; //總筆數
  pageShowCount: number = 5; //分頁器秀幾個

  list: {
    column: string;
    title: string;
    width: string;
    sort: boolean;
  }[] = [
    {
      column: 'index',
      title: '項次',
      width: '5%',
      sort: false,
    },
    {
      column: 'content',
      title: '回饋建議',
      width: '26%',
      sort: false,
    },
    {
      column: 'reply',
      title: '處理內容',
      width: '26%',
      sort: false,
    },
    {
      column: 'status',
      title: '處理狀態',
      width: '6%',
      sort: false,
    },
    {
      column: 'creator',
      title: '回饋者',
      width: '5%',
      sort: false,
    },
    {
      column: 'creationTime',
      title: '回饋日期',
      width: '6%',
      sort: false,
    },
    {
      column: 'replyCreationTime',
      title: '處理日期',
      width: '6%',
      sort: false,
    },
  ];

  constructor(
    private router: Router,
    private confirmService: ConfirmService,
    private questionService: QuestionService,
    private utils: UtilsService
  ) {}

  ngOnInit(): void {
    this.utils.setTitle('系統回饋');
    window.scrollTo({
      top: 0,
      behavior: 'smooth',
    });
    this.getSysQuestionList();
  }

  search() {
    this.nowPage = 1;
    this.getSysQuestionList();
  }

  getSysQuestionList() {
    let req: getSysQuestionListReq = {
      page: this.nowPage,
      pageSize: this.pageSize,
      status: this.status,
    };

    this.questionService.getSysQuestionList(req).subscribe({
      next: (resp: getSysQuestionListResp) => {
        if (resp.status === apiStatus.SUCCESS) {
          this.questionList = resp.data.opinionItems;
          this.totalCount = resp.data.itemTotalCount;
        } else {
          this.confirmService.showError(resp.message, '錯誤');
        }
      },
      error: () => {},
    });
  }

  /**
   * 第幾頁
   * @param item number
   */
  getPageFromPaginator(nowPage: number) {
    this.nowPage = nowPage;
    this.getSysQuestionList();
  }
  /**
   * 每頁資料數量
   * @param item number
   */
  getPageSizeFromPaginator(pageSize: number) {
    this.pageSize = pageSize;
    this.nowPage = 1;
    this.getSysQuestionList();
  }

  back(event: Event) {
    event.preventDefault();
    history.back();
  }
}
