<div class="paginator-group wrap">
  <div class="paginator-detail wrap">
    <div>
      <span>每頁資料</span>
      <select [(ngModel)]="pageSize" (ngModelChange)="changePageSize($event)">
        @for (option of pageSizeOptions; track option) {
        <option [value]="option">{{option}}</option>
        }
      </select>
      <span>筆，</span>
    </div>
    <span>{{pageReport}}</span>
  </div>

  <div class="paginator-page wrap">
    <div class="page-btn" (click)="firstPage()">
      <span class="material-icons">
        first_page
      </span>
    </div>
    <div class="page-btn" (click)="prevPage()">
      <span class="material-icons">
        keyboard_arrow_left
      </span>
    </div>

    <div class="pages-box wrap">
      @for (item of showPagesData; track item; let i = $index) {
      <div class="page-btn" (click)="clickPage(item)" [ngClass]="{'actived': nowPage === item}">
        <span>
          {{item}}
        </span>
      </div>
      }

      @if (((pageIndex + pageShowCount) < pagesData[pagesData.length-1] ) ) { <span>
        .....</span>
        <div class="page-btn" (click)="clickPage(pagesData[pagesData.length-1])" style="margin-left: 10px;">
          {{pagesData[pagesData.length-1]}}</div>
        }
    </div>

    <div class="page-btn" (click)="nextPage()">
      <span class="material-icons">
        keyboard_arrow_right
      </span>
    </div>

    <div class="page-btn" (click)="lastPage()">
      <span class="material-icons">
        last_page
      </span>
    </div>
    <div class="wrap" style="display: flex;align-items: center;">
      前往第
      <div class="paginator-detail">
        <input type="text" (input)="onInput($event)" (blur)="changePage($event)" (keyup.enter)="changePage($event)">頁
      </div>
    </div>
  </div>
</div>