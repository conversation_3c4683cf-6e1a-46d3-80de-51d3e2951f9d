import { Component, OnInit } from '@angular/core';
import {
  FormGroup,
  FormBuilder,
  FormsModule,
  ReactiveFormsModule,
} from '@angular/forms';
import { WordSubmissionService } from '../../../service/curl/word-submission.service';
import { fork<PERSON>oin } from 'rxjs';
import {
  mainCategoryList,
  subCategoryList,
} from '../../../interface/wordSubmission.interface';
import { LanguageService } from '../../../service/curl/language.service';
import { FileService } from '../../../service/curl/file.service';
import { NewWordService } from '../../../service/curl/new-word.service';
import {
  getNewWordListReq,
  getNewWordListResp,
  newWordItem,
} from '../../../interface/newWord.interface';
import { MatDialog } from '@angular/material/dialog';
import { NewWordDialogComponent } from '../../../dialog/new-word-dialog/new-word-dialog.component';
import { UtilsService } from '../../../service/utils/utils.service';
import { RouterLink } from '@angular/router';
import { MatIcon } from '@angular/material/icon';
import { NgClass } from '@angular/common';
import { PaginatorComponent } from '../../../utils/paginator/paginator.component';
import { getDialectAndLanguageListResp } from '../../../interface/language.interface';
import { apiStatus } from '../../../enum/apiStatus.enum';

@Component({
  selector: 'app-term-release',
  imports: [
    RouterLink,
    FormsModule,
    ReactiveFormsModule,
    MatIcon,
    NgClass,
    PaginatorComponent,
  ],
  templateUrl: './term-release.component.html',
  styleUrl: './term-release.component.scss',
})
export class TermReleaseComponent implements OnInit {
  form: FormGroup;
  isKeyboard: boolean = false;
  mainCategoryList: mainCategoryList[] = [];
  tempSubCategoryList: subCategoryList[] = [];
  subCategoryList: subCategoryList[] = [];
  newWordList: newWordItem[] = [];
  yearList: number[] = [];
  languageList: { id: string; name: string }[] = [];

  keyboardList: string[] = [];
  pageSize: number = 10;
  nowPage: number = 1;
  totalCount: number = 0;
  pageShowCount: number = 5; //分頁器秀幾個

  private currentMediaElement: HTMLAudioElement | null = null;

  constructor(
    private wordSubmissionService: WordSubmissionService,
    private languageService: LanguageService,
    private formBuilder: FormBuilder,
    private fileService: FileService,
    private matDialg: MatDialog,
    private newWordService: NewWordService,
    private utils: UtilsService
  ) {
    this.form = this.formBuilder.group({
      tribe: [null],
      year: [null],
      mainCategoryId: [null],
      subCategoryId: [null],
      keyword: [''],
    });
  }

  ngOnInit(): void {
    this.utils.setTitle(`公告新詞`);
    this.initialization();
  }

  ngOnDestroy(): void {
    // 離開頁面時停止並移除播放器
    this.stopMusic();
  }

  initialization() {
    forkJoin({
      ethnicityKeyboard: this.getEthnicityKeyboard(),
      newWordYearList: this.getNewWordYearList(),
      wantContributeCategoryList: this.getWantContributeCategoryList(),
    }).subscribe({
      next: (result) => {
        const ethnicityKeyboard = result.ethnicityKeyboard;
        const newWordYearList = result.newWordYearList;
        const wantContributeCategoryList = result.wantContributeCategoryList;
        this.keyboardList = ethnicityKeyboard.data.symbolList;
        this.yearList = newWordYearList.data.yearItems;
        const { mainCategoryList } = wantContributeCategoryList.data;
        this.mainCategoryList = mainCategoryList;
        this.tempSubCategoryList = mainCategoryList.flatMap(
          (mainCategoryList: mainCategoryList) =>
            mainCategoryList.subCategoryList.map(
              (subCategoryList: subCategoryList) => ({
                ...subCategoryList,
              })
            )
        );
      },
    });

    this.getDialectAndLanguageList();
  }

  getDialectAndLanguageList() {
    this.languageService.getDialectAndLanguageList().subscribe({
      next: (resp: getDialectAndLanguageListResp) => {
        if (resp.status === apiStatus.SUCCESS) {
          const { tribes } = resp.data;
          this.languageList = tribes;
        }
      },
      error: () => { },
    });
  }

  getWantContributeCategoryList() {
    return this.wordSubmissionService.getWantContributeCategoryList();
  }
  getNewWordYearList() {
    return this.newWordService.getNewWordYearList(null);
  }

  getEthnicityKeyboard() {
    return this.languageService.getEthnicityKeyboard(null);
  }

  keyUp(value: string, inputElement: HTMLInputElement) {
    const currentKeyword = this.form.get('keyword')?.value || '';
    const start = inputElement.selectionStart ?? currentKeyword.length;
    const end = inputElement.selectionEnd ?? currentKeyword.length;
    const newKeyword =
      currentKeyword.slice(0, start) + value + currentKeyword.slice(end);
    this.form.get('keyword')?.patchValue(newKeyword);
    const cursorPos = start + value.length;
    setTimeout(() => {
      inputElement.focus();
      inputElement.setSelectionRange(cursorPos, cursorPos);
    });
  }

  selectMainCategory() {
    this.form.patchValue({
      subCategoryId: null,
    });
    const selectedSubCategory = this.tempSubCategoryList.filter(
      (item) => item.mainCategoriesId === this.form.value.mainCategoryId
    );
    this.subCategoryList =
      selectedSubCategory.length > 0 ? selectedSubCategory : [];
  }

  search() {
    this.nowPage = 1;
    this.getNewWordList();
  }

  getNewWordList() {
    let req: getNewWordListReq = {
      tribeId: this.form.value.tribe,
      year: this.form.value.year,
      mainCategoryId: this.form.value.mainCategoryId,
      subCategoryId: this.form.value.subCategoryId,
      keyword: this.form.value.keyword,
      page: this.nowPage,
      pageSize: this.pageSize,
    };
    this.newWordService.getNewWordList(req).subscribe({
      next: (resp: getNewWordListResp) => {
        this.newWordList = resp.data.items;
        this.totalCount = resp.data.itemTotalCount;
      },
    });
  }

  play(id: string) {
    if (!id) {
      return;
    }
    if (this.currentMediaElement) {
      this.currentMediaElement.pause();
      this.currentMediaElement.remove();
    }
    this.fileService.getAudioFile(id).subscribe({
      next: (resp: string) => {
        let mediaElement: HTMLAudioElement = document.createElement('audio');
        mediaElement.style.display = 'none'; // 這行讓音頻播放器隱藏
        mediaElement.setAttribute('src', resp); // 設置音頻源
        mediaElement.setAttribute('controls', 'true'); // 加入控制條
        document.body.appendChild(mediaElement);
        mediaElement.play();
        mediaElement.addEventListener('ended', () => {
          this.currentMediaElement = null; // 重置當前音樂播放器
        });

        this.currentMediaElement = mediaElement; // 儲存當前的音樂播放器
      },
      error: () => { },
    });
  }

  stopMusic() {
    if (this.currentMediaElement) {
      this.currentMediaElement.pause();
      this.currentMediaElement.remove();
      this.currentMediaElement = null;
    }
  }

  /**
   * 第幾頁
   * @param item number
   */
  getPageFromPaginator(nowPage: number) {
    this.nowPage = nowPage;
    this.getNewWordList();
  }
  /**
   * 每頁資料數量
   * @param item number
   */
  getPageSizeFromPaginator(pageSize: number) {
    this.pageSize = pageSize;
    this.nowPage = 1;
    this.getNewWordList();
  }

  back(event: Event) {
    event.preventDefault();
    history.back();
  }

  openDetail(event: Event, id: string, name: string) {
    event.preventDefault();
    this.matDialg.open(NewWordDialogComponent, {
      disableClose: true,
      autoFocus: false,
      width: '60%',
      data: {
        name: name,
        id: id,
      },
    });
  }
}
