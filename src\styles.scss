/* You can add global styles to this file, and also import other style files */

@use "../src/assets/scss/body_layout.scss";
@use "../src/assets/scss/master-pages-container-layout.scss";
@use "../src/assets/scss/master-footer-layout.scss";
@use "../src/assets/scss/input-style.scss";
@use "../src/assets/scss/btn-list.scss";
@use "../src/assets/scss/template-list.scss";
@use "../src/assets/scss/breadcrumb-layout.scss";
@use "../src/assets/scss/table-list.scss";
@use "../src/assets/scss/cont-style-list.scss";
@use "../src/assets/scss/keyboard.scss";

html,
body {
    font-family: "Times New Roman", Times, serif !important;
    font-size: 16px;
    height: 100%;
}

.mat-expansion-panel-body {
    padding: 0 !important;
}

.mat-expansion-panel-header,
.mat-expansion-panel-content {
    font-family: "Times New Roman", Times, serif !important;
}

.mat-spinner-color circle {
    stroke: green;
}

.highlighted {
    background-color: lightgreen !important;
}

mat-dialog-content {
    font-family: "Times New Roman", Times, serif !important;
}

.dialog-close-btn {
    font-size: 1em;
    line-height: 1;
    display: flex;
    align-items: center;
    position: relative;
    color: white;
    margin-left: auto;
    cursor: pointer;
}