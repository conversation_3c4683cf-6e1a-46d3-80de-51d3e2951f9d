@use "./scss/word-description-layout.scss";
@use "./scss/second-search.scss";
@use "./scss/term-list.scss";

//進階搜尋

.panel-header {
    height: auto !important;
    background-color: white !important;
}
.panel-header:hover {
    background-color: white !important;
}
.panel-header:focus {
    background-color: white !important;
}

mat-expansion-panel {
    margin-bottom: 1em;
}

.word-description-container {
    display: flex;
    align-items: flex-start;
    max-width: 68vw;
}

.advanced-container {
    min-width: 22vw;
    margin-right: 2em;
}

@media (max-width: 1200px) {
    .word-description-container {
        display: flex;
        flex-direction: column;
        max-width: 100%;
    }
    .advanced-container {
        width: 90%;
        margin: 0 10px;
        padding: 10px;
    }
    .word-description-list-layout {
        width: 90%;
        margin: 0 10px;
        padding: 10px;
    }
}

.tag {
    width: 60px;
    padding: 8px 20px;
}

@media (max-width: 1310px) {
    .tag {
        width: 70px;
    }
}
@media (max-width: 680px) {
    .tag {
        width: 80px;
    }
}

.function-block-background {
    background-color: #e7e8e9; /* 灰色背景 */
    padding: 1em 0;
    .function-block {
        padding: 10px 30px;
        .function-gropu {
            display: flex;
            align-items: center;
            img {
                cursor: pointer;
                display: flex;
                align-items: center;
            }
        }
        .block {
            margin-bottom: 1em;
        }
    }
}

.spinner-wrapper-index {
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 998;

    app-spinner {
        width: 6rem;
        height: 6rem;
    }
}

.panel-header-group {
    width: 100%;
    display: flex;
    margin: 10px 0;
    line-height: 2;
    align-items: center;
    .panel-header-tag {
        margin-right: 10px;
        padding: 8px 20px;
        border-radius: 100px;
        font-size: 1.125em;
        color: #4a7f42;
        font-weight: bold;
        width: 150px;
    }

    .panel-header-description-tag {
        margin-right: 10px;
        padding: 8px 20px;
        border-radius: 100px;
        font-size: 1.125em;
        color: white;
        background-color: #4a7f42;
    }
    .panel-header-explanation {
        width: 60%;
        .word-description-cont-font {
            width: 80%;
            display: block;
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
        }
    }
}

.notfound-group {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    margin-top: 10vh;

    .notfound-text {
        font-size: 2em;
        font-weight: bold;
    }
}

::ng-deep {
    .mdc-tab__text-label {
        font-size: 2em;
    }
    .mdc-tab-indicator__content--underline {
        border-color: #4a7f42 !important;
        border-bottom: 6px solid;
    }
}

.word-description-explain {
    label {
        color: gray;
    }
}

.word-description-sentence-text {
    span {
        cursor: pointer;
    }
}

.word-list {
    display: flex;
    flex-direction: column;
    max-height: 300px; /* 設定最大高度 */
    overflow-y: auto; /* 啟用垂直滾動條 */
    background-color: #f8f8f8; /* 背景色 */
    padding: 10px; /* 內距 */
    border: 1px solid #ddd; /* 邊框 */
    border-radius: 5px; /* 圓角 */
}

.word-list span {
    margin: 5px 0; /* 每個文字項目的間距 */
    cursor: pointer; /* 鼠標變成手型 */
    color: #000; /* 預設文字顏色 */
}

.word-list span:hover {
    color: #ff0000; /* 滑鼠懸停時變紅 */
}

.word-list span:active,
.word-list span.active {
    color: #ff0000; /* 滑鼠點擊時變紅 */
}

.word-list span:focus {
    outline: none; /* 移除點擊時的外框 */
    background-color: #f0f0f0; /* 點擊時背景色 */
}

/* 滾動條樣式 */
.word-list::-webkit-scrollbar {
    width: 8px; /* 滾動條寬度 */
}

.word-list::-webkit-scrollbar-thumb {
    background: #ccc; /* 滾動條顏色 */
    border-radius: 4px; /* 滾動條圓角 */
}

.word-list::-webkit-scrollbar-thumb:hover {
    background: #999; /* 滑鼠懸停時的顏色 */
}

.carousel-block {
    width: 30%;
    margin: 1em;
}

mat-icon {
    cursor: pointer;
}

.material-symbols-outlined {
    font-variation-settings:
        "FILL" 1,
        "wght" 400,
        "GRAD" 0,
        "opsz" 24;
}

.bottom-border {
    border-bottom: 3px solid; /* 底線顏色 & 粗細 */
    padding-bottom: 2px; /* 控制底線與文字的距離 */
}
.bottom-border-hidden {
    border-bottom: 3px solid transparent; /* 底線顏色 & 粗細 */
    padding-bottom: 2px; /* 控制底線與文字的距離 */
}

@media (max-width: 640px) {
    .panel-header-group {
        align-items: flex-start !important;
        flex-direction: column !important;
    }
    .panel-header-name {
        width: 100% !important;
    }
    .panel-header-name-group {
        display: flex !important;
        flex-direction: column !important;
        align-items: flex-start !important;
    }
    .panel-header-explanation {
        width: 100% !important;
        margin: 10px 0 !important;
    }
    .panel-header-tag-group {
        display: flex !important;
        align-items: center !important;
        width: 100% !important;
        margin: 10px 0 !important;
    }
    .panel-header {
        // height: 20vh !important;
    }
    .panel-header-description-tag {
        min-width: 40px !important;
    }
    .name-box {
        max-width: 100% !important;
        width: 100% !important;
        margin: 10px 0 !important;
    }
}
