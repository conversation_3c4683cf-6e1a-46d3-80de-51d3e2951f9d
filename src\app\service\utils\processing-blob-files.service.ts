import { Injectable } from '@angular/core';
import { Dom<PERSON>anitizer, SafeUrl } from '@angular/platform-browser';

@Injectable({
  providedIn: 'root',
})
export class ProcessingBlobFilesService {
  constructor(private sanitizer: DomSanitizer) {}
  // 根据边界标记分割二进制数据
  splitByBoundary(data: Uint8Array, boundary: Uint8Array): Uint8Array[] {
    const parts: Uint8Array[] = [];
    let start = 0;

    for (let i = 0; i < data.length; i++) {
      if (this.matchBoundary(data, i, boundary)) {
        const part = data.slice(start, i);
        if (part.length > 0) {
          parts.push(part);
        }
        start = i + boundary.length;
      }
    }

    // 添加最后一部分
    if (start < data.length) {
      parts.push(data.slice(start));
    }

    return parts.filter((part) => part.length > 0);
  }

  // 检查当前位置是否匹配边界
  matchBoundary(
    data: Uint8Array,
    start: number,
    boundary: Uint8Array
  ): boolean {
    for (let i = 0; i < boundary.length; i++) {
      if (data[start + i] !== boundary[i]) {
        return false;
      }
    }
    return true;
  }

  // 提取 Content-Type
  extractContentType(part: Uint8Array): string {
    const text = new TextDecoder().decode(part);
    const match = text.match(/Content-Type: (audio\/mpeg|image\/png)/);
    return match ? match[1].trim() : '';
  }

  // 提取内容数据
  extractContentData(part: Uint8Array): Uint8Array {
    const doubleCRLF = '\r\n\r\n';
    const text = new TextDecoder().decode(part);
    const splitIndex = text.indexOf(doubleCRLF);

    if (splitIndex >= 0) {
      const headerEnd = splitIndex + doubleCRLF.length;
      return part.slice(headerEnd);
    }

    return new Uint8Array();
  }

  getSafeImageUrl(blob: Blob): SafeUrl {
    if (blob) {
      return this.sanitizer.bypassSecurityTrustUrl(URL.createObjectURL(blob));
    }
    return '';
  }
}
