import { defaultItem } from './share.interface';

export interface getLinkListReq {
  keyword: string; // 關鍵字
  page: number;
  pageSize: number;
}

export interface getLinkListResp extends defaultItem {
  data: {
    searchData: linkItem[];
    page: number;
    pageSize: number;
    itemTotalCount: number;
  };
}
export interface linkItem {
  relatedLinkId: string;
  websiteName: string; // 名稱
  websiteLink: string; // 連結
}
