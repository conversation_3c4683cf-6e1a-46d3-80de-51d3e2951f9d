import { Component, OnInit } from '@angular/core';
import { ShareService } from '../../service/curl/share.service';
import {
  getAboutListResp,
  getBrowseVisitorsResp,
} from '../../interface/share.interface';
import { RouterLink } from '@angular/router';
import { DatePipe } from '@angular/common';

@Component({
    selector: 'app-footer',
    templateUrl: './footer.component.html',
    styleUrl: './footer.component.scss',
    imports: [RouterLink, DatePipe],
})
export class FooterComponent implements OnInit {
  dayBrowseCount: number = 0;
  totalBrowseCount: number = 0;
  now = Date.now();
  privacyId: string = '';
  copyrightId: string = '';
  securityId: string = '';

  constructor(private shareService: ShareService) {}

  ngOnInit(): void {
    this.getAboutList();
    this.shareService.getBrowseVisitors().subscribe({
      next: (resp: getBrowseVisitorsResp) => {
        this.dayBrowseCount = resp.data.dayBrowseCount;
        this.totalBrowseCount = resp.data.totalBrowseCount;
      },
      error: () => {},
    });
  }

  linkExternal(url: string) {
    window.open(url, '_blank', 'noopener,noreferrer');
  }

  getAboutList() {
    this.shareService.getAboutList().subscribe({
      next: (resp: getAboutListResp) => {
        this.privacyId = resp.data[1].id;
        this.copyrightId = resp.data[2].id;
        this.securityId = resp.data[3].id;
      },
      error: () => {},
    });
  }
}
