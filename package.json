{"name": "ilrdf-dictionary-font-stage", "version": "0.0.0", "scripts": {"ng": "ng", "start": "ng serve --proxy-config proxy.conf.json --port 3700 --o", "build:dev": "ng build --configuration development --aot", "build:prod": "ng build --configuration production --aot", "build:staging": "ng build --configuration staging --aot", "build:all": "npm run build:prod && npm run build:staging", "watch": "ng build --watch --configuration development", "test": "ng test"}, "private": true, "dependencies": {"@angular/animations": "^19.0.6", "@angular/cdk": "^19.0.5", "@angular/common": "^19.0.6", "@angular/compiler": "^19.0.6", "@angular/core": "^19.0.6", "@angular/forms": "^19.0.6", "@angular/material": "^19.0.5", "@angular/platform-browser": "^19.0.6", "@angular/platform-browser-dynamic": "^19.0.6", "@angular/router": "^19.0.6", "angular-froala-wysiwyg": "^4.5.2", "angular-oauth2-oidc": "^19.0.0", "froala-editor": "^4.5.2", "hammerjs": "^2.0.8", "ngx-owl-carousel-o": "^19.0.0", "rxjs": "~7.8.0", "tslib": "^2.3.0", "uuid": "^11.1.0", "zone.js": "~0.15.0"}, "devDependencies": {"@angular-devkit/build-angular": "^19.0.7", "@angular/cli": "^19.0.7", "@angular/compiler-cli": "^19.0.6", "@types/jasmine": "~5.1.0", "jasmine-core": "~5.1.0", "karma": "~6.4.0", "karma-chrome-launcher": "~3.2.0", "karma-coverage": "~2.2.0", "karma-jasmine": "~5.1.0", "karma-jasmine-html-reporter": "~2.1.0", "typescript": "~5.6.3"}}