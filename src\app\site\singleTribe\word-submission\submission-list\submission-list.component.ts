import { Component } from '@angular/core';
import {
  getEthnicityLanguageResp,
  wordItem,
} from '../../../../interface/language.interface';
import { GetEthnicityService } from '../../../../service/utils/get-ethnicity.service';
import { LanguageService } from '../../../../service/curl/language.service';
import { WordSubmissionService } from '../../../../service/curl/word-submission.service';
import {
  creationItem,
  getSubmissionListReq,
  getSubmissionListResp,
} from '../../../../interface/wordSubmission.interface';
import { FormsModule } from '@angular/forms';
import { PaginatorComponent } from '../../../../utils/paginator/paginator.component';
import { DatePipe } from '@angular/common';

@Component({
    selector: 'app-submission-list',
    templateUrl: './submission-list.component.html',
    styleUrl: './submission-list.component.scss',
    imports: [
        FormsModule,
        PaginatorComponent,
        DatePipe,
    ],
})
export class SubmissionListComponent {
  tribeId: string | null = null;
  dialectId: string | null = null;
  dialectList: { id: string; name: string }[] = [];
  status: number | null = null;
  keyword: string = '';

  list: {
    column: string;
    title: string;
    width: string;
  }[] = [
    {
      column: '',
      title: '項次',
      width: '5%',
    },
    {
      column: '',
      title: '語別',
      width: '15%',
    },
    {
      column: '',
      title: '族語創詞',
      width: '',
    },
    {
      column: '',
      title: '華語創詞',
      width: '',
    },
    {
      column: '',
      title: '推薦者',
      width: '12%',
    },
    {
      column: '',
      title: '處理狀態',
      width: '15%',
    },
    {
      column: '',
      title: '推薦數',
      width: '12%',
    },
    {
      column: '',
      title: '處理日期',
      width: '15%',
    },
  ];

  submissionList: creationItem[] = [];

  pageSize: number = 10;
  nowPage: number = 1;
  totalCount: number = 0;
  pageShowCount: number = 5; //分頁器秀幾個

  constructor(
    private getEthnicityService: GetEthnicityService,
    private wordSubmissionService: WordSubmissionService,
    private languageService: LanguageService
  ) {}

  ngOnInit(): void {
    this.tribeId = this.getEthnicityService.GetEthnicityId();
    this.getEthnicityLanguage();
    this.initialization();
  }

  initialization() {}

  getEthnicityLanguage() {
    return this.languageService
      .getEthnicityLanguage(this.tribeId as string)
      .subscribe({
        next: (resp: getEthnicityLanguageResp) => {
          this.dialectList = resp.data.items;
        },
      });
  }
  search() {
    this.nowPage = 1;
    this.getSubmissionList();
  }

  getSubmissionList() {
    let req: getSubmissionListReq = {
      tribeId: this.tribeId as string,
      keyword: this.keyword,
      status: this.status as number | null,
      dialectId: this.dialectId,
      page: this.nowPage,
      pageSize: this.pageSize,
    };
    this.wordSubmissionService.getSubmissionList(req).subscribe({
      next: (resp: getSubmissionListResp) => {
        this.submissionList = resp.data.creationItems;
        this.totalCount = resp.data.itemTotalCount;
      },
      error: () => {},
    });
  }

  /**
   * 第幾頁
   * @param item number
   */
  getPageFromPaginator(nowPage: number) {
    this.nowPage = nowPage;
    this.getSubmissionList();
  }
  /**
   * 每頁資料數量
   * @param item number
   */
  getPageSizeFromPaginator(pageSize: number) {
    this.pageSize = pageSize;
    this.nowPage = 1;
    this.getSubmissionList();
  }
}
