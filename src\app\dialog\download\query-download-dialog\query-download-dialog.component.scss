@use "./scss/query-download.scss";

.mat-mdc-dialog-title {
    display: flex;
    align-items: self-end;
    margin: 0;
}

.btn-group {
    margin-top: 2em;
}

.advanced-search-cont {
    margin: 0;
    padding: 0;
    display: flex;
    flex-wrap: wrap; // 允許多行排列
    list-style: none;
    align-items: center;
    .advanced-search-item {
        box-sizing: border-box;
        padding: 5px 0; // 調整間距以確保上下對齊
        display: flex;
        align-items: center;
        .checkbox-list {
            padding: 0;
            display: flex;
            align-items: baseline; // 讓 checkbox 和 label 垂直對齊
        }
        .theme-count {
            color: #0a91d4;
        }
        label {
            margin-left: 5px; // 調整 checkbox 和 label 之間的間距
        }
    }
}

@media (max-width: 3000px) {
    .advanced-search-cont {
        .advanced-search-item {
            width: 30%; // 每行顯示 3 個 checkbox，留一些空白間距
        }
    }
}

@media (max-width: 1400px) {
    .advanced-search-cont {
        .advanced-search-item {
            width: 50%; // 每行顯示 2 個 checkbox，留一些空白間距
        }
    }
}
@media (max-width: 880px) {
    .advanced-search-cont {
        .advanced-search-item {
            width: 100%; // 每行顯示 1 個 checkbox，留一些空白間距
        }
    }
}
