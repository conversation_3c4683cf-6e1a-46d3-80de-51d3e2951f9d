import { Component, Inject } from '@angular/core';
import { MAT_DIALOG_DATA, MatDialogRef, MatDialogTitle, MatDialogContent, MatDialogActions, MatDialogClose } from '@angular/material/dialog';
import { DomSanitizer, SafeHtml } from '@angular/platform-browser';
import { MatIcon } from '@angular/material/icon';
import { CdkScrollable } from '@angular/cdk/scrolling';

@Component({
    selector: 'app-tip-dialog',
    templateUrl: './tip-dialog.component.html',
    styleUrl: './tip-dialog.component.scss',
    imports: [
        MatDialogTitle,
        MatIcon,
        CdkScrollable,
        MatDialogContent,
        MatDialogActions,
        MatDialogClose,
    ],
})
export class TipDialogComponent {
  tip: SafeHtml;

  constructor(
    @Inject(MAT_DIALOG_DATA)
    public data: {
      tip: string;
    },
    private dialogRef: MatDialogRef<TipDialogComponent>,
    private sanitizer: DomSanitizer
  ) {
    this.tip = this.sanitizer.bypassSecurityTrustHtml(data.tip);
  }

  close() {
    this.dialogRef.close();
  }
}
