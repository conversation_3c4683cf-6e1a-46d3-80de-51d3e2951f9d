import { Overlay, OverlayRef } from '@angular/cdk/overlay';
import { ComponentPortal } from '@angular/cdk/portal';
import { Injectable } from '@angular/core';
import { SpinnerComponent } from '../../utils/spinner/spinner.component';

@Injectable({
  providedIn: 'root',
})
export class SpinnerService {
  private overlayRef?: OverlayRef;
  constructor(private overlay: Overlay) {}
  public show(message = '') {
    // Returns an OverlayRef (which is a PortalHost)
    if (this.overlayRef) {
      return; // 如果已有，則不再創建新的 overlay
    } else {
      this.overlayRef = this.overlay.create();
    }

    // Create ComponentPortal that can be attached to a PortalHost
    const spinnerOverlayPortal = new ComponentPortal(SpinnerComponent);
    const component = this.overlayRef.attach(spinnerOverlayPortal); // Attach ComponentPortal to PortalHost
  }

  public hide() {
    if (this.overlayRef) {
      this.overlayRef.detach();
      this.overlayRef = undefined; // 清除對象，方便下一次使用
    }
  }
}
