import { Component, Inject } from '@angular/core';
import {
  getRootStructureResp,
  rootStructureItem,
} from '../../interface/language.interface';
import { HttpErrorResponse } from '@angular/common/http';
import {
  MAT_DIALOG_DATA,
  MatDialogRef,
  MatDialogTitle,
  MatDialogContent,
  MatDialogActions,
  MatDialogClose,
} from '@angular/material/dialog';
import { LanguageService } from '../../service/curl/language.service';
import { ConfirmService } from '../../service/utils/confirm.service';
import { environment } from '../../../environments/environment';
import { MatIcon } from '@angular/material/icon';
import { CdkScrollable } from '@angular/cdk/scrolling';
import { MatProgressSpinner } from '@angular/material/progress-spinner';

@Component({
  selector: 'app-root-structure-dialog',
  templateUrl: './root-structure-dialog.component.html',
  styleUrl: './root-structure-dialog.component.scss',
  imports: [
    MatDialogTitle,
    MatIcon,
    CdkScrollable,
    MatDialogContent,
    MatProgressSpinner,
    MatDialogActions,
    MatDialogClose,
  ],
})
export class RootStructureDialogComponent {
  rootStructure?: rootStructureItem;
  isLoading: boolean = false;
  constructor(
    @Inject(MAT_DIALOG_DATA)
    public data: {
      dictionaryId: string;
    },
    private dialogRef: MatDialogRef<RootStructureDialogComponent>,
    private languageService: LanguageService,
    private confirmService: ConfirmService
  ) {}

  ngOnInit(): void {
    this.getRootStructure(this.data.dictionaryId);
  }

  getRootStructure(dictionaryId: string) {
    this.isLoading = true;
    this.languageService.getRootStructure(dictionaryId).subscribe({
      next: (resp: getRootStructureResp) => {
        this.isLoading = false;
        this.rootStructure = resp.data;
      },
      error: (err: HttpErrorResponse) => {
        this.isLoading = false;
        this.confirmService.showError('錯誤', err.message);
      },
    });
  }
  close() {
    this.dialogRef.close();
  }

  goto(id: string, word: string, tribeName: string) {
    window.open(
      `${environment.sitePath}/singleSearch?tribeId=${id}&keyword=${word}&tribeName=${tribeName}`,
      '_blank'
    );
  }
}
