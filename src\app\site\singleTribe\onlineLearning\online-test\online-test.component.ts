import { Component, OnInit } from '@angular/core';
import { GetEthnicityService } from '../../../../service/utils/get-ethnicity.service';
import { OnlineLearningService } from '../../../../service/curl/online-learning.service';
import { getOnlineTestLinkResp } from '../../../../interface/onlineLearning.interface';
import { UtilsService } from '../../../../service/utils/utils.service';
import { RouterLink } from '@angular/router';
import { NgIf } from '@angular/common';

@Component({
    selector: 'app-online-test',
    templateUrl: './online-test.component.html',
    styleUrl: './online-test.component.scss',
    imports: [RouterLink, NgIf],
})
export class OnlineTestComponent implements OnInit {
  tribeId: string | null = null;
  ethnicity: string | null = null;
  gameList: { name: string; img: string; url: string }[] = [];
  nameList: string[] = [];
  imgList: string[] = [
    'image/game/game01.png',
    'image/game/game02.png',
    'image/game/game03.png',
    'image/game/game04.png',
    'image/game/game05.png',
    'image/game/game06.png',
  ];
  constructor(
    private getEthnicityService: GetEthnicityService,
    private onlineLearningService: OnlineLearningService,
    private utils:UtilsService
  ) {}

  ngOnInit(): void {
    this.tribeId = this.getEthnicityService.GetEthnicityId();
    this.ethnicity = this.getEthnicityService.GetEthnicityName();
    this.utils.setTitle(`${this.ethnicity}-線上測驗`)
    this.getOnlineTestLink();
  }

  back(event:Event) {
    event.preventDefault();
    history.back();
  }

  getOnlineTestLink() {
    this.onlineLearningService
      .getOnlineTestLink(this.tribeId as string)
      .subscribe({
        next: (resp: getOnlineTestLinkResp) => {
          this.gameList = resp.data.gameUrls.map((item, index) => ({
            name: this.nameList[index] || '',
            img: this.imgList[index] || '',
            url: item,
          }));
        },
      });
  }
}
