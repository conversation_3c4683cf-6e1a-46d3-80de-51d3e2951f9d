import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import {
  addWantContributeReq,
  getSubmissionListReq,
  getSubmissionListResp,
  getWantContributeCategoryListResp,
  getWantContributeMethodListResp,
  getWantToKnowListResp,
  sendWantToKnowReq,
} from '../../interface/wordSubmission.interface';
import { defaultItem } from '../../interface/share.interface';

@Injectable({
  providedIn: 'root',
})
export class WordSubmissionService {
  constructor(private httpClient: HttpClient) {}

  /**
   * 創詞投稿-投稿列表
   * @param req
   * @returns
   */
  getSubmissionList(
    req: getSubmissionListReq
  ): Observable<getSubmissionListResp> {
    return this.httpClient.post<getSubmissionListResp>(
      'api/app/dictionary-creation/select',
      req
    );
  }

  /**
   * 創詞投稿-我想知道選項列表
   * @param req
   * @returns
   */
  getWantToKnowList(tribeId: string): Observable<getWantToKnowListResp> {
    return this.httpClient.post<getWantToKnowListResp>(
      'api/app/dictionary-creation/get-want-to-know',
      {
        tribeId: tribeId,
      }
    );
  }

  /**
   * 創詞投搞-我想知道發表
   * @returns
   */
  sendWantToKnow(req: sendWantToKnowReq): Observable<defaultItem> {
    return this.httpClient.post<defaultItem>(
      'api/app/dictionary-creation/publish-want-to-know',
      req
    );
  }

  /**
   * 創詞投搞-我要投稿創詞類別
   * @returns
   */
  getWantContributeCategoryList(): Observable<getWantContributeCategoryListResp> {
    return this.httpClient.get<getWantContributeCategoryListResp>(
      'api/app/dictionary-creation/categories'
    );
  }

  /**
   * 創詞投搞-我要投稿創詞方法
   * @returns
   */
  getWantContributeMethodList(): Observable<getWantContributeMethodListResp> {
    return this.httpClient.get<getWantContributeMethodListResp>(
      'api/app/dictionary-creation/method'
    );
  }

  /**
   * 創詞投搞-我要投稿
   * @param req
   * @returns
   */
  addWantContribute(req: addWantContributeReq): Observable<defaultItem> {
    const formData: FormData = new FormData();
    formData.append('dictionaryName', req.dictionaryName);
    formData.append('chineseExplanation', req.chineseExplanation);
    formData.append('audioFile', req.audioFile);
    formData.append('categoryId', req.categoryId);
    formData.append('tribeId', req.tribeId);
    formData.append('dialectId', req.dialectId);
    formData.append('creationMethodOther', req.creationMethodOther);
    formData.append('email', req.email);
    formData.append('name', req.name);
    formData.append('dictionaryNote', req.dictionaryNote);
    formData.append('creationConcept', req.creationConcept);
    formData.append('originalSentence', req.originalSentence);
    formData.append('chineseSentence', req.chineseSentence);
    formData.append('verifyCode', req.verifyCode);
    formData.append('SessionId', req.sessionId);
    if (req.subCategoryId) {
      formData.append('subCategoryId', req.subCategoryId);
    }
    req.creationMethodId.map((item) => {
      formData.append('creationMethodId', item);
    });
    req.creationByWordList.map((item) => {
      formData.append('creationByWordList', item);
    });
    return this.httpClient.post<defaultItem>(
      'api/app/dictionary-creation/publish',
      formData
    );
  }
}
