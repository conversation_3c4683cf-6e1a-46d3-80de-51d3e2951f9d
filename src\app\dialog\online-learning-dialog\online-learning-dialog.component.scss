@use "./scss/word-description-layout.scss";
@use "./scss/term-list.scss";

.mat-mdc-dialog-title {
    display: flex;
    align-items: self-end;
    margin: 0;
}

//進階搜尋

.panel-header {
    height: 10vh !important;
    background-color: white !important;
}
.panel-header:hover {
    background-color: white !important;
}
.panel-header:focus {
    background-color: white !important;
}

mat-expansion-panel {
    margin-bottom: 1em;
}

.tag {
    margin: 0 10px;
    padding: 8px 20px;
    border-radius: 100px;
    font-size: 1em;
    color: white;
    background-color: #4a7f42;
}

.function-block-background {
    background-color: #e7e8e9; /* 灰色背景 */
    padding: 1em 0;
    .function-block {
        padding: 10px 30px;
        .function-gropu {
            display: flex;
            align-items: center;
            img {
                cursor: pointer;
                display: flex;
                align-items: center;
            }
        }
        .block {
            margin-bottom: 1em;
        }
    }
}

.spinner-wrapper-index {
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 998;

    app-spinner {
        width: 6rem;
        height: 6rem;
    }
}

.more-description-group {
    display: flex;
    align-items: center;
    justify-content: center;
    .more-description-tag {
        display: flex;
        cursor: pointer;
        align-items: center;
        justify-content: center;
        margin-right: 10px;
        padding: 10px 20px;
        border-radius: 100px;
        font-size: 1.5em;
        color: black;
        background-color: white;
        box-shadow:
            0 4px 6px rgba(0, 0, 0, 0.2),
            0 1px 3px rgba(0, 0, 0, 0.1); /* 添加外部陰影 */
        transition: box-shadow 0.3s ease; /* 添加過渡效果 */
    }

    .more-description-tag:hover {
        box-shadow:
            0 6px 8px rgba(0, 0, 0, 0.3),
            0 2px 4px rgba(0, 0, 0, 0.2); /* 滑鼠懸停時增加陰影效果 */
    }
}

.panel-header-group {
    width: 100%;
    display: flex;
    line-height: 1;
    align-items: center;
    .panel-header-tag {
        margin-right: 10px;
        border-radius: 100px;
        font-size: 1.125em;
        color: #4a7f42;
        font-weight: bold;
        width: 150px;
    }

    .panel-header-description-tag {
        margin-right: 10px;
        padding: 8px 20px;
        border-radius: 100px;
        font-size: 1.125em;
        color: white;
        background-color: #4a7f42;
    }
    .tag {
        width: 60px;
        padding: 8px 20px;
    }

    @media (max-width: 1310px) {
        .tag {
            width: 70px;
        }
    }
    @media (max-width: 680px) {
        .tag {
            width: 80px;
        }
    }
}

.notfound-group {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    margin-top: 10vh;

    .notfound-text {
        font-size: 2em;
        font-weight: bold;
    }
}

::ng-deep {
    .mdc-tab__text-label {
        font-size: 2em;
    }
    .mdc-tab-indicator__content--underline {
        border-color: #4a7f42 !important;
        border-bottom: 6px solid;
    }
}

.advanced-item {
    display: flex;
    flex-direction: column;
    .range-group {
        display: flex;
        flex-direction: row;
        align-items: center;
        span {
            font-size: 1.6em;
        }
        select {
            width: 50%;
        }
    }
    span {
        font-size: 1.2em;
    }
    select {
        width: 100%;
    }
}

.word-description-explain {
    label {
        color: gray;
    }
}

.word-description-sentence-text {
    span {
        cursor: pointer;
    }
}

.carousel-block {
    width: 30%;
    margin: 1em;
}

.material-symbols-outlined {
    font-variation-settings:
        "FILL" 1,
        "wght" 400,
        "GRAD" 0,
        "opsz" 24;
}

.bottom-border {
    border-bottom: 3px solid; /* 底線顏色 & 粗細 */
    padding-bottom: 2px; /* 控制底線與文字的距離 */
}
.bottom-border-hidden {
    border-bottom: 3px solid transparent; /* 底線顏色 & 粗細 */
    padding-bottom: 2px; /* 控制底線與文字的距離 */
}


@media (max-width: 640px) {
    .panel-header-group {
        align-items: flex-start !important;
        flex-direction: column !important;
    }
    .panel-header-name {
        width: 100% !important;
    }
    .panel-header-name-group {
        display: flex !important;
        flex-direction: column !important;
        align-items: flex-start !important;
    }
    .panel-header-explanation {
        width: 100% !important;
        margin: 10px 0 !important;
    }
    .panel-header-tag-group {
        display: flex !important;
        align-items: center !important;
        width: 100% !important;
        margin: 10px 0 !important;
    }
    .panel-header {
        height: 20vh !important;
    }
    .panel-header-description-tag {
        min-width: 40px !important;
    }
    .name-box {
        max-width: 100% !important;
        width: 100% !important;
        margin: 10px 0 !important;
    }
}
