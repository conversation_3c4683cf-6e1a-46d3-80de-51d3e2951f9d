import { Component } from '@angular/core';
import { IntroductionService } from '../../../service/curl/introduction.service';
import { GetEthnicityService } from '../../../service/utils/get-ethnicity.service';
import { ActivatedRoute, RouterLink } from '@angular/router';
import { getIntroductionResp } from '../../../interface/introduction.interface';
import { DomSanitizer, SafeHtml } from '@angular/platform-browser';
import { UtilsService } from '../../../service/utils/utils.service';
import { DatePipe } from '@angular/common';
import { FroalaViewModule } from 'angular-froala-wysiwyg';

@Component({
  selector: 'app-introduction',
  templateUrl: './introduction.component.html',
  styleUrl: './introduction.component.scss',
  imports: [RouterLink, DatePipe, FroalaViewModule],
})
export class IntroductionComponent {
  ethnicity: string;
  id: string = '';
  introduction!: {
    title: string;
    imgUrl: string;
    content: string;
    lastModificationTime: string;
  };

  constructor(
    private introductionService: IntroductionService,
    private activatedRoute: ActivatedRoute,
    private sanitizer: DomSanitizer,
    private getEthnicityService: GetEthnicityService,
    private utils: UtilsService
  ) {
    this.activatedRoute.queryParamMap.subscribe((queryParamMap) => {
      this.id = queryParamMap.get('id') as string;
      this.getIntroduction();
    });
    this.ethnicity = this.getEthnicityService.GetEthnicityName() as string;
  }

  getIntroduction() {
    this.introductionService.getIntroduction(this.id).subscribe({
      next: (resp: getIntroductionResp) => {
        this.utils.setTitle(`${this.ethnicity}-${resp.data.title}`);
        this.introduction = resp.data;
        this.introduction.content = resp.data.content;
      },
      error: () => {},
    });
  }
  back(event: Event) {
    event.preventDefault();
    history.back();
  }
}
