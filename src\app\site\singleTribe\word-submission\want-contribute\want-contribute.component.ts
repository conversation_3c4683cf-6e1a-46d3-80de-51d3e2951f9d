import {
  Component,
  Input,
  OnChanges,
  OnDestroy,
  OnInit,
  SimpleChanges,
} from '@angular/core';
import { MatTabChangeEvent, MatTabGroup } from '@angular/material/tabs';
import { WordSubmissionService } from '../../../../service/curl/word-submission.service';
import {
  addWantContributeReq,
  mainCategoryList,
  subCategoryList,
} from '../../../../interface/wordSubmission.interface';
import {
  FormArray,
  FormBuilder,
  FormControl,
  FormGroup,
  Validators,
  FormsModule,
  ReactiveFormsModule,
} from '@angular/forms';
import { defaultItem } from '../../../../interface/share.interface';
import { GetEthnicityService } from '../../../../service/utils/get-ethnicity.service';
import { forkJoin } from 'rxjs';
import { LanguageService } from '../../../../service/curl/language.service';
import { v4 as uuidv4 } from 'uuid';
import { SafeHtml } from '@angular/platform-browser';
import { ProcessingBlobFilesService } from '../../../../service/utils/processing-blob-files.service';
import { QuestionService } from '../../../../service/curl/question.service';
import { MatDialog } from '@angular/material/dialog';
import { RecordingDialogComponent } from '../../../../dialog/recording-dialog/recording-dialog.component';
import { ConfirmService } from '../../../../service/utils/confirm.service';
import { SpinnerService } from '../../../../service/utils/spinner.service';
import { apiStatus } from '../../../../enum/apiStatus.enum';
import { TipDialogComponent } from '../../../../dialog/tip-dialog/tip-dialog.component';
import { RouterLink } from '@angular/router';
import { MatIcon } from '@angular/material/icon';
@Component({
  selector: 'app-want-contribute',
  templateUrl: './want-contribute.component.html',
  styleUrl: './want-contribute.component.scss',
  imports: [FormsModule, ReactiveFormsModule, RouterLink, MatIcon],
})
export class WantContributeComponent implements OnInit, OnChanges, OnDestroy {
  @Input() tabGroup!: MatTabGroup;
  @Input() wantKnowValue: string = ''; // 用來接收傳遞的數據
  isChineseExplanation: boolean = false;
  isOriginalSentence: boolean = false;
  form: FormGroup;
  tribeId: string | null = null;
  tribeName: string | null = null;
  wordListDescriptionTip: string =
    '填寫說明:於小方格依詞項、詞根或詞綴，分別填入族語及其華語逐詞解釋。\n「+」號為増加小方格數量;同理」\n「-」號為減少小方格數量。\n解釋:\n排灣語sinanparavac a kakaigian，則填寫為【sinan:選為...】+【paravac:重要】+【a:連接詞】+【ka:Ca重疊】+【kaigi:開會】+an:處所標記】。';
  mainCategoryList: mainCategoryList[] = [];
  tempSubCategoryList: subCategoryList[] = [];
  subCategoryList: subCategoryList[] = [];
  dialectList: { id: string; name: string }[] = [];

  creationMethodList: { id: string; name: string; tip: string }[] = [];
  selectMethodList: string[] = [];
  sessionId: string = '';

  keyboardList: string[] = [];
  file!: File;

  img!: SafeHtml;
  audio!: string;
  isOther: boolean = false;

  private currentMediaElement: HTMLAudioElement | null = null;
  constructor(
    private wordSubmissionService: WordSubmissionService,
    private getEthnicityService: GetEthnicityService,
    private languageService: LanguageService,
    private questionService: QuestionService,
    private processingBlobFilesService: ProcessingBlobFilesService,
    private formBuilder: FormBuilder,
    private confirmService: ConfirmService,
    private matDialog: MatDialog,
    private spinnerService: SpinnerService
  ) {
    this.form = this.formBuilder.group({
      chineseExplanation: ['', Validators.required],
      dictionaryName: ['', Validators.required],
      mainCategoryId: [null],
      subCategoryId: [null],
      dialect: [null],
      creationByWordList: this.formBuilder.array([]),
      creationByWordListDescription: [],
      dictionaryNote: [],
      creationConcept: ['', Validators.required],
      originalSentence: ['', Validators.required],
      chineseSentence: ['', Validators.required],
      investor: ['', Validators.required],
      email: ['', Validators.required],
      captcha: ['', Validators.required],
    });
    this.addCreationByWordItem();
  }

  get creationByWordList(): FormArray {
    return this.form.get('creationByWordList') as FormArray;
  }

  ngOnInit(): void {
    this.form.reset();
    window.scrollTo({
      top: 0,
      behavior: 'smooth',
    });
    if (this.tabGroup) {
      this.tabGroup.selectedTabChange.subscribe((event: MatTabChangeEvent) => {
        if (event.index !== 1) {
          this.stopMusic();
        }
      });
    }

    this.getCaptcha();
    this.initialization();
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['wantKnowValue']) {
      // console.log(changes['wantKnowValue']);
    }
  }

  ngOnDestroy(): void {
    // 離開頁面時停止並移除播放器
    this.stopMusic();
  }

  initialization() {
    this.tribeId = this.getEthnicityService.GetEthnicityId();
    this.tribeName = this.getEthnicityService.GetEthnicityName();
    if (this.wantKnowValue) {
      this.form.patchValue({ chineseExplanation: this.wantKnowValue });
    }

    forkJoin({
      ethnicityLanguage: this.getEthnicityLanguage(),
      ethnicityKeyboard: this.getEthnicityKeyboard(),
      wantContributeCategoryList: this.getWantContributeCategoryList(),
      wantContributeMethodList: this.getWantContributeMethodList(),
    }).subscribe({
      next: (result) => {
        const ethnicityLanguage = result.ethnicityLanguage;
        const ethnicityKeyboard = result.ethnicityKeyboard;
        const wantContributeCategoryList = result.wantContributeCategoryList;
        const wantContributeMethodList = result.wantContributeMethodList;
        this.keyboardList = ethnicityKeyboard.data.symbolList;
        this.dialectList = ethnicityLanguage.data.items;
        this.creationMethodList = wantContributeMethodList.data.methodItems;
        const { mainCategoryList } = wantContributeCategoryList.data;
        this.mainCategoryList = mainCategoryList;
        this.tempSubCategoryList = mainCategoryList.flatMap(
          (mainCategoryList: mainCategoryList) =>
            mainCategoryList.subCategoryList.map(
              (subCategoryList: subCategoryList) => ({
                ...subCategoryList,
              })
            )
        );
      },
    });
  }

  getEthnicityLanguage() {
    return this.languageService.getEthnicityLanguage(this.tribeId as string);
  }

  getEthnicityKeyboard() {
    return this.languageService.getEthnicityKeyboard(this.tribeId as string);
  }

  getWantContributeCategoryList() {
    return this.wordSubmissionService.getWantContributeCategoryList();
  }
  getWantContributeMethodList() {
    return this.wordSubmissionService.getWantContributeMethodList();
  }

  clearInput(key: string) {
    this.form.patchValue({ [key]: '' });
  }

  keyUp(word: string, formName: string, inputElement: HTMLInputElement) {
    const currentKeyword = this.form.get(formName)?.value || '';
    const start = inputElement.selectionStart ?? currentKeyword.length;
    const end = inputElement.selectionEnd ?? currentKeyword.length;
    const newKeyword =
      currentKeyword.slice(0, start) + word + currentKeyword.slice(end);
    this.form.get(formName)?.patchValue(newKeyword);
    const cursorPos = start + word.length;
    setTimeout(() => {
      inputElement.focus();
      inputElement.setSelectionRange(cursorPos, cursorPos);
    });
  }

  keyboardUp(value: string, inputElement: HTMLInputElement) {
    const currentKeyword = this.form.value.keyword || '';
    const start = inputElement.selectionStart ?? currentKeyword.length;
    const end = inputElement.selectionEnd ?? currentKeyword.length;
    const newKeyword =
      currentKeyword.slice(0, start) + value + currentKeyword.slice(end);
    this.form.get('keyword')?.patchValue(newKeyword);
    const cursorPos = start + value.length;
    setTimeout(() => {
      inputElement.focus();
      inputElement.setSelectionRange(cursorPos, cursorPos);
    });
  }

  selectMainCategory() {
    this.form.patchValue({
      subCategoryId: null,
    });
    const selectedSubCategory = this.tempSubCategoryList.filter(
      (item) => item.mainCategoriesId === this.form.value.mainCategoryId
    );
    this.subCategoryList =
      selectedSubCategory.length > 0 ? selectedSubCategory : [];
  }

  addCreationByWordItem() {
    const newItem = this.formBuilder.group({
      id: [uuidv4()], // 產生唯一 ID
      inputValue: [], // 綁定輸入框的值
      isKeyboard: [false], // 控制鍵盤是否顯示
    });
    this.creationByWordList.push(newItem);
  }

  deleteCreationByWordItem(index: number) {
    this.creationByWordList.removeAt(index);
    this.splitCreationByWordList();
  }

  openKeyBoard(index: number) {
    const item = this.creationByWordList.at(index);
    const currentIsKeyboard = item.get('isKeyboard')?.value;
    item.patchValue({ isKeyboard: !currentIsKeyboard });
  }

  keyUpByWordList(index: number, key: string, inputElement: HTMLInputElement) {
    const item = this.creationByWordList.at(index) as FormGroup;
    const control = item.get('inputValue');

    const currentValue = control?.value || '';
    const start = inputElement.selectionStart ?? currentValue.length;
    const end = inputElement.selectionEnd ?? currentValue.length;

    // 插入文字在游標處
    const newValue =
      currentValue.slice(0, start) + key + currentValue.slice(end);
    control?.patchValue(newValue);

    // 將游標移到新插入字元之後
    const newCursorPos = start + key.length;
    setTimeout(() => {
      inputElement.focus();
      inputElement.setSelectionRange(newCursorPos, newCursorPos);
    });
  }

  getFormControl(index: number) {
    const item = this.creationByWordList.at(index) as FormGroup;
    return item.get('inputValue') as FormControl;
  }

  getInputValueLength(index: number) {
    const item = this.creationByWordList.at(index) as FormGroup;
    const inputValue = item.get('inputValue')?.value; // 取得 inputValue 的值
    return inputValue ? inputValue.length : 0; // 若 inputValue 存在則回傳長度，否則回傳 0
  }

  clearInputValue(index: number) {
    const item = this.creationByWordList.at(index) as FormGroup;
    item.get('inputValue')?.patchValue(''); // 清空 inputValue
  }

  /**
   * 取得checkbox
   * @param event
   * @param id
   */
  selectMethod(event: Event, id: string) {
    const target = event.target as HTMLInputElement;
    let selectStatus = target.checked;
    const index = this.selectMethodList.indexOf(id);
    if (selectStatus && index === -1) {
      this.selectMethodList.push(id);
    } else if (!selectStatus && index !== -1) {
      this.selectMethodList.splice(index, 1);
    }
  }

  openTip(tip: string) {
    this.matDialog.open(TipDialogComponent, {
      autoFocus: false,
      disableClose: true,
      data: {
        tip: tip,
      },
    });
  }

  selectOther(event: Event) {
    const target = event.target as HTMLInputElement;
    if (target.checked) {
      this.isOther = true;
      this.form.addControl('creationMethodOther', new FormControl(''));
    } else {
      this.isOther = false;
      this.form.removeControl('creationMethodOther');
    }
  }

  stopMusic() {
    if (this.currentMediaElement) {
      this.currentMediaElement.pause();
      this.currentMediaElement.remove();
      this.currentMediaElement = null;
    }
  }

  getCaptcha() {
    this.stopMusic();
    this.questionService.getCaptcha().subscribe({
      next: (resp: Blob) => {
        const reader = new FileReader();

        // 使用 FileReader 读取 Blob 数据
        reader.onload = () => {
          const data = reader.result as ArrayBuffer;

          // 转换为 Uint8Array 以便处理二进制数据
          const uint8Array = new Uint8Array(data);

          // 定义边界标记
          const boundary = new TextEncoder().encode('--file_boundary\r\n');

          // 分割多部分内容
          const parts = this.processingBlobFilesService.splitByBoundary(
            uint8Array,
            boundary
          );

          // 处理每一部分内容
          parts.forEach((part) => {
            const contentType =
              this.processingBlobFilesService.extractContentType(part);
            const contentData =
              this.processingBlobFilesService.extractContentData(part);

            if (contentType === 'image/png') {
              // 创建图片 Blob 并显示图片
              const imageBlob = new Blob([contentData], { type: 'image/png' });
              this.img =
                this.processingBlobFilesService.getSafeImageUrl(imageBlob);
            } else if (contentType === 'audio/mpeg') {
              // 创建音频 Blob 并播放音频
              const audioBlob = new Blob([contentData], { type: 'audio/mpeg' });
              this.audio = URL.createObjectURL(audioBlob);
            } else {
              // 嘗試解析 sessionId
              const sessionText = new TextDecoder().decode(contentData);
              if (
                sessionText.trim().startsWith('{') &&
                sessionText.trim().endsWith('}')
              ) {
                // 嘗試解析 JSON
                const json = JSON.parse(sessionText);
                if (json.sessionId) {
                  this.sessionId = json.sessionId;
                }
              }
            }
          });
        };

        reader.readAsArrayBuffer(resp);
      },
      error: () => {},
    });
  }

  play() {
    this.stopMusic();

    let mediaElement: HTMLAudioElement = document.createElement('audio');

    mediaElement.style.display = 'none'; // 這行讓音頻播放器隱藏
    mediaElement.setAttribute('src', this.audio); // 設置音頻源
    mediaElement.setAttribute('controls', 'true'); // 加入控制條
    document.body.appendChild(mediaElement);
    mediaElement.play();

    this.currentMediaElement = mediaElement; // 儲存當前的音樂播放器
  }

  openRecording() {
    if (!this.form.value.dictionaryName) {
      this.confirmService.showWARN('請先填寫族語創詞', '警告');
      return;
    }
    this.matDialog
      .open(RecordingDialogComponent, {
        autoFocus: false,
        disableClose: true,
        data: {
          title: this.form.value.dictionaryName,
        },
      })
      .afterClosed()
      .subscribe((result: { file: File }) => {
        this.file = result.file;
      });
  }

  splitCreationByWordList() {
    let creationByWordList = this.form.value.creationByWordList;
    let result: string = '';
    creationByWordList.map((item: { inputValue: string }) => {
      if (item.inputValue && item.inputValue.includes(':')) {
        result =
          result + (result ? '+' : '') + item.inputValue.split(':').pop();
      }
    });
    this.form.patchValue({ creationByWordListDescription: result });
  }

  send() {
    if (!this.form.valid) {
      this.confirmService.showWARN('必填欄位尚未填寫');
    }
    let req: addWantContributeReq = {
      dictionaryName: this.form.value.dictionaryName,
      chineseExplanation: this.form.value.chineseExplanation,
      audioFile: this.file,
      categoryId:
        this.form.value.mainCategoryId == null
          ? ''
          : this.form.value.mainCategoryId,
      subCategoryId: this.form.value.subCategoryId,
      tribeId: this.tribeId as string,
      dialectId: this.form.value.dialect == null ? '' : this.form.value.dialect,
      creationMethodId: this.selectMethodList,
      creationMethodOther: this.form.value.creationMethodOther,
      creationByWordList: [],
      email: this.form.value.email,
      name: this.form.value.investor,
      dictionaryNote: this.form.value.dictionaryNote,
      creationConcept: this.form.value.creationConcept,
      originalSentence: this.form.value.originalSentence,
      chineseSentence: this.form.value.chineseSentence,
      verifyCode: this.form.value.captcha,
      sessionId: this.sessionId,
    };

    let creationByWordList: string[] = this.form.value.creationByWordList.map(
      (item: { inputValue: string }) => item.inputValue
    );
    req.creationByWordList = creationByWordList;
    this.spinnerService.show();
    this.wordSubmissionService.addWantContribute(req).subscribe({
      next: (resp: defaultItem) => {
        this.spinnerService.hide();
        if (resp.status !== apiStatus.SUCCESS) {
          this.confirmService.showError(resp.message);
        } else {
          this.confirmService
            .showSUCCESS('投稿成功')
            .afterClosed()
            .subscribe(() => {
              this.tabGroup.selectedIndex = 3;
            });
        }
      },
      error: () => {
        this.spinnerService.hide();
      },
    });
  }
}
