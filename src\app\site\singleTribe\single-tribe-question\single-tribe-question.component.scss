.breadcrumb-layout {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
}

.breadcrumb-item {
    cursor: pointer;
}

.title {
    font-size: 1.8em;
    font-weight: bold;
    background-color: #4a7f42;
    color: white;
    margin: 1em 0;
    padding: 0.5em;
}
span {
    font-size: 1.2em;
}

.question-group {
    display: flex;
    flex-direction: column;
    justify-content: center;
    border: solid gray;
    margin: 1em 0;
    padding: 4em;
    .question-dec {
        display: flex;
        flex-direction: column;
        label {
            font-size: 1.4em;
            font-weight: bold;
        }
    }
    .question-input-box {
        span {
            span {
                color: red;
            }
            font-size: 1.3em;
        }
        display: flex;
        flex-direction: column;
    }
}

textarea {
    resize: vertical; /* 只允許垂直調整 */
    min-height: 200px; /* 最小高度 */
}

.captcha-box {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    mat-icon {
        cursor: pointer;
        padding: 20px 0.2em;
        font-size: 2em;
        color: green;
    }
}

.panel-header {
    height: 10vh !important;
    background-color: #ffeab5 !important;
}
.panel-header:hover {
    background-color: #ffeab5 !important;
}
.panel-header:focus {
    background-color: #ffeab5 !important;
}

mat-expansion-panel {
    margin-bottom: 1em;
}
