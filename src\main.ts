import { provideAnimationsAsync } from '@angular/platform-browser/animations/async';
import {
  HTTP_INTERCEPTORS,
  provideHttpClient,
  withInterceptorsFromDi,
} from '@angular/common/http';
import { HttpInterceptorService } from './app/service/interceptor/http-interceptor.service';
import { bootstrapApplication } from '@angular/platform-browser';

import { AppComponent } from './app/app.component';
import { provideRouter, withInMemoryScrolling } from '@angular/router';

import { SpinnerService } from './app/service/utils/spinner.service';
import { ConfirmService } from './app/service/utils/confirm.service';
import { routes } from './app/app-routing.module';
import { provideOAuthClient } from 'angular-oauth2-oidc';

bootstrapApplication(AppComponent, {
  providers: [
    provideRouter(
      routes,
      withInMemoryScrolling({
        anchorScrolling: 'enabled',
        scrollPositionRestoration: 'enabled',
      })
      // 如果需要視圖過渡動畫可以加上
      // withViewTransitions()
    ),
    provideAnimationsAsync(),
    // 注册拦截器依赖的服务
    ConfirmService,
    SpinnerService,
    provideHttpClient(withInterceptorsFromDi()), // 启用 DI 拦截器
    {
      provide: HTTP_INTERCEPTORS,
      useClass: HttpInterceptorService,
      multi: true,
    },
    provideOAuthClient(),
  ],
}).catch((err) => console.error(err));
