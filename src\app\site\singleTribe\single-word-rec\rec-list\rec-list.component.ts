import { Component } from '@angular/core';
import { Router } from '@angular/router';
import { GetEthnicityService } from '../../../../service/utils/get-ethnicity.service';
import { SingleWordRecService } from '../../../../service/curl/single-word-rec.service';
import {
  getRecListReq,
  getRecListResp,
  recommendItem,
} from '../../../../interface/single-word-rec.interface';
import { LanguageService } from '../../../../service/curl/language.service';
import { getEthnicityLanguageResp } from '../../../../interface/language.interface';
import { SpinnerService } from '../../../../service/utils/spinner.service';
import { FormsModule } from '@angular/forms';
import { PaginatorComponent } from '../../../../utils/paginator/paginator.component';
import { DatePipe } from '@angular/common';

@Component({
    selector: 'app-rec-list',
    templateUrl: './rec-list.component.html',
    styleUrl: './rec-list.component.scss',
    imports: [
        FormsModule,
        PaginatorComponent,
        DatePipe,
    ],
})
export class RecListComponent {
  tribeId: string | null = null;
  keyword: string = '';
  dialectId: string | null = null;
  status: number | null = null;
  tempStatus: number | null = 1;
  dialectList: { id: string; name: string }[] = [];

  recList: recommendItem[] = [];

  pageSize: number = 10; //一頁幾筆資料
  nowPage: number = 1;
  totalCount: number = 0; //總筆數
  pageShowCount: number = 5; //分頁器秀幾個

  list: {
    column: string;
    title: string;
    width: string;
    sort: boolean;
  }[] = [
    {
      column: 'index',
      title: '序號',
      width: '10%',
      sort: false,
    },
    {
      column: 'creationTime',
      title: '推薦日',
      width: '10%',
      sort: true,
    },
    {
      column: 'categoryName',
      title: '語別',
      width: '10%',
      sort: true,
    },
    {
      column: 'categoryName',
      title: '詞項',
      width: '10%',
      sort: true,
    },
    {
      column: 'function',
      title: '中文解釋',
      width: '',
      sort: false,
    },
    {
      column: 'function',
      title: '推薦者',
      width: '10%',
      sort: false,
    },
  ];

  constructor(
    private spinnerService: SpinnerService,
    private getEthnicityService: GetEthnicityService,
    private languageService: LanguageService,
    private singleWordRecService: SingleWordRecService
  ) {}

  ngOnInit(): void {
    window.scrollTo({
      top: 0,
      behavior: 'smooth',
    });
    this.tribeId = this.getEthnicityService.GetEthnicityId();
    this.getEthnicityLanguage();
  }

  search() {
    this.tempStatus = this.status;
    this.nowPage = 1;
    if (this.tempStatus === 0) {
      this.list.push({
        column: 'function',
        title: '未收錄原因',
        width: '20%',
        sort: false,
      });
    } else {
      this.list = this.list.filter((item) => item.title !== '未收錄原因');
    }
    this.getRecList();
  }

  getEthnicityLanguage() {
    return this.languageService
      .getEthnicityLanguage(this.tribeId as string)
      .subscribe({
        next: (resp: getEthnicityLanguageResp) => {
          this.dialectList = resp.data.items;
        },
      });
  }

  getRecList() {
    let req: getRecListReq = {
      page: this.nowPage,
      pageSize: this.pageSize,
      keyword: this.keyword,
      tribeId: this.tribeId as string,
      dialectId: this.dialectId,
      status: this.status,
    };
    this.spinnerService.show();
    this.singleWordRecService.getRecList(req).subscribe({
      next: (resp: getRecListResp) => {
        this.spinnerService.hide();
        this.recList = resp.data.recommendItems;
        this.totalCount = resp.data.itemTotalCount;
      },
      error: () => {},
    });
  }

  /**
   * 第幾頁
   * @param item number
   */
  getPageFromPaginator(nowPage: number) {
    this.nowPage = nowPage;
    this.getRecList();
  }
  /**
   * 每頁資料數量
   * @param item number
   */
  getPageSizeFromPaginator(pageSize: number) {
    this.pageSize = pageSize;
    this.nowPage = 1;
    this.getRecList();
  }

  back(event: Event) {
    event.preventDefault();
    history.back();
  }
}
