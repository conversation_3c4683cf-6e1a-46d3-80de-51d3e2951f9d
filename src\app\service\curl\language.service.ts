import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import {
  getAdvanceSearchResp,
  getAnaphoraDetailResp,
  getDialectAndLanguageListResp,
  getEthnicityCategoryResp,
  getEthnicityKeyboardResp,
  getEthnicityLanguageResp,
  getMarkListResp,
  getRootStructureResp,
  getShareDetailResp,
  getWordCommentResp,
  getWordMarkResp,
  searchDictionaryDetailResp,
  searchDictionaryReq,
  searchDictionaryResp,
  searchExampleResp,
} from '../../interface/language.interface';
import { SearhModel } from '../../enum/SearhModel .enum';
import { v7 } from 'uuid';

@Injectable({
  providedIn: 'root',
})
export class LanguageService {
  constructor(private httpClient: HttpClient) {}
  /**
   * 取得族語及方言列表
   * @returns
   */
  getDialectAndLanguageList(): Observable<getDialectAndLanguageListResp> {
    return this.httpClient.get<getDialectAndLanguageListResp>(
      'api/app/dictionary/filter-tribe-dialect'
    );
  }

  /**
   * 辭典搜尋
   * @param req
   * @returns
   */
  searchDictionary(
    req: searchDictionaryReq,
    searchType: SearhModel
  ): Observable<searchDictionaryResp> {
    let url: string;
    if (searchType === SearhModel.Accurate) {
      url = 'api/app/dictionary/search';
    } else {
      url = 'api/app/dictionary/search-fuzzy';
    }
    return this.httpClient.post<searchDictionaryResp>(url, req);
  }

  /**
   * 相關例句搜尋
   * @param req searchDictionaryReq
   * @returns searchExampleResp
   */
  searchExample(req: searchDictionaryReq): Observable<searchExampleResp> {
    return this.httpClient.post<searchExampleResp>(
      'api/app/dictionary/search-relate-sentence',
      req
    );
  }

  /**
   * 辭典搜尋詳細
   * @param id
   * @returns
   */
  searchDictionaryDetail(id: string): Observable<searchDictionaryDetailResp> {
    return this.httpClient.post<searchDictionaryDetailResp>(
      'api/app/dictionary/get-detail',
      { dictionaryId: id }
    );
  }

  /**
   * 取得族別虛擬鍵盤
   * @param tribeId
   * @returns
   */
  getEthnicityKeyboard(
    tribeId: string | null
  ): Observable<getEthnicityKeyboardResp> {
    return this.httpClient.post<getEthnicityKeyboardResp>(
      'api/app/dictionary/get-vistual-keyboard',
      {
        tribeId: tribeId,
      }
    );
  }

  /**
   * 取得族別收錄來源
   * @param tribeId
   * @returns
   */
  getEthnicitySource(tribeId: string): Observable<getEthnicityKeyboardResp> {
    return this.httpClient.post<getEthnicityKeyboardResp>(
      'api/app/dictionary/source',
      {
        tribeId: tribeId,
      }
    );
  }

  /**
   * 取得族別方言
   * @param tribeId
   * @returns
   */
  getEthnicityLanguage(tribeId: string): Observable<getEthnicityLanguageResp> {
    return this.httpClient.post<getEthnicityLanguageResp>(
      'api/app/dictionary/get-dialect',
      {
        tribeId: tribeId,
      }
    );
  }

  /**
   * 取得族別類別(範疇)
   * @param tribeId
   * @returns
   */
  getEthnicityCategory(tribeId: string): Observable<getEthnicityCategoryResp> {
    return this.httpClient.post<getEthnicityCategoryResp>(
      'api/app/dictionary/get-category',
      {
        tribeId: tribeId,
      }
    );
  }

  /**
   * 取得族別進階搜尋
   * @param tribeId
   * @returns
   */
  getAdvanceSearch(tribeId: string): Observable<getAdvanceSearchResp> {
    return this.httpClient.post<getAdvanceSearchResp>(
      'api/app/dictionary/get-advance-search',
      {
        tribeId: tribeId,
      }
    );
  }

  getAnaphoraDetail(id: string) {
    return this.httpClient.post<getAnaphoraDetailResp>(
      'api/app/dictionary/get-anaphora-detail',
      {
        dictionaryId: id,
      }
    );
  }

  getShareDetail(id: string): Observable<getShareDetailResp> {
    return this.httpClient.post<getShareDetailResp>(
      'api/app/dictionary/get-share-detail',
      {
        dictionaryId: id,
      }
    );
  }

  /**
   * 取得單族逐詞註解
   * @param id
   */
  getWordComment(id: string, content: string): Observable<getWordCommentResp> {
    return this.httpClient.post<getWordCommentResp>(
      'api/app/dictionary/get-word-annotation',
      {
        tribeId: id,
        content: content,
      }
    );
  }

  /**
   * 取得書寫符號
   * @param tribeId
   * @returns
   */
  getMarkList(tribeId: string): Observable<getMarkListResp> {
    return this.httpClient.post<getMarkListResp>(
      'api/app/dictionary/get-symbol',
      {
        tribeId: tribeId,
      }
    );
  }

  /**
   * 取得詞項列表符號
   * @param tribeId
   * @param symbolId
   * @returns
   */
  getWordList(tribeId: string, symbolId: string): Observable<getWordMarkResp> {
    return this.httpClient.post<getWordMarkResp>(
      'api/app/dictionary/get-list-by-symbol',
      {
        tribeId: tribeId,
        symbolId: symbolId,
      }
    );
  }

  /**
   * 取得詞根結構
   * @param dictionaryId
   * @returns
   */
  getRootStructure(dictionaryId: string): Observable<getRootStructureResp> {
    return this.httpClient.post<getRootStructureResp>(
      'api/app/dictionary/get-root-structure',
      {
        dictionaryId: dictionaryId,
      }
    );
  }
}
