import { Component, OnInit } from '@angular/core';
import { HeaderComponent } from '../../common/header/header.component';
import { RouterLink, RouterOutlet } from '@angular/router';
import { FooterComponent } from '../../common/footer/footer.component';

@Component({
    selector: 'app-index',
    templateUrl: './index.component.html',
    styleUrl: './index.component.scss',
    imports: [
        HeaderComponent,
        RouterLink,
        RouterOutlet,
        FooterComponent,
    ],
})
export class IndexComponent implements OnInit {
  ngOnInit(): void {
    sessionStorage.removeItem('singleTribe');
  }
}
