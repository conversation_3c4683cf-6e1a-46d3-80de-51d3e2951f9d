import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { MatTabGroup } from '@angular/material/tabs';
import { LineAuthService } from '../../../../service/utils/line-auth.service';
import { ActivatedRoute } from '@angular/router';
import { WordSubmissionService } from '../../../../service/curl/word-submission.service';
import { GetEthnicityService } from '../../../../service/utils/get-ethnicity.service';
import { environment } from '../../../../../environments/environment';
import {
  getWantToKnowListResp,
  sendWantToKnowReq,
} from '../../../../interface/wordSubmission.interface';
import {
  FormBuilder,
  FormGroup,
  Validators,
  FormsModule,
  ReactiveFormsModule,
} from '@angular/forms';
import { defaultItem } from '../../../../interface/share.interface';
import { ConfirmService } from '../../../../service/utils/confirm.service';

@Component({
  standalone: true,
  selector: 'app-want-know',
  templateUrl: './want-know.component.html',
  styleUrl: './want-know.component.scss',
  imports: [FormsModule, ReactiveFormsModule],
})
export class WantKnowComponent implements OnInit {
  @Input() tabGroup!: MatTabGroup;
  @Output() wantKnowValue = new EventEmitter<string>(); // 假設傳送的是一個字串
  form: FormGroup;
  redirectUri: string = `${environment.sitePath}/wordSubmission?tab=2`;
  tribeId: string | null = null;
  wantToKnowList: string[] = [];

  constructor(
    private lineAuthService: LineAuthService,
    private activatedRoute: ActivatedRoute,
    private wordSubmissionService: WordSubmissionService,
    private getEthnicityService: GetEthnicityService,
    private confirmService: ConfirmService,
    private formBuilder: FormBuilder
  ) {
    this.activatedRoute.queryParamMap.subscribe((queryParamMap) => {
      if (queryParamMap.get('code')) {
        let code: string = queryParamMap.get('code') as string;
        this.getLineData(code);
      }
    });
    this.form = this.formBuilder.group({
      content: ['', Validators.required],
      name: [''],
      email: [''],
    });
  }

  ngOnInit(): void {
    window.scrollTo({
      top: 0,
      behavior: 'smooth',
    });
    this.tribeId = this.getEthnicityService.GetEthnicityId();
    this.getWantToKnowList();
  }

  getWantToKnowList() {
    this.wordSubmissionService
      .getWantToKnowList(this.tribeId as string)
      .subscribe({
        next: (resp: getWantToKnowListResp) => {
          this.wantToKnowList = resp.data.wantToKnowItems;
        },
        error: () => {},
      });
  }

  wantKnow(item: string) {
    this.wantKnowValue.emit(item);
    this.tabGroup.selectedIndex = 1;
  }

  sendAnonymously() {
    if (!this.form.valid) {
      this.confirmService.showWARN('必填欄位尚未填寫', '警告');
      return;
    }
    let req: sendWantToKnowReq = {
      tribeId: this.tribeId as string,
      wantToKnowName: this.form.value.content,
    };
    this.sendWantToKnow(req);
  }

  send() {
    if (!this.form.valid || !this.form.value.name || !this.form.value.email) {
      this.confirmService.showWARN('必填欄位尚未填寫', '警告');
      return;
    }
    let req: sendWantToKnowReq = {
      tribeId: this.tribeId as string,
      wantToKnowName: this.form.value.content,
      name: this.form.value.name,
      email: this.form.value.email,
    };
    this.sendWantToKnow(req);
  }

  sendWantToKnow(req: sendWantToKnowReq) {
    this.wordSubmissionService.sendWantToKnow(req).subscribe({
      next: (resp: defaultItem) => {
        this.confirmService
          .showSUCCESS('投稿成功', '完成')
          .afterClosed()
          .subscribe(() => {
            this.form.reset();
            this.getWantToKnowList();
          });
      },
    });
  }

  login() {
    this.lineAuthService.login(this.redirectUri);
  }

  getLineData(code: string) {
    this.lineAuthService
      .getAccessToken(code, this.redirectUri)
      .subscribe((tokenResponse: any) => {
        const accessToken = tokenResponse.access_token;
        this.lineAuthService
          .getUserInfo(accessToken)
          .subscribe((userInfo: any) => {
            console.log('User Info:', userInfo.displayName);
            console.log('User Info:', userInfo.userId);
          });
      });
  }
}
