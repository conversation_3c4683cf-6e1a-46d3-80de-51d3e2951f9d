import { Component, <PERSON><PERSON><PERSON>roy, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { ConfirmService } from '../../../service/utils/confirm.service';
import { LanguageService } from '../../../service/curl/language.service';
import { HttpErrorResponse } from '@angular/common/http';
import {
  audioItem,
  dictionaryItem,
  getShareDetailResp,
  shareDetailWordItem,
  wordItem,
} from '../../../interface/language.interface';
import { AnaphoraSentenceDialogComponent } from '../../../dialog/anaphora-sentence-dialog/anaphora-sentence-dialog.component';
import { MatDialog } from '@angular/material/dialog';
import { environment } from '../../../../environments/environment';
import { FileService } from '../../../service/curl/file.service';
import { OwlOptions, CarouselModule } from 'ngx-owl-carousel-o';
import { RootStructureDialogComponent } from '../../../dialog/root-structure-dialog/root-structure-dialog.component';
import { UtilsService } from '../../../service/utils/utils.service';
import {
  MatAccordion,
  MatExpansionPanel,
  MatExpansionPanelHeader,
} from '@angular/material/expansion';
import { NgClass } from '@angular/common';

export enum ShareType {
  FB = 'fb',
  LINE = 'line',
  IG = 'ig',
}

declare const FB: any;

@Component({
  selector: 'app-share-page',
  templateUrl: './share-page.component.html',
  styleUrl: './share-page.component.scss',
  imports: [
    MatAccordion,
    MatExpansionPanel,
    MatExpansionPanelHeader,
    NgClass,
    CarouselModule,
  ],
})
export class SharePageComponent implements OnInit, OnDestroy {
  id: string = '';
  wordItem: shareDetailWordItem = {
    audioItems: [],
    id: '',
    name: '',
    pinyin: '',
    variant: '',
    formationWord: '',
    derivativeRoot: '',
    frequency: 0,
    hit: 0,
    dictionaryNote: '',
    sources: [],
    explanationItems: [],
    dialect: '',
    tribe: '',
    tribeId: '',
    isDerivativeRoot: false,
  };
  shareType = ShareType;

  customOptions: OwlOptions = {
    loop: false,
    navSpeed: 700,
    dots: true,
    items: 1,
    center: true,
  };
  private currentMediaElement: HTMLAudioElement | null = null;
  isImage: boolean = false;
  isDerivativeRoot: boolean = false;

  fontSize: number = 1;

  constructor(
    private activatedRoute: ActivatedRoute,
    private router: Router,
    private matDialog: MatDialog,
    private confirmService: ConfirmService,
    private languageService: LanguageService,
    private fileService: FileService,
    private utils: UtilsService
  ) {
    this.utils.fontSize$.subscribe((size) => {
      this.fontSize = size === 0 ? 1 : size;
    });
    this.activatedRoute.queryParamMap.subscribe((queryParams) => {
      this.id = queryParams.get('id') as string;
    });
  }

  ngOnInit(): void {
    this.utils.setTitle('分享');
    if (!this.id) {
      this.confirmService
        .showError('詞語不存在', '錯誤')
        .afterClosed()
        .subscribe(() => {
          this.router.navigate(['home']);
        });
    } else {
      this.getWorItem(this.id);
    }
  }

  ngOnDestroy(): void {
    this.stopMusic();
  }

  getWorItem(id: string) {
    this.languageService.getShareDetail(id).subscribe({
      next: (resp: getShareDetailResp) => {
        this.isImage = resp.data.isImage;
        this.isDerivativeRoot = resp.data.isDerivativeRoot;
        this.wordItem = resp.data.word;
      },
      error: (err: HttpErrorResponse) => {},
    });
  }

  onPanelOpened(item: dictionaryItem) {}

  onPanelClose(item: dictionaryItem) {
    item.isOpenPanel = false;
  }

  clickAnaphoraSentence(id: string | null) {
    if (id === null) {
      return;
    }
    this,
      this.matDialog.open(AnaphoraSentenceDialogComponent, {
        disableClose: true,
        autoFocus: false,
        width: '60%',
        data: {
          id: id,
        },
      });
  }

  share(type: ShareType, id: string) {
    const shareUrl = `${environment.sitePath}/sharePage?id=${id}`;
    switch (type) {
      case this.shareType.FB:
        FB.ui(
          {
            method: 'share',
            href: shareUrl,
          },
          (response: any) => {
            if (response && !response.error_message) {
              console.log('分享成功');
            } else {
              console.error('分享失敗或取消', response);
            }
          }
        );
        break;
      case this.shareType.LINE:
        window.open(
          `https://social-plugins.line.me/lineit/share?url=${environment.sitePath}/sharePage?id=${id}`,
          '_blank',
          'noopener,noreferrer'
        );
        break;
      case this.shareType.IG:
        window.open('https://example.com', '_blank', 'noopener,noreferrer');
        break;
    }
  }
  question(item: wordItem) {
    window.open(
      `${environment.sitePath}/singleQuestion?id=${item.id}&tribeId=${item.tribeId}`,
      '_blank'
    );
  }

  getRootStructure(dictionaryId: string) {
    this.matDialog.open(RootStructureDialogComponent, {
      disableClose: true,
      autoFocus: false,
      width: '60%',
      data: {
        dictionaryId: dictionaryId,
      },
    });
  }

  play(item: audioItem) {
    if (this.currentMediaElement) {
      this.currentMediaElement.pause();
      this.currentMediaElement.remove();
    }
    this.fileService.getAudioFile(item.fileId).subscribe({
      next: (resp: string) => {
        let mediaElement: HTMLAudioElement = document.createElement('audio');
        mediaElement.style.display = 'none'; // 這行讓音頻播放器隱藏
        mediaElement.setAttribute('src', resp); // 設置音頻源
        mediaElement.setAttribute('controls', 'true'); // 加入控制條
        document.body.appendChild(mediaElement);
        mediaElement.play();
        mediaElement.addEventListener('ended', () => {
          this.currentMediaElement = null; // 重置當前音樂播放器
        });

        this.currentMediaElement = mediaElement; // 儲存當前的音樂播放器
      },
      error: () => {},
    });
  }

  stopMusic() {
    if (this.currentMediaElement) {
      this.currentMediaElement.pause();
      this.currentMediaElement.remove();
      this.currentMediaElement = null;
    }
  }

  onCopy(event: ClipboardEvent) {
    event.preventDefault(); // 阻止預設複製行為
    const selection = window.getSelection();
    if (selection) {
      let copiedText = selection.toString().replace(/\r?\n/g, ' '); // 移除換行
      event.clipboardData?.setData('text/plain', copiedText);
    }
  }

  openImage(item: shareDetailWordItem) {
    item.explanationItems.map((item) => {
      return (item.isImage = !item.isImage);
    });
  }
}
