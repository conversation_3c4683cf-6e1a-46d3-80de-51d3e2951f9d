<main class="master-pages-container-layout">
    <div class="master-pages-container-cont">
        <div class="cont pages-cont-layout">
            <div class="pages-cont-list-layout">

                <!--路徑列-->
                <div class="breadcrumb-layout">
                    <div>
                        <nav class="breadcrumb-cont">
                            <ol class="breadcrumb">
                                <li class="breadcrumb-item">
                                    <a href="" (click)="$event.defaultPrevented" [routerLink]="'/home'">
                                        <span>首頁</span>
                                        <span class="material-symbols-outlined">keyboard_arrow_right</span>
                                    </a>
                                </li>
                                <li class="breadcrumb-item">
                                    <a href="" (click)="$event.defaultPrevented">
                                        <span>{{ethnicity}}</span>
                                        <span class="material-symbols-outlined">keyboard_arrow_right</span>
                                    </a>
                                </li>
                                <li class="breadcrumb-item active">查詢下載</li>
                            </ol>
                        </nav>
                    </div>
                    <div>
                        <nav class="breadcrumb-cont">
                            <ol class="breadcrumb">
                                <li class="breadcrumb-item" style="margin-right: -24px;">
                                    <a hre="" (click)="back($event)">
                                        <span>&lt;&lt;回上一頁</span>
                                    </a>
                                </li>
                            </ol>
                        </nav>
                    </div>
                </div>

                <div class="word-description-container">
                    <div class="advanced-container">
                        <main class="second-search-layout">
                            <div class="search-group">
                                <div class="search-all" #searchAll>
                                    <div class="search-box">
                                        <input class="search-bar font_18" type="text" title="keyword"
                                            placeholder="請輸入族語或中文" [(ngModel)]="keyword"
                                            (ngModelChange)="onKeywordChange($event)"
                                            (keydown.enter)="search(); keywordInput.blur()" #keywordInput>
                                        <a class="search-a1" href=""
                                            (click)="$event.preventDefault();keyword='';autoInputList=[];">
                                            <span class="material-symbols-outlined">close</span>
                                        </a>
                                    </div>
                                    <!--搜尋框架-->
                                    @if(autoInputList.length>0){
                                    <div class="search-frame">
                                        @for (value of autoInputList; track value) {
                                        <div class="search-frame-info" (click)="selectValue(value)">
                                            <span class="search-frame-name">{{value}}</span>
                                        </div>
                                        }
                                    </div>
                                    }
                                </div>
                                <div class="btns">
                                    <input class="btn-box btn-primary-color" value="搜尋" type="button"
                                        (click)="search()">
                                </div>
                            </div>
                            <!--鍵盤-->
                            <div class="keyboard-group">
                                <div class="keyboard-box">
                                    @for (key of keyboardList; track key) {
                                    <button class="keyboard-btn" (click)="keyboardUp(key,keywordInput)">
                                        <span class="keyboard_font">{{key}}</span>
                                    </button>
                                    }
                                </div>
                            </div>

                            <div class="second-search-list">
                                <div class="second-search-box">
                                    <div class="second-search-title" (click)="isDialect=!isDialect">
                                        <div class="second-search-h4 font_w">請選擇語別 (可多選)</div>
                                    </div>
                                    @if(isDialect){
                                    <mat-icon class="material-symbols-outlined font_w"
                                        (click)="isDialect=false">keyboard_arrow_down</mat-icon>
                                    }@else{
                                    <mat-icon class="isDialect-symbols-outlined font_w"
                                        (click)="isDialect=true">keyboard_arrow_up</mat-icon>
                                    }
                                </div>
                                @if(isDialect){
                                <div class="second-search-box2">
                                    <div class="advanced-search-cont-layout">
                                        <ul class="advanced-search-cont">
                                            @if(dialectList.length>0){
                                            @for ( item of dialectList; let i=$index; track i) {
                                            <li class="advanced-search-item">
                                                <span class="checkbox-list">
                                                    <input id="dialectCheckbox{{i}}" type="checkbox" [value]="item.id"
                                                        [(ngModel)]="item.selected" (change)="dialectListChange(item)">
                                                    <label for="dialectCheckbox{{i}}">{{item.name}}</label>
                                                </span>
                                            </li>
                                            }
                                            }@else{
                                            <div style="width: 100%;text-align: center;">
                                                <span>無語別</span>
                                            </div>
                                            }
                                        </ul>
                                    </div>
                                </div>
                                }
                            </div>

                            <!--下拉選單3 -->
                            <div class="second-search-list">
                                <div class="second-search-box">
                                    <div class="second-search-title" (click)="isAdvanced=!isAdvanced">
                                        <div class="second-search-h4 font_w">進階搜尋</div>
                                    </div>
                                    @if(isAdvanced){
                                    <mat-icon class="material-symbols-outlined font_w"
                                        (click)="isAdvanced=false">keyboard_arrow_down</mat-icon>
                                    }@else{
                                    <mat-icon class="isDialect-symbols-outlined font_w"
                                        (click)="isAdvanced=true">keyboard_arrow_up</mat-icon>
                                    }
                                </div>
                                @if(isAdvanced){
                                <div class="second-search-box2">
                                    <div class="input-group">
                                        <span class="second-search-item">
                                            書寫符號
                                        </span>
                                        <div class="select-group">
                                            <select title="symbolStart" class="select-box" [(ngModel)]="symbolStart">
                                                @for (option of symbolList; track option) {
                                                <option [ngValue]="option.id">{{option.name}}</option>
                                                }
                                            </select>&nbsp;<span class="font_to">~</span>&nbsp;
                                            <select title="symbolEnd" class="select-box" [(ngModel)]="symbolEnd">
                                                @for (option of symbolList; track option) {
                                                <option [ngValue]="option.id">{{option.name}}</option>
                                                }
                                            </select>
                                        </div>
                                    </div>
                                    <div class="input-group">
                                        <span class="second-search-item">
                                            範疇分類
                                        </span>
                                        <select title="category" [(ngModel)]="category">
                                            @for (option of categoryList; track option) {
                                            <option [ngValue]="option.id">{{option.name}}</option>
                                            }
                                        </select>
                                    </div>
                                    <div class="input-group">
                                        <span class="second-search-item">
                                            詞類
                                        </span>
                                        <select title="partOfSpeech" [(ngModel)]="partOfSpeech">
                                            @for (option of partOfSpeechList; track option) {
                                            <option [ngValue]="option.id">{{option.name}}</option>
                                            }
                                        </select>
                                    </div>
                                    <div class="input-group">
                                        <span class="second-search-item">
                                            僅搜尋
                                        </span>
                                        <select title="restrictSearch" [(ngModel)]="restrictSearch">
                                            @for (option of restrictSearchList; track option) {
                                            <option [ngValue]="option.value">{{option.name}}</option>
                                            }
                                        </select>
                                    </div>
                                    <div class="input-group">
                                        <span class="second-search-item">
                                            收錄來源
                                        </span>
                                        <div class="checkbox-group">
                                            <ul class="advanced-search-cont">
                                                @for ( item of sourceList; let i=$index; track i) {
                                                <li class="advanced-search-item">
                                                    <span class="checkbox-list">
                                                        <input id="sourceCheckbox{{i}}" type="checkbox"
                                                            [value]="item.id" [(ngModel)]="item.selected"
                                                            (change)="sourceListChange(item)">
                                                        <label for="sourceCheckbox{{i}}">{{item.name}}</label>
                                                    </span>
                                                </li>
                                                }
                                            </ul>
                                        </div>
                                    </div>
                                </div>
                                }
                            </div>

                        </main>
                    </div>
                    <div class="word-description-list-layout">
                        <div style="width: 100%;">
                            <main class="query-download-layout">
                                <div class="query-download-title">
                                    <span class="query-download-item">
                                        精準查詢條件：所有族語「{{showKeyword}}」 之查詢結果 (共{{totalCount}}筆)
                                    </span>
                                    <input class="btn-list btn-primary-color" value="下載" type="button"
                                        (click)="download()">
                                </div>
                                <!--表格1-->
                                <div>
                                    <table class="table-list-layout table-list-style rwd-table01">
                                        <tbody>
                                            <tr class="th-no">
                                                <th style="width: 13%;">
                                                    <label>
                                                        <input class="checkbox-list" type="checkbox"
                                                            (change)="isAll($event)">全選
                                                    </label>
                                                </th>
                                                <th style="width: 25%;">詞項</th>
                                                <th>中文</th>
                                            </tr>
                                            @if(queryDownloadList.length>0){
                                            @for (item of queryDownloadList; track item) {
                                            <tr>
                                                <td class="text_c">
                                                    <span class="rwd-th">項次</span>
                                                    <input class="checkbox-list" type="checkbox"
                                                        [checked]="checkSelect(item.dictionaryId)"
                                                        (change)="changeSelect($event, item.dictionaryId)">
                                                </td>
                                                <td class="text_c">
                                                    <span class="rwd-th">詞項</span>
                                                    {{item.word}}
                                                </td>
                                                <td class="text_l">
                                                    <span class="rwd-th">中文</span>
                                                    @for (chineseExplanationItem of item.chineseExplanation; let
                                                    last=$last;track
                                                    chineseExplanationItem) {
                                                    {{chineseExplanationItem}}
                                                    <span *ngIf="!last">&nbsp;,&nbsp;</span>
                                                    }
                                                </td>
                                            </tr>
                                            }
                                            }@else{
                                            <tr>
                                                <td colspan="3" style="text-align: center;">沒有找到符合條件的資料</td>
                                            </tr>
                                            }


                                        </tbody>
                                    </table>
                                </div>
                                @if(queryDownloadList.length>0){
                                <app-paginator [pageSize]="pageSize" [totalRecords]="totalCount"
                                    [pageShowCount]="pageShowCount" [nowPage]="nowPage"
                                    currentPageReportTemplate="第 {first} 到 {last} 筆，共 {totalRecords} 筆"
                                    (clickPageEvent)="getPageFromPaginator($event)"
                                    (pageSizeChangeEvent)="getPageSizeFromPaginator($event)"></app-paginator>
                                }
                            </main>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</main>