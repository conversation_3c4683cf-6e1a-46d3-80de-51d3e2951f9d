<form [formGroup]="form">
    <main class="want-contribute-layout">
        <div class="want-contribute-cont">
            <p>創詞是因應語言與社會歷經古早至現代發展與演變，將傳統族語以新事物、新概念加以詮釋，創造嶄新的溝通方式，讓族語對應文化和社會發展同步躍進。歡迎熱心貢獻、提出寶貴建議！所貢獻的創詞族語單詞一經本會採納，將收錄至本辭典並且列入族語貢獻者名單。
            </p>
        </div>
        <div class="input-group">
            <span class="want-contribute-item">
                <span class="font_r">*</span>華語創詞
            </span>
            <div class="input-list">
                <div class="search-box">
                    <input type="text" title="chineseExplanation" placeholder="請輸入華語，例：銀行" class="form-control"
                        style="width: 100%;padding-right: 45px;" formControlName="chineseExplanation"
                        [readonly]="wantKnowValue" #inputWantKnowValue>
                    @if (!wantKnowValue && form.value.chineseExplanation?.length > 0) {
                    <a class="search-a1" href="" (click)="$event.preventDefault()">
                        <span class="material-symbols-outlined" (click)="clearInput('chineseExplanation')">close</span>
                    </a>
                    }

                </div>
                <input class="btn-list btn-primary-color keyboard2" value="虛擬鍵盤" type="button"
                    (click)="isChineseExplanation=!isChineseExplanation">
            </div>
            @if(isChineseExplanation){
            <div class="keyboard-group2">
                <div class="keyboard-box">
                    @for (item of keyboardList; track item) {
                    <button class="keyboard-btn" type="button"
                        (click)="keyUp(item,'chineseExplanation',inputWantKnowValue)" [disabled]="wantKnowValue">
                        <span class="keyboard_font">{{item}}</span>
                    </button>
                    }
                </div>
            </div>
            }
        </div>
        <div class="input-group">
            <span class="want-contribute-item">
                <span class="font_r">*</span>族語創詞
            </span>
            <div class="input-list">
                <input type="text" title="dictionaryName" placeholder="請輸入族語拼寫，例：sskwan pila" class="form-control"
                    style="width: 100%;" formControlName="dictionaryName">
                <input class="btn-list btn-primary-color" value="參考歷年新詞" type="button" [routerLink]="'/newWord'"
                    [queryParams]="{'tribeId':tribeId,'tribeName':tribeName}">
                <input class="btn-list btn-primary-color" value="錄音" type="button" (click)="openRecording()">
            </div>
        </div>
        <div class="input-group">
            <span class="want-contribute-item">
                主類別
            </span>
            <select title="mainCategoryId" formControlName="mainCategoryId" (change)="selectMainCategory()">
                <option [ngValue]="null" disabled>請選擇主類別</option>
                @for (item of mainCategoryList; track item) {
                <option [value]="item.id">{{item.name}}</option>
                }
            </select>
        </div>
        <div class="input-group">
            <span class="want-contribute-item">
                次類別
            </span>
            <select title="subCategoryId" formControlName="subCategoryId">
                <option [ngValue]="null" disabled selected>請選擇次類別</option>
                <option [ngValue]="null" selected>不明</option>
                @for (item of subCategoryList; track item) {
                <option [value]="item.id">{{item.name}}</option>
                }
            </select>
        </div>
        <div class="input-group">
            <span class="want-contribute-item">
                語別
            </span>
            <select title="dialect" formControlName="dialect">
                <option [ngValue]="null" disabled>請選語別</option>
                @for (option of dialectList; track option) {
                <option [ngValue]="option.id">{{option.name}}</option>
                }
            </select>
        </div>
        <div class="input-group">
            <span class="want-contribute-item">
                <span class="font_r">*</span>創詞方式
            </span>
            <div class="checkbox-group">
                @for (item of creationMethodList;let i=$index; track item) {
                <ul class="checkbox-menu">
                    <input class="checkbox-list" type="checkbox" id="languageCheckbox{{i}}"
                        (change)="selectMethod($event,item.id)">
                    <label for="languageCheckbox{{i}}">{{item.name}}</label>
                    <div class="material-symbols-outlined tip-btn" (click)="openTip(item.tip)">help</div>
                </ul>
                }
            </div>
            <div class="checkbox-group2">
                <ul>
                    <input id="other" class="checkbox-list" type="checkbox" (change)="selectOther($event)"><label
                        for="other">其他</label>
                </ul>
                <div class="input-list">
                    <input type="text" title="creationMethodOther" placeholder="請輸入其他" class="form-control"
                        style="width: 100%;" formControlName="creationMethodOther">
                </div>
            </div>
            <div class="input-group">
                <span class="want-contribute-item">
                    <span class="font_r">*</span>創詞說明（華語逐詞解釋）
                    <div class="material-symbols-outlined tip-btn" (click)="openTip(wordListDescriptionTip)">help</div>
                </span>
                <div class="input-list">
                    <input type="text" title="creationByWordListDescription" placeholder="請輸入華語逐詞解釋"
                        class="form-control" style="width: 100%;" readonly
                        formControlName="creationByWordListDescription">
                </div>
            </div>
            <div class="input-group">
                <span class="want-contribute-item">
                    （可分開儲存詞綴/詞根/衍生詞，並輸入對應中文以利比對）
                </span>
                @for (item of creationByWordList.controls ;let index=$index; track item) {
                <div class="input-list">
                    <div class="search-box">
                        <input type="text" title="creationByWord" placeholder="請輸入對應中文" class="form-control"
                            style="width: 100%; padding-right: 45px;" [formControl]="getFormControl(index)"
                            (blur)="splitCreationByWordList()" #inputRef>
                        @if(getInputValueLength(index)>0){
                        <a class="search-a1" href="" (click)="$event.preventDefault()"><span
                                class="material-symbols-outlined" (click)="clearInputValue(index)">close</span></a>
                        }
                    </div>
                    <input class="btn-list btn-primary-color keyboard2" value="虛擬鍵盤" type="button"
                        (click)="openKeyBoard(index)">
                    @if(index===0){
                    <input class="btn-list btn-primary-color" value="新增" type="button"
                        style="margin: 20px 0px 20px 10px;" (click)="addCreationByWordItem()">
                    }@else{
                    <input class="btn-list btn-danger-solid" value="刪除" type="button"
                        style="margin: 20px 0px 20px 10px;" (click)="deleteCreationByWordItem(index)">
                    }
                </div>
                @if(item.value.isKeyboard){
                <div class="keyboard-group2">
                    <div class="keyboard-box">
                        @for (key of keyboardList; track key) {
                        <button class="keyboard-btn" type="button" (click)="keyUpByWordList(index,key,inputRef)">
                            <span class="keyboard_font">{{key}}</span>
                        </button>
                        }
                    </div>
                </div>
                }
                }

            </div>
            <div class="input-group">
                <span class="want-contribute-item">
                    備註
                </span>
                <div class="textarea-list">
                    <textarea class="form-control" placeholder="例：一種傳統的狩獵工具" formControlName="dictionaryNote">
                </textarea>
                </div>
                <div class="textarea-count"><span class="font_r">0 </span>&nbsp;<span class="font_gray">/ 500</span>
                </div>
            </div>
        </div>
        <div class="input-group">
            <span class="want-contribute-item">
                <span class="font_r">*</span>語意（創詞概念，請以中文說明此族語創詞的意思語概念）
            </span>
            <div class="input-list">
                <input type="text" title="creationConcept" placeholder="請輸入語意" class="form-control" style="width: 100%;"
                    formControlName="creationConcept">
            </div>
        </div>
        <div class="input-group">
            <span class="want-contribute-item">
                <span class="font_r">*</span>族語例句
            </span>

            <div class="input-list">
                <div class="search-box">
                    <input type="text" title="originalSentence" placeholder="請輸入族語例句" class="form-control"
                        style="width: 100%;padding-right: 45px;" formControlName="originalSentence" #originalSentence>
                    @if(form.value.originalSentence&& form.value.originalSentence.length>0){
                    <a class="search-a1" href="" (click)="$event.preventDefault()"><span
                            class="material-symbols-outlined" (click)="clearInput('originalSentence')">close</span></a>
                    }
                </div>
                <input class="btn-list btn-primary-color keyboard2" value="虛擬鍵盤" type="button"
                    (click)="isOriginalSentence=!isOriginalSentence">
            </div>
            @if(isOriginalSentence){
            <div class="keyboard-group2">
                <div class="keyboard-box">
                    @for (item of keyboardList; track item) {
                    <button class="keyboard-btn" type="button"
                        (click)="keyUp(item,'originalSentence',originalSentence)">
                        <span class="keyboard_font">{{item}}</span>
                    </button>
                    }
                </div>
            </div>
            }
        </div>
        <div class="input-group">
            <span class="want-contribute-item">
                <span class="font_r">*</span>華語例句
            </span>
            <div class="input-list">
                <input type="text" title="chineseSentence" placeholder="請輸入華語例句" class="form-control"
                    style="width: 100%;" formControlName="chineseSentence">
            </div>
        </div>
        <div class="input-group">
            <span class="want-contribute-item">
                <span class="font_r">*</span>投稿者
            </span>
            <div class="input-list">
                <input type="text" title="name" placeholder="請輸入姓名" class="form-control" style="width: 100%;"
                    formControlName="investor">
            </div>
        </div>
        <div class="input-group">
            <span class="want-contribute-item">
                <span class="font_r">*</span>Email(前端不顯示)
            </span>
            <div class="input-list">
                <input type="text" title="email" placeholder="請輸入Email" class="form-control" style="width: 100%;"
                    formControlName="email">
            </div>
        </div>
        <div class="question-input-box">
            <span class="want-contribute-item">
                <span class="font_r">*</span>驗證碼
            </span>
            <div class="captcha-box">
                <input class="form-control" type="text" title="captcha" formControlName="captcha"
                    placeholder="請輸入驗證碼">&nbsp;&nbsp;
                <img [src]="img" alt="captcha">
                <mat-icon (click)="getCaptcha()">refresh</mat-icon>
                <mat-icon (click)="play()">volume_up</mat-icon>
            </div>
        </div>
        <div class="input-list">
            <input class="btn-list2 btn-primary-color" value="送出" type="button" (click)="send()">
        </div>
    </main>
</form>