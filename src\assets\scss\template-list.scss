//換色母版
$basic-color: #4a7f42;
$deep-color: #265a1e;
$light-color: #d8eed4;
$light-deep-color: #7eaadb;
$white-color: #ffffff;
$black-coolr: #232127;
$grey-color: #949494;
$deep-grey-color: #364250;
$light-grey-color: #3642500f;

//按鈕換色
$blue-color: #0193d9;
$deep-blue-color: #2474af;
$light-blue-color: #8ceffc;

$green-color: #adcc6a;
$deep-green-color: #568636;
$light-green-color: #81d17e;

$yellow-color: #fabe55;
$deep-yellow-color: #bb8600;
$light-yellow-color: #ffeab5;

$orange-color: #fc8e5e;
$deep-orange-color: #b46848;
$light-orange-color: #ffdbcb;

$red-color: #f0524b;
$deep-red-color: #c53029;
$light-red-color: #ffd2d0;

//圖片換色
//單色轉白色
$img-color-change-white: invert(100%) sepia(100%) saturate(0%) hue-rotate(288deg) brightness(102%) contrast(102%);

//通用連結顏色
a {
  color: $basic-color;
}
//頁首
.master-header-layout {
  //右上方快速選單
  .master-top-info {
    //上方功能列-第一層
    .master-top-info-cont {
      .master-top-info-item {
        border-bottom-color: $light-grey-color;
        a {
          color: $deep-color;
        }
        &:hover {
          > a {
            background-color: $basic-color;
            color: $white-color;
          }
        }
        .active {
          a {
            color: $white-color;
          }
        }
        //上方功能列-第二層
        .master-top-info-dropdown-cont {
          background: $white-color;
          .master-top-info-dropdown-item {
            border-bottom-color: $light-grey-color;
          }
        }
      }
    }
  }
}

//文字大中小
.font-Change-lsit {
  //標題
  .font-Change-title {
    color: $deep-color;
  }
  //內容
  .font-Change-cont {
    .font-Change-item {
      a {
        background-color: $light-grey-color;
        color: $deep-color;
      }
      &.active {
        a {
          color: var(--white--color);
          background-color: $basic-color;
        }
      }
    }
  }
}

//主選單
.master-menu-layout {
  background-color: $white-color;
  //第一層
  .master-menu-cont {
    .master-menu-cont-item {
      border-bottom-color: $light-grey-color;
      .master-menu-drawer-item {
        color: $deep-grey-color;
        border-bottom-color: $white-color;
        &.active {
          color: $basic-color;
          border-bottom-color: $basic-color;
          a {
            color: $white-color;
          }
        }
        &:hover {
          border-bottom-color: $basic-color;
          > a {
            color: $basic-color;
            background: $white-color;
          }
        }
      }
      &:hover {
        > a {
          color: $basic-color;
          background: $white-color;
        }
      }
    }
    //第二層
    .master-dropdown-menu-layout {
      background: $white-color;
    }
  }
}

//頁尾資訊
.master-footer-layout {
  background-color: $black-coolr;
  .master-footer-content {
    .master-footer-infor {
      .master-footer-infor-text {
        color: $white-color;
      }
    }
  }
  a {
    color: $white-color;
  }
}
@media (max-width: 905px) {
  .master-header-layout {
    h1.logo-list {
      background-color: $white-color;
    }
  }
}

//首頁
//標題
.master-index-title {
  color: $deep-grey-color;
}

.master-pages-title {
  color: $deep-grey-color;
  &:before {
    border-left-color: $basic-color;
  }
}

//每日一句
.short-sentences-layout {
  .short-sentences-subtitle {
    color: $white-color;
  }
  .short-sentences-main-title {
    color: $deep-grey-color;
  }
}
.short-sentences-cont {
  color: $black-coolr;
}
.short-sentences-translate {
  color: $deep-grey-color;
}
//最新消息
.news-layout {
  .news-title {
    color: $deep-grey-color;
  }
  .news-cont {
    .news-cont-list {
      .news-cont-item {
        .news-cont-item-date {
          background-color: $basic-color;
          color: $white-color;
        }
        .news-cont-item-text {
          color: $deep-grey-color;
        }
      }
    }
  }
}
//內頁
.pages-cont-layout {
  background-color: $white-color;
}

//查詢結果
.word-description-layout-title {
  color: black;
}
.word-description-cont {
  background-color: $light-yellow-color;
}
.word-description-cont-icon {
  color: $deep-yellow-color;
}
//單詞內容區
.word-description-explain-layout {
  .word-description-explain-list {
    border-bottom-color: $grey-color;
  }
}
//單詞說明
.word-description-explain-layout {
  .word-description-explain-list {
    .word-description-explain-item {
      .word-description-explain {
        .word-description-explain-tag {
          color: $white-color;
          background-color: $basic-color;
        }
      }
    }
  }
}
//後台左選單
.backstage-menu-layout {
  .backstage-menu-cont-list {
    border-bottom-color: $white-color;
    &:hover > {
      a {
        background-color: $basic-color;
      }
    }
  }
  .menu-active {
    a {
      background-color: $deep-color;
      &:hover {
        background-color: $basic-color;
        .mat-icon {
          background-color: $basic-color;
        }
      }
    }
    &:hover {
      a {
        // background-color: $deep-color;
      }
      .backstage-menu-second-cont {
        .backstage-menu-cont {
          background-color: $basic-color;
          &:hover {
            background-color: $deep-color;
          }
        }
      }
    }
  }
  .backstage-menu-cont {
    background-color: $basic-color;
    color: $white-color;
    &:hover {
      background-color: $deep-color;
    }
  }
  .mat-icon {
    // background-color: $basic-color;
    color: $white-color;
  }
  .backstage-menu-second-cont {
    .backstage-menu-cont-list {
      border-top-color: $white-color;
    }
    .backstage-menu-cont {
      background-color: $basic-color;
      &:before {
        border-color: $white-color;
      }
      &:hover {
        background-color: $deep-color;
      }
    }

    .second-menu-active {
      a {
        background-color: $deep-color;
        &:hover {
          background-color: $basic-color;
        }
      }
    }
  }
}

//彈出視窗樣式
/*彈出視窗版型*/
.popup-layout {
  .popup-cont {
    background-color: $white-color;
  }
  .popup-cont-title {
    background-color: $basic-color;
    color: $white-color;
    .popup-cont-title-logo {
      img {
        filter: $img-color-change-white;
      }
    }
    .popup-close-btn {
      &:after,
      &:before {
        border-left-color: $white-color;
      }
    }
  }
}

//系統彈窗
.mat-mdc-dialog-title {
  background-color: $basic-color;
  color: $white-color !important;
}
.btn-cancel-color {
  background: $white-color;
  color: $basic-color;
  border-color: $basic-color;
}
.btn-cancel-color {
  background: $white-color;
  color: $basic-color;
  border-color: $basic-color;
}

.success-title {
  background-color: $basic-color;
  color: $white-color;
}

.warn-title {
  background-color: $yellow-color;
  color: $white-color;
}

.error-title {
  background-color: $red-color;
  color: $white-color;
}

.btn-cancel-color {
  background: $white-color;
  color: $blue-color;
  border-color: $blue-color;
}

.font-success {
  color: $green-color;
}
.font-deep {
  color: $deep-color;
}

.font-warn {
  color: $yellow-color;
}

.font-error {
  color: $red-color;
}

.file-name {
  background-color: $light-blue-color;
  mat-icon {
    color: $red-color;
  }
}
label {
  span {
    color: $red-color;
  }
}

//頁籤樣式
#tab-list-layout {
  /* 頁籤ul */
  > ul {
    > li {
      > a {
        color: $deep-grey-color;
      }
    }
  }
}
/*第一筆的底色*/
span.tab-item:target ~ #tab-list-layout > ul li:first-child a {
  border-bottom-color: $white-color;
}
/*頁籤變換&第一筆*/
span.tab-item ~ #tab-list-layout > ul li:first-child a,
#tab-1:target ~ #tab-list-layout > ul li a[href$="#tab-1"],
#tab-2:target ~ #tab-list-layout > ul li a[href$="#tab-2"] {
  background: $white-color;
  border-bottom-color: $basic-color;
}
/*頁籤內容顯示&第一筆*/
span.tab-item ~ #tab-list-layout > div:first-of-type,
#tab-1:target ~ #tab-list-layout > div.tab-content-1,
#tab-2:target ~ #tab-list-layout > div.tab-content-2 {
  background: $white-color;
}

//表單樣式表
//按鈕
//實心
//通用
.btn-primary-solid,
.btn-normal-solid,
.btn-warn-solid,
.btn-danger-solid,
.btn-default-solid {
  color: $white-color;
}
//一般1
.btn-primary-solid {
  background: $basic-color;
  &:hover {
    background: $basic-color;
  }
  &:active {
    background-color: $deep-color;
    border-color: $deep-color;
  }
}

//一般2
.btn-normal-solid {
  background: $green-color;
  &:hover,
  &:focus {
    background: $green-color;
  }
  &:active {
    background-color: $deep-green-color;
    border-color: $deep-green-color;
  }
}

//警告
.btn-warn-solid {
  background: $yellow-color;
  &:hover,
  &:focus {
    background: $yellow-color;
  }
  &:active {
    background-color: $deep-yellow-color;
    border-color: $deep-yellow-color;
  }
}

//危險
.btn-danger-solid {
  background: $red-color;
  &:hover,
  &:focus {
    background: $red-color;
  }
  &:active {
    background-color: $deep-red-color;
    border-color: $deep-red-color;
  }
}

//不啟用
.btn-default-solid {
  background: $light-grey-color;
  color: $deep-grey-color;
  &:hover,
  &:focus {
    background: $grey-color;
  }
  &:active {
    background-color: $deep-grey-color;
    border-color: $deep-grey-color;
  }
}

//線條
//一般1
.btn-primary-line,
.btn-normal-line,
.btn-warn-line,
.btn-danger-line,
.btn-default-line {
  background: $white-color;
  &:hover,
  &:focus,
  &:active {
    color: $white-color;
  }
}
//一般1
.btn-primary-line {
  color: $basic-color;
  border-color: $basic-color !important;
  &:hover,
  &:focus,
  &:active {
    background: $basic-color;
  }
}
//一般2
.btn-normal-line {
  color: $green-color;
  border-color: $green-color !important;
  &:hover,
  &:focus,
  &:active {
    background: $green-color;
  }
}
//警告
.btn-warn-line {
  color: $yellow-color;
  border-color: $yellow-color !important;
  &:hover,
  &:focus,
  &:active {
    background: $yellow-color;
  }
}
//危險
.btn-danger-line {
  color: $red-color;
  border-color: $red-color !important;
  &:hover,
  &:focus,
  &:active {
    background: $red-color;
  }
}
//不啟用
.btn-default-line {
  color: $grey-color;
  border-color: $grey-color !important;
  &:hover,
  &:focus,
  &:active {
    background: $grey-color;
  }
}

//文字
//通用
.btn-primary-text,
.btn-normal-text,
.btn-warn-text,
.btn-danger-text,
.btn-default-text {
  background: $white-color;
}
//一般1
.btn-primary-text {
  color: $basic-color;
  &:hover,
  &:focus,
  &:active {
    border-color: $basic-color !important;
  }
}
//一般2
.btn-normal-text {
  color: $green-color;
  &:hover,
  &:focus,
  &:active {
    border-color: $green-color !important;
  }
}
//警告
.btn-warn-text {
  color: $yellow-color;
  &:hover,
  &:focus,
  &:active {
    border-color: $yellow-color !important;
  }
}
//危險
.btn-danger-text {
  color: $red-color;
  &:hover,
  &:focus,
  &:active {
    border-color: $red-color !important;
  }
}
//不啟用
.btn-default-text {
  color: grey-color;
  &:hover,
  &:focus,
  &:active {
    border-color: $grey-color !important;
  }
}

//ICON
//通用
.btn-primary-icon,
.btn-normal-icon,
.btn-warn-icon,
.btn-danger-icon,
.btn-default-icon {
  margin: 0 !important;
  padding: 5px 5px 0 !important;
  background: $white-color;
}
//一般1
.btn-primary-icon {
  color: $basic-color;
  &:hover,
  &:focus,
  &:active {
    color: $deep-color;
  }
}
//一般2
.btn-normal-icon {
  color: $green-color;
  &:hover,
  &:focus,
  &:active {
    color: $deep-green-color;
  }
}
//警告
.btn-warn-icon {
  color: $yellow-color;
  &:hover,
  &:focus,
  &:active {
    color: $deep-yellow-color;
  }
}
//危險
.btn-danger-icon {
  color: $red-color;
  &:hover,
  &:focus,
  &:active {
    color: $deep-red-color;
  }
}
//不啟用
.btn-default-icon {
  color: $grey-color;
  &:hover,
  &:focus,
  &:active {
    color: $deep-grey-color;
  }
}

/*內容頁標題*/
.section-heading-layout {
  border-bottom-color: $deep-color;
  .section-heading-title {
    color: $deep-grey-color;
    &:before {
      border-color: $basic-color;
    }
  }
  .section-heading-more-btn {
    color: $basic-color;
    border-color: $basic-color;
    &:hover {
      background-color: $basic-color;
      color: $white-color;
    }
  }
}

//表單樣式表
//通用
input,
textarea,
select {
  &:disabled {
    background-color: $light-grey-color;
  }
}

//輸入框
.form-control-list {
  background-color: $white-color;
  border-color: $grey-color;
}

//選項按鈕
.radio-list {
  input[type="radio"] {
    &:checked {
      + label:before {
        background-color: $white-color !important;
        border-color: $basic-color;
      }
    }
    + label:before {
      border-color: $grey-color;
      background-color: $white-color;
    }
    &:disabled {
      + label:before {
        background-color: $light-grey-color;
        border-color: $light-grey-color;
      }
    }
  }
}

//核選按鈕
.checkbox-list {
  input[type="checkbox"] {
    background: $white-color;
    border-color: $grey-color;
    &:checked {
      background: $basic-color;
      border-color: $basic-color;
      &::after {
        border-color: $white-color;
      }
    }
    &:disabled {
      background-color: $light-grey-color;
      border-color: $light-grey-color;
    }
  }
}

//開關按鈕
.switch-list {
  .switch-item {
    input {
      &:checked + .round-icon {
        background-color: $basic-color;
      }
      &:focus + .round-icon {
        box-shadow: 0 0 1px $basic-color;
      }
      &:disabled + .round-icon {
        background-color: $light-grey-color !important;
      }
    }
    .round-icon {
      background-color: $light-grey-color;
      &:before {
        background-color: $white-color;
      }
    }
  }
}

//驗證碼
.captcha-list {
  border-color: $grey-color;
}

//表格
//通用
.table-list-layout {
  > thead > tr > th,
  tbody > tr > th,
  tfoot > tr > th,
  thead > tr > td,
  tbody > tr > td,
  tfoot > tr > td {
    border-bottom-color: $light-grey-color;
  }
  tbody + tbody {
    border-top-color: $light-grey-color;
  }
  tr {
    &:nth-child(odd) {
      background: #e5efe3;
    }
  }
}

@media (max-width: 640px) {
  .rwd-table02 {
    tr {
      border-color: $light-grey-color;
    }
  }
  .rwd-th {
    background-color: $basic-color;
    color: $white-color;
  }
  .rwd-th02 {
    background: $basic-color none repeat scroll 0 0 !important;
    color: $basic-color;
  }
}
//列表表格
.table-list-style {
  th {
    background-color: #4a7f42;
    color: $white-color;
  }
}

//連結&檔案下載
.link-layout {
  .link-list {
    &:last-child {
      .link-cont {
        padding-right: 0;
      }
    }
    &:hover {
      .link-cont {
        .link-cont-icon {
          color: $basic-color;
        }
        &:after {
          background-color: $light-deep-color;
        }
      }
    }
  }
}
.file-downlond-layout {
  .link-list {
    .link-cont {
      border-bottom-color: $grey-color;
      color: $deep-grey-color;
      &:hover {
        color: $white-color;
        background-color: $light-deep-color;
        border-bottom-color: $white-color;
        .link-file-infor {
          color: $white-color;
        }
      }
    }
  }
}

//網站導覽
.sitemap-cont-list-layout {
  .sitemap-cont-list-item {
    border-bottom-color: $light-color;
    .sitemap-cont-list-layout {
      .sitemap-cont-list-item {
        a {
          color: $light-deep-color;
        }
      }
    }
  }
}

mat-icon {
  font-size: 1.5em;
}

.none {
  display: none;
}
