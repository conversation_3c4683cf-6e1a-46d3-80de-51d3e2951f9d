import { HttpErrorResponse } from '@angular/common/http';
import {
  Component,
  OnInit,
  OnDestroy,
  ViewChild,
  AfterViewInit,
  ElementRef,
  Renderer2,
} from '@angular/core';
import { MatDialog } from '@angular/material/dialog';
import {
  MatTabGroup,
  MatTabChangeEvent,
  MatTab,
  MatTabLabel,
} from '@angular/material/tabs';
import { DomSanitizer, SafeHtml } from '@angular/platform-browser';
import { ActivatedRoute, Router, RouterLink } from '@angular/router';
import { debounceTime, forkJoin, Subject } from 'rxjs';
import { AnaphoraSentenceDialogComponent } from '../../../dialog/anaphora-sentence-dialog/anaphora-sentence-dialog.component';
import { apiStatus } from '../../../enum/apiStatus.enum';
import {
  dictionaryItem,
  searchDictionaryReq,
  searchDictionaryResp,
  searchDictionaryDetailResp,
  searchExampleResp,
  relateSentenceItem,
  audioItem,
} from '../../../interface/language.interface';
import { LanguageService } from '../../../service/curl/language.service';
import { ConfirmService } from '../../../service/utils/confirm.service';
import { environment } from '../../../../environments/environment';
import { FileService } from '../../../service/curl/file.service';
import { OwlOptions, CarouselModule } from 'ngx-owl-carousel-o';
import { SearhModel } from '../../../enum/SearhModel .enum';
import { RootStructureDialogComponent } from '../../../dialog/root-structure-dialog/root-structure-dialog.component';
import { UtilsService } from '../../../service/utils/utils.service';
import { SpinnerService } from '../../../service/utils/spinner.service';
import { ShareService } from '../../../service/curl/share.service';
import { autoSearchResp } from '../../../interface/share.interface';
import { FormsModule } from '@angular/forms';
import { MatIcon } from '@angular/material/icon';
import {
  MatAccordion,
  MatExpansionPanel,
  MatExpansionPanelHeader,
} from '@angular/material/expansion';
import { MatProgressSpinner } from '@angular/material/progress-spinner';
import { NgClass } from '@angular/common';
import { PaginatorComponent } from '../../../utils/paginator/paginator.component';
import { GetEthnicityService } from '../../../service/utils/get-ethnicity.service';

export enum ShareType {
  FB = 'fb',
  LINE = 'line',
  IG = 'ig',
}
declare const FB: any;

@Component({
  selector: 'app-single-tribe-search',
  templateUrl: './single-tribe-search.component.html',
  styleUrl: './single-tribe-search.component.scss',
  imports: [
    RouterLink,
    FormsModule,
    MatIcon,
    MatTabGroup,
    MatTab,
    MatTabLabel,
    MatAccordion,
    MatExpansionPanel,
    MatExpansionPanelHeader,
    MatProgressSpinner,
    NgClass,
    CarouselModule,
    PaginatorComponent,
  ],
})
export class SingleTribeSearchComponent
  implements OnInit, AfterViewInit, OnDestroy
{
  @ViewChild('keywordInput') keywordInput!: ElementRef;
  @ViewChild('searchAll') searchAll!: ElementRef;
  @ViewChild('tabGroup') tabGroup!: MatTabGroup;
  singleTribe?: {
    id: string;
    name: string;
  };
  ethnicity: string;
  ethnicityId: string;

  keyword: string = '';
  private keywordChanged: Subject<string> = new Subject<string>();
  autoInputList: string[] = [];
  showKeyword: string = '';

  isDialect: boolean = false;
  isAdvanced: boolean = false;

  keyboardList: string[] = []; //虛擬鍵盤list
  dialectList: {
    id: string;
    name: string;
    selected: boolean;
  }[] = []; //方言list
  symbolList: { id: string; name: string }[] = []; //符號List
  categoryList: { id: string; name: string }[] = []; //範疇List
  partOfSpeechList: { id: string; name: string }[] = []; //詞類List
  restrictSearchList: { value: number; name: string }[] = []; //僅搜尋List
  sourceList: { id: string; name: string; selected: boolean }[] = []; //來源List

  symbolStart: string | null = null;
  symbolEnd: string | null = null;
  category: string | null = null;
  partOfSpeech: string | null = null;
  restrictSearch: number = 0;
  source: string | null = null;

  accurateList: any[] = [{ name: 'bway' }, { name: 'buwa' }];

  pageSize: number = 10;
  nowPage: number = 1;
  activePage: number = 1;
  totalPage: number = 0;
  totalCount: number = 0;
  pageShowCount: number = 5; //分頁器秀幾個

  dictionaryList: dictionaryItem[] = [];
  exampleList: relateSentenceItem[] = [];

  selectedDialect: string[] = [];
  selectedSource: string[] = [];

  SearhModel = SearhModel;
  searchGroup: SearhModel[] = [
    SearhModel.Accurate,
    SearhModel.Fuzzy,
    SearhModel.Example,
  ];

  searhModel: SearhModel = SearhModel.Accurate;

  shareType = ShareType;

  private currentMediaElement: HTMLAudioElement | null = null;

  customOptions: OwlOptions = {
    loop: false,
    navSpeed: 700,
    dots: true,
    items: 1,
    center: true,
  };

  fontSize: number = 1;

  constructor(
    private router: Router,
    private languageService: LanguageService,
    private confirmService: ConfirmService,
    private sanitizer: DomSanitizer,
    private matDialog: MatDialog,
    private fileService: FileService,
    private utils: UtilsService,
    private spinnerService: SpinnerService,
    private activatedRoute: ActivatedRoute,
    private shareService: ShareService,
    private renderer: Renderer2,
    private getEthnicityService: GetEthnicityService
  ) {
    this.utils.fontSize$.subscribe((size) => {
      this.fontSize = size === 0 ? 1 : size;
    });

    router.navigate;
    const navigation = this.router.getCurrentNavigation();
    // 優先從 state 中獲取數據
    if (navigation?.extras.state) {
      this.singleTribe = navigation.extras.state['singleTribe'];
    }

    if (!this.singleTribe && sessionStorage.getItem('singleTribe')) {
      this.singleTribe = JSON.parse(sessionStorage.getItem('singleTribe')!);
    }

    this.ethnicity = this.getEthnicityService.GetEthnicityName() as string;
    this.ethnicityId = this.getEthnicityService.GetEthnicityId() as string;

    this.activatedRoute.queryParamMap.subscribe((queryParams) => {
      if (queryParams && queryParams.get('keyword')) {
        this.keyword = queryParams.get('keyword') as string;
        this.showKeyword = queryParams.get('keyword') as string;
        this.search();
      } else {
        this.showKeyword = this.keyword;
        this.searchAccurate(this.selectedDialect);
      }
    });
  }

  ngOnInit(): void {
    this.utils.setTitle(`${this.ethnicity}-檢索`);
    this.initializationSearchOptions();
    this.keywordChanged.pipe(debounceTime(500)).subscribe((value: string) => {
      if (value) {
        this.callAutoInputAPI(value);
      } else {
        this.autoInputList = [];
      }
    });
  }

  ngAfterViewInit(): void {
    this.setupDocumentClickListener();
  }

  ngOnDestroy(): void {
    this.stopMusic();
    this.documentClickListener?.();
  }

  // 監聽文檔點擊事件
  private documentClickListener: () => void = () => {};

  private setupDocumentClickListener() {
    setTimeout(() => {
      this.documentClickListener = this.renderer.listen(
        'document',
        'click',
        (event: MouseEvent) => {
          // 這裡需要先檢查 searchAll.nativeElement 是否存在
          if (
            this.searchAll?.nativeElement &&
            !this.searchAll.nativeElement.contains(event.target)
          ) {
            this.autoInputList = [];
          }
        }
      );
    });
  }

  // 當搜尋框獲得焦點時重新設置監聽器
  onSearchFocus() {
    this.setupDocumentClickListener();
  }

  onKeywordChange(value: string): void {
    this.keywordChanged.next(value);
  }

  callAutoInputAPI(value: string): void {
    this.shareService.autoSearch(value, this.ethnicityId).subscribe({
      next: (resp: autoSearchResp) => {
        this.autoInputList = resp.data.candidateWords;
      },
      error: () => {},
    });
  }

  selectValue(value: string) {
    this.keyword = value;
    this.autoInputList = [];
  }

  initializationSearchOptions() {
    forkJoin({
      keyboardData: this.languageService.getEthnicityKeyboard(this.ethnicityId),
      advanceSearchData: this.languageService.getAdvanceSearch(
        this.ethnicityId
      ),
      languageData: this.languageService.getEthnicityLanguage(this.ethnicityId),
    }).subscribe({
      next: (result) => {
        this.keyboardList = result.keyboardData.data.symbolList;
        this.dialectList = result.languageData.data.items.map(
          (item: { id: string; name: string }) => ({
            ...item,
            selected: false,
          })
        );
        this.symbolList = result.advanceSearchData.data.symbolItems;
        this.categoryList = result.advanceSearchData.data.categoryItems;
        this.partOfSpeechList = result.advanceSearchData.data.partOfSpeechItems;
        this.restrictSearchList =
          result.advanceSearchData.data.restrictSearchItems;
        this.sourceList = result.advanceSearchData.data.sourceItems.map(
          (item: { id: string; name: string }) => ({
            ...item,
            selected: false,
          })
        );
      },
      error: (err: HttpErrorResponse) => {
        console.error(err);
      },
    });
  }

  dialectListChange(item: any) {
    if (item.selected) {
      this.selectedDialect.push(item.id);
    } else {
      this.selectedDialect = this.selectedDialect.filter(
        (selectedDialectId) => {
          return selectedDialectId !== item.id;
        }
      );
    }
  }
  sourceListChange(item: any) {
    if (item.selected) {
      this.selectedSource.push(item.id);
    } else {
      this.selectedSource = this.selectedSource.filter((selectedDialectId) => {
        return selectedDialectId !== item.id;
      });
    }
  }

  selectTab(event: MatTabChangeEvent) {
    this.nowPage = 1;
    this.searhModel = event.tab.textLabel as SearhModel;
    let title: SearhModel = event.tab.textLabel as SearhModel;
    switch (title) {
      case SearhModel.Accurate:
        this.searchAccurate(this.selectedDialect);
        break;
      case SearhModel.Fuzzy:
        this.searchFuzzy(this.selectedDialect);
        break;
      case SearhModel.Example:
        this.searchExample(this.selectedDialect);
        break;
    }
  }

  search() {
    if (!this.keyword) {
      this.confirmService.showWARN('請輸入關鍵字', '警告');
      return;
    }
    if (this.tabGroup) {
      this.tabGroup.selectedIndex = 0; // 切換到第一個 Tab
    }
    let i = 0;
    this.nowPage = 1;
    if (i < 1) {
      this.searchAccurate(this.selectedDialect);
      i + 1;
    }
  }

  searchAccurate(selectedDialectList: string[]) {
    if (!this.keyword) return;

    this.showKeyword = this.keyword;
    this.isDialect = false;
    this.isAdvanced = false;

    let req: searchDictionaryReq = {
      page: this.nowPage,
      tribeDialectId: this.ethnicityId,
      pageSize: this.pageSize,
      keyword: this.keyword,
      advanceSearch: {
        dialectId: selectedDialectList,
        startSymbolId: this.symbolStart, // 符號起始ID
        endSymbolId: this.symbolEnd, // 符號結束 ID
        categoryId: this.category, // 範疇ID
        searchRestrict: this.restrictSearch,
        partOfSpeechId: this.partOfSpeech,
        sources: this.selectedSource,
      },
    };
    this.spinnerService.show();
    this.languageService.searchDictionary(req, SearhModel.Accurate).subscribe({
      next: (resp: searchDictionaryResp) => {
        this.spinnerService.hide();
        if (resp.status === apiStatus.SUCCESS) {
          this.dictionaryList = resp.data.wordItems;
          this.totalCount = resp.data.itemTotalCount;
          this.totalPage = resp.data.pageTotalCount;
          this.onPanelOpened(this.dictionaryList[0]);
          //TODO修正單族別localstroage
          const keyword = this.keyword;
        } else {
          this.confirmService.showError(resp.message, '錯誤');
        }
      },
      error: (err: HttpErrorResponse) => {
        this.spinnerService.hide();
        this.confirmService.showError(err.error.error.details, '錯誤');
      },
    });
  }

  searchFuzzy(selectedDialectList: string[]) {
    this.isDialect = false;
    this.isAdvanced = false;
    let req: searchDictionaryReq = {
      tribeDialectId: this.ethnicityId,
      page: this.nowPage,
      pageSize: this.pageSize,
      keyword: this.keyword,
      advanceSearch: {
        dialectId: selectedDialectList,
        startSymbolId: this.symbolStart, // 符號起始ID
        endSymbolId: this.symbolEnd, // 符號結束 ID
        categoryId: this.category, // 範疇ID
        searchRestrict: this.restrictSearch,
        partOfSpeechId: this.partOfSpeech,
        sources: this.selectedSource,
      },
    };

    this.spinnerService.show();
    this.languageService.searchDictionary(req, SearhModel.Fuzzy).subscribe({
      next: (resp: searchDictionaryResp) => {
        this.spinnerService.hide();
        if (resp.status === apiStatus.SUCCESS) {
          this.dictionaryList = resp.data.wordItems;
          this.totalCount = resp.data.itemTotalCount;
          this.totalPage = resp.data.pageTotalCount;
          this.onPanelOpened(this.dictionaryList[0]);

          const keyword = this.keyword;
          //TODO修正單族別localstroage
          const indexSearch = {
            keyword,
            selectedDialectList,
          };
          sessionStorage.setItem('indexSearch', JSON.stringify(indexSearch));
        } else {
          this.confirmService.showError(resp.message, '錯誤');
        }
      },
      error: (err: HttpErrorResponse) => {
        this.spinnerService.hide();
        this.confirmService.showError(err.error.error.details, '錯誤');
      },
    });
  }

  searchExample(selectedDialectList: string[]) {
    this.isDialect = false;
    this.isAdvanced = false;
    let req: searchDictionaryReq = {
      page: this.nowPage,
      pageSize: this.pageSize,
      keyword: this.keyword,
      tribeDialectId: this.ethnicityId,
      advanceSearch: {
        dialectId: selectedDialectList,
        startSymbolId: this.symbolStart, // 符號起始ID
        endSymbolId: this.symbolEnd, // 符號結束 ID
        categoryId: this.category, // 範疇ID
        partOfSpeechId: this.partOfSpeech,
        searchRestrict: this.restrictSearch,
        sources: this.selectedSource,
      },
    };
    this.spinnerService.show();
    this.languageService.searchExample(req).subscribe({
      next: (resp: searchExampleResp) => {
        this.spinnerService.hide();
        if (resp.status === apiStatus.SUCCESS) {
          this.exampleList = resp.data.relateSentenceItems;
          this.totalCount = resp.data.itemTotalCount;
          this.totalPage = resp.data.pageTotalCount;
          this.onPanelOpened(this.dictionaryList[0]);

          const keyword = this.keyword;
          const indexSearch = {
            keyword,
            selectedDialectList,
          };
          sessionStorage.setItem('indexSearch', JSON.stringify(indexSearch));
        } else {
          this.confirmService.showError(resp.message, '錯誤');
        }
      },
      error: (err: HttpErrorResponse) => {
        this.spinnerService.hide();
        this.confirmService.showError(err.error.error.details, '錯誤');
      },
    });
  }

  sanitizeExplanation(rawHtml: string): SafeHtml {
    return this.sanitizer.bypassSecurityTrustHtml(rawHtml);
  }

  isNameMatched(a: string, b: string): boolean {
    return a?.toLowerCase() === b?.toLowerCase();
  }

  keyboardUp(value: string, inputElement: HTMLInputElement) {
    const currentKeyword = this.keyword || '';
    const start = inputElement.selectionStart ?? currentKeyword.length;
    const end = inputElement.selectionEnd ?? currentKeyword.length;
    const newKeyword =
      currentKeyword.slice(0, start) + value + currentKeyword.slice(end);
    this.keyword = newKeyword;
    const cursorPos = start + value.length;
    setTimeout(() => {
      inputElement.focus();
      inputElement.setSelectionRange(cursorPos, cursorPos);
    });
  }

  clear() {
    this.keyword = '';
    this.showKeyword = '';
  }

  onPanelOpened(item: dictionaryItem) {
    console.log('onPanelOpened item', item);
    if (!item) {
      return;
    }
    item.isOpenPanel = true;
    item.loading = true;
    this.languageService.searchDictionaryDetail(item.id).subscribe({
      next: (resp: searchDictionaryDetailResp) => {
        if (resp.status === apiStatus.SUCCESS) {
          item.loading = false;
          item.isDerivativeRoot = resp.data.isDerivativeRoot;
          item.isImage = resp.data.isImage;
          item.wordItem = resp.data.word;
        } else {
          this.confirmService.showError(resp.message, '錯誤');
        }
      },
      error: () => {},
    });
  }

  onPanelClose(item: dictionaryItem) {
    item.isOpenPanel = false;
  }

  clickAnaphoraSentence(id: string | null) {
    if (id === null) {
      return;
    }
    this,
      this.matDialog.open(AnaphoraSentenceDialogComponent, {
        disableClose: true,
        autoFocus: false,
        width: '60%',
        data: {
          id: id,
        },
      });
  }

  /**
   * 第幾頁
   * @param item number
   */
  getPageFromPaginator(nowPage: number) {
    this.nowPage = nowPage;
    this.activePage = nowPage;
    switch (this.searhModel) {
      case SearhModel.Accurate:
        this.searchAccurate(this.selectedDialect);
        break;
      case SearhModel.Fuzzy:
        this.searchFuzzy(this.selectedDialect);
        break;
      case SearhModel.Example:
        this.searchExample(this.selectedDialect);
        break;
    }
    window.scrollTo({
      top: 0,
      behavior: 'smooth',
    });
  }
  /**
   * 每頁資料數量
   * @param item number
   */
  getPageSizeFromPaginator(pageSize: number) {
    this.pageSize = pageSize;
    this.nowPage = 1;
    switch (this.searhModel) {
      case SearhModel.Accurate:
        this.searchAccurate(this.selectedDialect);
        break;
      case SearhModel.Fuzzy:
        this.searchFuzzy(this.selectedDialect);
        break;
      case SearhModel.Example:
        this.searchExample(this.selectedDialect);
        break;
    }

    window.scrollTo({
      top: 0,
      behavior: 'smooth',
    });
  }

  share(type: ShareType, id: string) {
    const shareUrl = `${environment.sitePath}/sharePage?id=${id}`;
    switch (type) {
      case this.shareType.FB:
        FB.ui(
          {
            method: 'share',
            href: shareUrl,
          },
          (response: any) => {
            if (response && !response.error_message) {
              console.log('分享成功');
            } else {
              console.error('分享失敗或取消', response);
            }
          }
        );
        break;
      case this.shareType.LINE:
        window.open(
          `https://social-plugins.line.me/lineit/share?url=${environment.sitePath}/sharePage?id=${id}`,
          '_blank',
          'noopener,noreferrer'
        );
        break;
    }
  }

  getRootStructure(dictionaryId: string) {
    this.matDialog.open(RootStructureDialogComponent, {
      disableClose: true,
      autoFocus: false,
      width: '60%',
      data: {
        dictionaryId: dictionaryId,
      },
    });
  }

  question(item: dictionaryItem) {
    window.open(
      `${environment.sitePath}/singleQuestion?id=${item.id}&tribeId=${item.tribeId}`,
      '_blank'
    );
  }

  play(item: audioItem) {
    if (this.currentMediaElement) {
      this.currentMediaElement.pause();
      this.currentMediaElement.remove();
    }
    this.fileService.getAudioFile(item.fileId).subscribe({
      next: (resp: string) => {
        let mediaElement: HTMLAudioElement = document.createElement('audio');
        mediaElement.style.display = 'none'; // 這行讓音頻播放器隱藏
        mediaElement.setAttribute('src', resp); // 設置音頻源
        mediaElement.setAttribute('controls', 'true'); // 加入控制條
        document.body.appendChild(mediaElement);
        mediaElement.play();
        mediaElement.addEventListener('ended', () => {
          this.currentMediaElement = null; // 重置當前音樂播放器
        });

        this.currentMediaElement = mediaElement; // 儲存當前的音樂播放器
      },
      error: () => {},
    });
  }

  stopMusic() {
    if (this.currentMediaElement) {
      this.currentMediaElement.pause();
      this.currentMediaElement.remove();
      this.currentMediaElement = null;
    }
  }

  goWantContribute() {
    this.router.navigate(['singleWordRec'], {
      queryParams: {
        tab: 1,
      },
    });
  }

  back(event: Event) {
    event.preventDefault();
    history.back();
  }
  onCopy(event: ClipboardEvent) {
    event.preventDefault(); // 阻止預設複製行為
    const selection = window.getSelection();
    if (selection) {
      let copiedText = selection
        .toString()
        .replace(/\r?\n/g, ' ') // 移除換行
        .replace(/\s+/g, ' '); // 將多個空格替換為單一空格
      event.clipboardData?.setData('text/plain', copiedText);
    }
  }

  openImage(item: dictionaryItem) {
    item.wordItem?.explanationItems.map((item) => {
      return (item.isImage = !item.isImage);
    });
  }
}
