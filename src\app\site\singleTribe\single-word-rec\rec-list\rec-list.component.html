<!--內容區-->



<div class="row">
    <div class="col-3 col-12 gutter-16px">
        <div class="mb-12px w-full relative select-style">
            <select class="select w-full" title="處理狀態" [(ngModel)]="status">
                <option [ngValue]="null" disabled selected>請選擇詞項狀態</option>
                <option [ngValue]="null">不拘</option>
                <option [ngValue]="2">審查中</option>
                <option [ngValue]="1">已收錄</option>
                <option [ngValue]="0">未收錄</option>
            </select>
            <div class="block-select-bg absolute">
                <div class="row items-stretch">
                    <div class="col">
                        <div class="block-select-bg-rect w-full radius-card "></div>
                    </div>
                    <div class="col-auto shrink-0">
                        <div class="button-dot">
                            <span class="material-symbols-outlined">keyboard_arrow_down</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-3 col-12 gutter-16px">
        <div class="mb-12px w-full relative select-style">
            <select class="select w-full" title="語別" [(ngModel)]="dialectId">
                <option [ngValue]="null" disabled selected>請選語別</option>
                @if(dialectList.length>1){
                <option [ngValue]="null" selected>不拘</option>
                }
                @for (option of dialectList; track option) {
                <option [ngValue]="option.id">{{option.name}}</option>
                }
            </select>

            <div class="block-select-bg absolute">
                <div class="row items-stretch">
                    <div class="col">
                        <div class="block-select-bg-rect w-full radius-card"></div>
                    </div>
                    <div class="col-auto shrink-0">
                        <div class="button-dot">
                            <span class="material-symbols-outlined">keyboard_arrow_down</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    &nbsp;&nbsp;
    <input type="text" title="keyword" class="form-control" placeholder="請輸入您的推薦詞項/推薦者姓名/EMAIL" [(ngModel)]="keyword">
    &nbsp;&nbsp;
    <button class="btn-list btn-primary-solid btn-search" (click)="search()">搜尋</button>
</div>
<div class="table-box">
    <table class="table-list-layout  table-list-style rwd-table01">
        <tbody>
            <tr class="th-no">
                @for (item of list; track $index) {
                <th [style.width]="item.width">
                    {{item.title}}
                </th>
                }
            </tr>
            @if(recList.length>0){
            @for (item of recList; let index= $index;track index) {
            <tr>
                <td class="text-c">
                    <span class="rwd-th">序號</span>
                    {{index+1+(nowPage>1?(nowPage-1)*pageSize:0)}}
                </td>
                <td class="text-c">
                    <span class="rwd-th">推薦日</span>
                    {{item.creationTime|date:'YYYY.MM.dd'}}
                </td>
                <td class="text-c">
                    <span class="rwd-th">語別
                    </span>
                    {{item.dialect}}
                </td>
                <td class="text-c">
                    <span class="rwd-th">詞項</span>
                    {{item.dictionaryName}}
                </td>
                <td class="text-l">
                    <span class="rwd-th">中文解釋</span>
                    {{item.chineseExplanation}}
                </td>
                <td class="text-c">
                    <span class="rwd-th">推薦者</span>
                    {{item.creator}}
                </td>
                @if(tempStatus===0){
                <td class="text-l">
                    <span class="rwd-th">未收錄原因</span>
                    {{item.statusNote}}
                </td>
                }
            </tr>
            }
            }@else{
            <tr>
                @if(tempStatus===0){
                <td colspan="7" style="text-align: center;">沒有找到符合條件的資料</td>
                }@else{
                <td colspan="6" style="text-align: center;">沒有找到符合條件的資料</td>
                }
            </tr>
            }
        </tbody>
    </table>
    @if(recList.length>0){
    <div>
        <app-paginator [pageSize]="pageSize" [nowPage]="nowPage" [totalRecords]="totalCount"
            [pageShowCount]="pageShowCount" currentPageReportTemplate="第 {first} 到 {last} 筆，共 {totalRecords} 筆"
            (clickPageEvent)="getPageFromPaginator($event)"
            (pageSizeChangeEvent)="getPageSizeFromPaginator($event)"></app-paginator>
    </div>
    }
</div>