// Scss Document
.index-search-layout{
	margin: 0 auto;
    padding: 30px 90px;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    align-items: center;
	background: transparent url("../../../../../assets/image/main-bg.png") center bottom no-repeat;
	background-size: cover;
	width: 100%;
	box-sizing: border-box;
	.index-search-cont{
		margin: 0 auto;
		padding: 0;
		display: flex;
		flex-direction: row;
		justify-content: space-between;
		align-items: center;
		width: 100%;
		.index-search-btn{
			margin: 0 auto;
			padding: 0;
			display: flex;
			flex-direction: row;
			justify-content: space-between;
			align-items: center;
		}
	}
	.select-control{
		display: flex;
		flex-direction: column;
		justify-content: space-between;
		align-items: center;
		width: 410px;
		select{
			width: 100%;
		}
	}
	.form-control{
		margin-left: 15px;
		width: 100%;
	}
	.btn-list{
		margin-left: 15px;
	}
	.index-search-list-layout{
		margin: 0 auto;
		padding: 0 0 160px;
		display: flex;
		flex-direction: row;
		justify-content: space-between;
		align-items: stretch;
		list-style: none;
		flex-wrap: wrap;
		.index-search-list{
			margin: 0;
			padding: 0 0 20px;
			width: calc(100% / 8);
			.index-search-list-item{
				display: flex;
				flex-direction: column;
				justify-content: space-between;
				align-items: center;
				font-size: 1.976em;
				font-weight: bold;
				Letter-spacing: -0.1em;
				text-decoration: none;
				color: #000;
				span{
					display: flex;
					flex-direction: column;
					justify-content: space-between;
					align-items: center;
				}
				img{
					width: 100%;
					max-width: 186px;
					height: auto;
				}
			}
		}
	}
}
@media (max-width: 1542px){
	.index-search-layout{
		.index-search-list-layout{
			.index-search-list{
				.index-search-list-item{
					font-size: 1.5em;
				}
			}
		}
	}
}
@media (max-width: 1206px){
	.index-search-layout{
		.index-search-list-layout{
			.index-search-list{
				.index-search-list-item{
					font-size: 1.25em;
				}
			}
		}
	}
}

@media (max-width: 905px){
	.index-search-layout{
		 padding: 20px 30px;
		background-size: auto 50%;
		.index-search-cont{
			flex-direction: column;
		}
		.select-control{
			width: 100%;
		}
		.form-control{
			margin-left: 0;
		}
		.index-search-btn{
			display: flex;
       		justify-content: space-between;
			width: 100%;
			.btn-list{
				margin-left: 0;
				margin-right: 0;
				width: calc(50% - 10px);
			}
		}
		.index-search-list-layout{
			.index-search-list{
				width: calc(100% / 4);
				min-height: 135px;
				.index-search-list-item{
					font-size: 1.25em;
				}
			}
		}
	}
}

@media (max-width: 414px){
	.index-search-layout{
		.index-search-list-layout{
			.index-search-list{
				.index-search-list-item{
					font-size: 1em;
				}
			}
		}
	}
}