<div mat-dialog-title class="success-title">
    <mat-icon class="dialog-close-btn" tabindex="0" (click)="close()">close</mat-icon>
</div>
<mat-dialog-content>
    @if(isLoading){
    <div class="spinner-wrapper-index">
        <mat-spinner class="mat-spinner-color"></mat-spinner>
    </div>
    }@else{
    <form [formGroup]="form">
        <span>關鍵字 </span>
        <input type="text" class="form-control" placeholder="請輸入關鍵字" formControlName="keyword">&nbsp;&nbsp;
        <button class="btn-list btn-primary-solid" (click)="search()">查詢</button>
    </form>
    <table class="table-list-layout  table-list-style rwd-table01" style="margin: 1em 0;">
        <tbody>
            <tr class="th-no">
                <th>項次 </th>
                <th> 詞</th>
                <th>功能 </th>
            </tr>
            @if(termList.length>0){
            @for (item of termList; let index= $index;track index) {
            <tr>
                <td class="text-c">
                    <span class="rwd-th">項次</span>
                    {{index+1+(nowPage>1?(nowPage-1)*pageSize:0)}}
                </td>
                <td class="text-c">
                    <span class="rwd-th">詞</span>
                    {{item.name}}
                </td>
                <td class="text-c">
                    <span class="rwd-th">功能</span>
                    <button class="btn-list btn-primary-solid" style="margin-bottom:0.5em;"
                        (click)="select(item)">選擇</button>
                </td>
            </tr>
            }
            }@else{
            <tr>
                <td colspan="3" style="text-align: center;">沒有找到符合條件的資料</td>
            </tr>
            }
        </tbody>
    </table>
    @if(termList.length>0){
    <app-paginator [pageSize]="pageSize" [nowPage]="nowPage" [totalRecords]="totalCount" [pageShowCount]="pageShowCount"
        currentPageReportTemplate="第 {first} 到 {last} 筆，共 {totalRecords} 筆"
        (clickPageEvent)="getPageFromPaginator($event)"
        (pageSizeChangeEvent)="getPageSizeFromPaginator($event)"></app-paginator>
    }
    }
</mat-dialog-content>