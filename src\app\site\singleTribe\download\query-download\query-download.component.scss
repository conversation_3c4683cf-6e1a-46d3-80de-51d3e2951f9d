@use "./scss/query-download.scss";
@use "./scss/word-description-layout.scss";
@use "./scss/second-search.scss";

.breadcrumb-layout {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
}

.breadcrumb-item {
    cursor: pointer;
}

::ng-deep {
    .mdc-tab__text-label {
        font-size: 2em;
    }
    .mdc-tab-indicator__content--underline {
        border-color: #4a7f42 !important;
        border-bottom: 4px solid;
    }
}

//進階搜尋

.advanced-search-cont {
    margin: 0;
    padding: 0;
    display: flex;
    flex-wrap: wrap; // 允許多行排列
    list-style: none;
    align-items: center;
    .advanced-search-item {
        box-sizing: border-box;
        padding: 5px 0; // 調整間距以確保上下對齊
        display: flex;
        align-items: center;
        .checkbox-list {
            padding: 0;
            display: flex;
            // align-items: baseline; // 讓 checkbox 和 label 垂直對齊
        }
        .theme-count {
            color: #0a91d4;
        }
        label {
            font-size: 1.2em;
            margin-left: 5px; // 調整 checkbox 和 label 之間的間距
            // white-space: nowrap;
        }
    }
}

@media (max-width: 3000px) {
    .advanced-search-cont {
        .advanced-search-item {
            width: 50%; // 每行顯示 2 個 checkbox，留一些空白間距
        }
    }
}

@media (max-width: 1200px) {
    .advanced-search-cont {
        .advanced-search-item {
            width: 20%; // 每行顯示 2 個 checkbox，留一些空白間距
        }
    }
}
@media (max-width: 850px) {
    .advanced-search-cont {
        .advanced-search-item {
            width: 25%; // 每行顯示 2 個 checkbox，留一些空白間距
        }
    }
}

@media (max-width: 750px) {
    .advanced-search-cont {
        .advanced-search-item {
            width: 50%; // 每行顯示 2 個 checkbox，留一些空白間距
        }
    }
}
@media (max-width: 410px) {
    .advanced-search-cont {
        .advanced-search-item {
            width: 100%; // 每行顯示 2 個 checkbox，留一些空白間距
        }
    }
}

.word-description-container {
    display: flex;
    align-items: flex-start;
    max-width: 68vw;
}

.advanced-container {
    min-width: 22vw;
    margin-right: 2em;
}

@media (max-width: 1200px) {
    .word-description-container {
        display: flex;
        flex-direction: column;
        max-width: 100%;
        width: 100%;
    }
    .advanced-container {
        width: 90%;
        margin: 0 10px;
        padding: 10px;
    }
    .word-description-list-layout {
        width: 90%;
        margin: 0 10px;
        padding: 10px;
    }
}

mat-icon {
    cursor: pointer;
}

.search-bar {
    padding-right: 45px;
}

@media (max-width: 1600px) and (min-width: 1201px) {
    .search-group {
        flex-wrap: wrap;
    }
}
.search-group {
    width: 100%;
    display: flex;
    flex-direction: row;
    align-items: flex-start;
    //搜尋樣式
    .search-all {
        width: 100%;
        margin: 2px 20px 10px 0;
        display: flex;
        flex-direction: column;
        align-items: center;
        position: relative;
        .search-box {
            width: 100%;
            position: relative;
            display: inline-flex;
            flex-direction: column;
            align-items: center;
            min-width: 225px;
            // margin-right: 20px;
            // margin: 10px 20px 10px 0;
            .search-a1 {
                position: absolute;
                top: 14px;
                right: 0;
                display: block;
                width: 45px;
                height: 45px;
                color: #000;
            }
        }
        //搜尋框架
        .search-frame {
            padding: 20px 10px;
            // max-width: 900px;
            width: 97%;
            box-sizing: border-box;
            background-color: #ffffff;
            border-radius: 10px;
            box-shadow: 0px 0px 5px 2px rgba(0, 0, 0, 0.2);
            position: absolute;
            top: 100%;
        }
        .search-frame-info {
            cursor: pointer;
            font-size: 1.125em;
            // padding: 5px 10px;
            border-radius: 10px;
            width: 100%;
            &:hover,
            &:focus {
                background: #e4e6eb;
                // opacity: 0.5;
            }
            &:active,
            &.active {
                background-color: #e4e6eb;
                border-color: #e4e6eb;
            }
            .search-frame-name {
                padding-left: 10px;
            }
        }
    }
}

@media (max-width: 400px) {
    .search-group {
        flex-wrap: wrap;
        .search-all {
            margin: 0;
            .search-box {
                margin: 10px 0 0 0;
            }
        }
        .btns {
            width: 100%;
            .btn-box {
                width: 100%;
            }
        }
    }
}
