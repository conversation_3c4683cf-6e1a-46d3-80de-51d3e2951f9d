import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import {
  getNewsDetailResp,
  getNewsListReq,
  getNewsListResp,
  getNewsResp,
} from '../../interface/news.interface';

@Injectable({
  providedIn: 'root',
})
export class NewsService {
  constructor(private httpClient: HttpClient) {}

  getNews(): Observable<getNewsResp> {
    return this.httpClient.get<getNewsResp>(
      'api/app/news/home-news'
    );
  }
  getNewsList(req: getNewsListReq): Observable<getNewsListResp> {
    return this.httpClient.post<getNewsListResp>(
      'api/app/news/select-news',
      req
    );
  }
  getNewsDetail(id: string): Observable<getNewsDetailResp> {
    return this.httpClient.post<getNewsDetailResp>(
      'api/app/news/get-news-detail',
      {
        newsId: id,
      }
    );
  }
}
