import { Component, OnInit } from '@angular/core';
import { GetEthnicityService } from '../../../../service/utils/get-ethnicity.service';
import { DownloadService } from '../../../../service/curl/download.service';
import {
  downloadImageReq,
  getImageDownloadClassListResp,
  getImageDownloadListReq,
  getImageDownloadListResp,
  imageClassItem,
  imageDownloadItem,
} from '../../../../interface/download.interface';
import { FormBuilder, FormGroup, FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatDialog } from '@angular/material/dialog';
import { SpinnerService } from '../../../../service/utils/spinner.service';
import { ImageDownloadDialogComponent } from '../../../../dialog/download/image-download-dialog/image-download-dialog.component';
import { ConfirmService } from '../../../../service/utils/confirm.service';
import { UtilsService } from '../../../../service/utils/utils.service';
import { RouterLink } from '@angular/router';
import { NgFor, NgIf } from '@angular/common';
import { PaginatorComponent } from '../../../../utils/paginator/paginator.component';

@Component({
    selector: 'app-card-download',
    templateUrl: './card-download.component.html',
    styleUrl: './card-download.component.scss',
    imports: [
        RouterLink,
        FormsModule,
        ReactiveFormsModule,
        NgFor,
        NgIf,
        PaginatorComponent,
    ],
})
export class CardDownloadComponent implements OnInit {
  form: FormGroup;
  ethnicity: string | null = null;
  tribeId: string | null = null;
  imageClassList: imageClassItem[] = [];

  imageDownloadList: imageDownloadItem[] = [];
  imageList: string[] = [];
  deleteImageList: string[] = [];

  pageSize: number = 10;
  nowPage: number = 1;
  totalCount: number = 0;
  pageShowCount: number = 5; //分頁器秀幾個
  isAllStatus: boolean = false;

  constructor(
    private getEthnicityService: GetEthnicityService,
    private downloadService: DownloadService,
    private formBuilder: FormBuilder,
    private matDialog: MatDialog,
    private spinnerService: SpinnerService,
    private confirmService: ConfirmService,
    private utils:UtilsService
  ) {
    this.form = this.formBuilder.group({
      imageClass: [],
    });
  }

  ngOnInit(): void {
    this.ethnicity = this.getEthnicityService.GetEthnicityName();
    this.tribeId = this.getEthnicityService.GetEthnicityId();
    this.utils.setTitle(`${this.ethnicity}-圖卡下載`)
    this.getImageDownloadClassList();
  }

  getImageDownloadClassList() {
    this.downloadService.getImageDownloadClassList().subscribe({
      next: (resp: getImageDownloadClassListResp) => {
        this.imageClassList = resp.data;
        this.form.setValue({ imageClass: this.imageClassList[0].imageClassId });
        this.getImageDownloadList();
      },
      error: () => {},
    });
  }

  getImageDownloadList() {
    let req: getImageDownloadListReq = {
      tribeId: this.tribeId as string,
      imageClassId: this.form.value.imageClass,
      page: this.nowPage,
      pageSize: this.pageSize,
    };
    this.spinnerService.show();
    this.downloadService.getImageDownloadList(req).subscribe({
      next: (resp: getImageDownloadListResp) => {
        this.spinnerService.hide();
        this.totalCount = resp.data.itemTotalCount;
        this.imageDownloadList = resp.data.searchData;
      },
      error: () => {
        this.spinnerService.hide();
      },
    });
  }

  download() {
    if (
      (!this.isAllStatus && this.imageList.length < 1) ||
      (this.isAllStatus && this.deleteImageList.length === this.totalCount)
    ) {
      this.confirmService.showWARN('請選擇必填欄位。');
      return;
    }
    let req: downloadImageReq = {
      dictionaryIds: this.imageList,
      tribeId: this.tribeId as string,
      imageClassId: this.form.value.imageClass,
      fileType: 0,
      isAll: this.isAllStatus,
      deleteDictionaryIds: this.deleteImageList,
    };
    this.matDialog.open(ImageDownloadDialogComponent, {
      data: { req: req },
    });
  }

  search() {
    this.nowPage = 1;
    this.imageList = [];
    this.deleteImageList = [];
    this.getImageDownloadList();
  }

  isAll(event: Event) {
    const checked = (event.target as HTMLInputElement).checked;
    this.imageList = [];
    this.deleteImageList = [];
    this.isAllStatus = checked;
  }

  changeImage(event: Event, id: string) {
    const checked = (event.target as HTMLInputElement).checked;
    if (this.isAllStatus) {
      if (!checked) {
        this.deleteImageList.push(id);
      } else {
        this.deleteImageList = this.deleteImageList.filter(
          (item) => item !== id
        );
      }
    } else {
      if (checked) {
        this.imageList.push(id);
      } else {
        this.imageList = this.imageList.filter((item) => item !== id);
      }
    }
  }

  checkImage(id: string) {
    if (this.isAllStatus) {
      if (id) {
        return this.deleteImageList.includes(id) ? false : true;
      }
      return true;
    } else {
      if (id) {
        return this.imageList.includes(id) ? true : false;
      }
      return false;
    }
  }

  /**
   * 第幾頁
   * @param item number
   */
  getPageFromPaginator(nowPage: number) {
    this.nowPage = nowPage;
    this.getImageDownloadList();
  }
  /**
   * 每頁資料數量
   * @param item number
   */
  getPageSizeFromPaginator(pageSize: number) {
    this.pageSize = pageSize;
    this.nowPage = 1;
    this.getImageDownloadList();
  }

  back(event:Event) {
    event.preventDefault();
    history.back();
  }
}
