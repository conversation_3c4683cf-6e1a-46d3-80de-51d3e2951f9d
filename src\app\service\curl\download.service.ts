import { HttpClient, HttpResponse } from '@angular/common/http';
import { Injectable } from '@angular/core';
import {
  downloadImageReq,
  downloadQueryFileReq,
  downloadTextFileResp,
  getImageDownloadClassListResp,
  getImageDownloadListReq,
  getImageDownloadListResp,
  getQueryDownloadListReq,
  getQueryDownloadListResp,
} from '../../interface/download.interface';
import { map, Observable } from 'rxjs';

@Injectable({
  providedIn: 'root',
})
export class DownloadService {
  constructor(private httpClient: HttpClient) {}

  /**
   * 取得圖片下載圖片類別
   * @returns
   */
  getImageDownloadClassList(): Observable<getImageDownloadClassListResp> {
    return this.httpClient.get<getImageDownloadClassListResp>(
      'api/app/download/image-class'
    );
  }

  /**
   * 取得圖片下載列表
   * @param req
   * @returns
   */
  getImageDownloadList(
    req: getImageDownloadListReq
  ): Observable<getImageDownloadListResp> {
    return this.httpClient.post<getImageDownloadListResp>(
      'api/app/download/search-image-download-list',
      req
    );
  }

  /**
   * 圖片下載
   * @param req
   * @returns
   */
  downloadImage(req: downloadImageReq): Observable<HttpResponse<Blob>> {
    return this.httpClient.post<Blob>('api/app/download/imageDownload', req, {
      responseType: 'blob' as 'json', // 设置返回类型为 Blob
      observe: 'response', // 获取完整的响应
    });
  }

  /**
   * 全文下載
   * @param tribeId
   * @returns
   */
  downloadTextFile(tribeId: string): Observable<downloadTextFileResp> {
    return this.httpClient.post<downloadTextFileResp>(
      'api/app/download/get-full-text',
      {
        tribeId: tribeId,
      }
    );
  }

  /**
   * 取得選擇下載列表
   * @param req
   * @returns
   */
  getQueryDownloadList(
    req: getQueryDownloadListReq
  ): Observable<getQueryDownloadListResp> {
    return this.httpClient.post<getQueryDownloadListResp>(
      'api/app/download/search-search-download-list',
      req
    );
  }

  downloadQueryFile(req: downloadQueryFileReq): Observable<HttpResponse<Blob>> {
    return this.httpClient.post<Blob>('api/app/download/SearchDownload', req, {
      responseType: 'blob' as 'json', // 设置返回类型为 Blob
      observe: 'response', // 获取完整的响应
    });
  }

  /**
   * 取得檔案名稱
   * @param contentDisposition
   * @returns
   */
  getFilenameFromContentDisposition(contentDisposition: string | null): string {
    if (!contentDisposition) return 'downloaded_file.xlsx';

    const filenameStarRegex = /filename\*=UTF-8''([^;]+)(?:;|$)/i;
    const filenameRegex = /filename[^;=\n]*=([^;]*)/i;

    const matchesStar = filenameStarRegex.exec(contentDisposition);
    if (matchesStar && matchesStar[1]) {
      return decodeURIComponent(matchesStar[1].replace(/['"]/g, ''));
    }

    const matches = filenameRegex.exec(contentDisposition);
    if (matches && matches[1]) {
      return matches[1].replace(/['"]/g, '');
    }

    return 'downloaded_file.xlsx';
  }
}
