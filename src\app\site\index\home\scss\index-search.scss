.index-search-layout {
    margin: 0;
    width: 100%;
    box-sizing: border-box;
}
.index-search-title {
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
    align-items: center;
    width: 100%;
    color: #364250;
    // margin: 20px 0;

    h2 {
        font-size: 1.9em;
        color: #000;
        display: contents;
        color: #364250;
    }
}

//ICON
.material-symbols-outlined {
    &:hover,
    &:focus {
        color: #255d1c;
    }
    &:active,
    &.active {
        color: #255d1c;
    }
}

//下拉選單
// select {
// 	// margin: 10px 0;
// 	padding: 20px 20px;
// 	border-radius: 5px;
// 	background-color: #fff;
// 	border: 1px solid #ccc;
// 	box-shadow: 0 1px 1px rgba(0, 0, 0, 0.075) inset;
// 	box-sizing: border-box;
// 	transition:
// 		border-color 0.15s ease-in-out 0s,
// 		box-shadow 0.15s ease-in-out 0s;
// }

// .input-group{
//     display: flex;
//     flex-direction: row;
//     flex-wrap: wrap;
//     margin-top: 15px;
//     width: 100%;
//     ul{
//         width: 100%;
//         display: flex;
//         flex-direction: row;
//         flex-wrap: wrap;
//         // padding: 20px 40px 0 0;
//         margin: 0;
//         font-size: 1.13em;
//         // .input-list{
//         //     width: 45%;
//         // }
//         li{
//             list-style-type: none;
//             display: flex;
//             width: 100%;
//             .select-item{
//                 padding: 0 10px;
//                 width: 100%;
//                 select{
//                     width: 100%;
//                 }
//             }
//             .announcement-new-words-item{
//                 font-size: 1.13em;
//                 display: flex;
//                 align-items: center;
//                 // min-width: 82px;
//                 justify-content: flex-end;
//                 select{
//                     width: 100%;
//                 }
//             }
//         }
//     }
//     .textarea-list{
//         display: contents;
//     }
// }

//搜尋樣式
.search-group {
    width: 100%;
    display: flex;
    flex-direction: row;
    align-items: center;

    .search-box1 {
        width: 18%;
        position: relative;
        display: inline-flex;
        // margin-right: 20px;
        margin: 10px 20px 10px 0;
        .search-a1 {
            position: absolute;
            top: 24px;
            right: 0;
            display: block;
            width: 55px;
            height: 55px;
            color: #000;
        }
        input {
            cursor: pointer;
        }
    }
    .search-box2 {
        width: 70%;
        position: relative;
        display: inline-flex;
        // margin-right: 20px;
        margin: 10px 20px 10px 0;
        .search-a2 {
            position: absolute;
            top: 24px;
            right: 40px;
            display: block;
            width: 55px;
            height: 55px;
            color: #000;
        }
        .search-a3 {
            position: absolute;
            top: 24px;
            right: 0;
            display: block;
            width: 55px;
            height: 55px;
            color: #000;
        }
    }
}
.search-bar {
    width: 100%;
    // margin: 10px 0;
    padding: 16px 35px;
    background-color: #fff;
    background-image: none;
    border: 1px solid #ccc;
    border-radius: 80px;
    box-shadow: 0 1px 1px rgba(0, 0, 0, 0.075) inset;
    display: inline-block;
    transition:
        border-color 0.15s ease-in-out 0s,
        box-shadow 0.15s ease-in-out 0s;
    box-sizing: border-box;
    box-shadow: 0px 0px 5px 2px rgba(0, 0, 0, 0.2);
    &:focus {
        // border: 3px solid #00b4ff;
        // outline: 0;
        // box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 25%);
        border: 3px solid #4a7f42;
        outline: 0;
        box-shadow: 0 0 0 0.25rem #d8eed4;
    }
}

//按鈕樣式
.btns {
    margin: 10px 0;
}
.btn-box {
    // margin: 5px 5px 5px 0px;
    padding: 20px 40px;
    border-radius: 5px;
    -moz-user-select: none;
    background-image: none;
    border: 1px solid transparent;
    border-radius: 80px;
    cursor: pointer;
    display: inline-block;
    font-weight: 400;
    line-height: 1.2;
    text-align: center;
    font-size: 1.5em;
    white-space: nowrap;
    box-shadow: 0px 0px 5px 2px rgba(0, 0, 0, 0.2);
}

.btn-primary-color {
    background: #4a7f42;
    color: #fff;
    &:hover,
    &:focus {
        background: #255d1c;
        // opacity: 0.5;
    }
    &:active,
    &.active {
        background-color: #255d1c;
        border-color: #255d1c;
    }
}

//鍵盤
.keyboard-group {
    margin: 10px 0;
    padding: 10px;
    max-width: 920px;
    width: 100%;
    box-sizing: border-box;
    background-color: #d8eed4;
    border-radius: 10px;
    box-shadow: 0px 0px 5px 2px rgba(0, 0, 0, 0.2);

    .keyboard-box {
        width: 100%;
        margin: 0;
        padding: 0;
        display: flex;
        flex-direction: row;
        align-items: center;
        justify-content: center;
        flex-wrap: wrap;
        .keyboard-btn {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            background-color: #fff;
            border: 1px solid #949494;
            border-radius: 5px;
            box-shadow: 0px 2px 2px 1px rgba(0, 0, 0, 0.2);
            max-width: 60px;
            width: 100%;
            margin: 10px;
            padding: 5px 0;
            &:hover,
            &:focus {
                background: #255d1c;
                border-color: #255d1c;
                color: #fff;
                // opacity: 0.5;
            }
            &:active,
            &.active {
                background-color: #255d1c;
                border-color: #255d1c;
            }
            .keyboard_font {
                font-size: 1em;
            }
        }
    }
}

//文字大小
.font_30 {
    font-size: 1.875em;
}
.font_24 {
    font-size: 1.5em;
}
.font_18 {
    font-size: 1.125em;
}
.font_16 {
    font-size: 1em;
}

//文字間距
.font_r10 {
    margin-right: 10px;
}
.font_l10 {
    margin-left: 10px;
}

@media (max-width: 1000px) {
    .search-group {
        .search-box1 {
            .font_24 {
                font-size: 1.125em;
            }
            .search-a1 {
                top: 20px;
            }
        }
        .search-box2 {
            width: 48%;
            .font_24 {
                font-size: 1.125em;
            }
            .search-a2 {
                top: 20px;
            }
            .search-a3 {
                top: 20px;
            }
        }
        .btns {
            .btn-box {
                width: 100%;
                font-size: 1.125em;
            }
        }
    }
    .keyboard-group {
        padding: 5px;
        .keyboard-box {
            .keyboard-btn {
                max-width: 45px;
                width: 100%;
                padding: 0;
                margin: 4px;
                .keyboard_font {
                    font-size: 0.75em;
                }
            }
        }
    }
}

@media (max-width: 750px) {
    .index-search-layout {
        flex-wrap: wrap;
        padding: 20px;
        .search-group {
            flex-wrap: wrap;
            .search-box1 {
                width: 100%;
                margin: 10px 0;
            }
            .search-box2 {
                width: 100%;
                margin: 10px 0;
            }
            .btns {
                width: 100%;
            }
        }
    }
}
