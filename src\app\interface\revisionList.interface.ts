import {
  searchDictionaryReq,
  searchDictionaryResp,
} from './language.interface';
import { defaultItem } from './share.interface';

export interface searchRevisionListReq {
  type: number; // 1 = 修訂一覽表，2 = 書寫系統
  search: searchDictionaryReq;
}

export interface searchRevisionListResp extends searchDictionaryResp {}

export interface getRevisionResp extends defaultItem {
  data: {
    items: revisionItem[];
  };
}

export interface revisionItem {
  id: string;
  before: string;
  after: string;
  note: string;
  creationTime: string;
}

export interface getRevisionListReq {
  page: number;
  pageSize: number;
  type: number; // 1修訂2書寫
}

export interface getRevisionListResp extends defaultItem {
  data: {
    page: number;
    pageSize: number;
    pageTotalCount: number;
    itemTotalCount: number;
    items: revisionListItem[];
  };
}

export interface revisionListItem {
  dictionaryId: string;
  revisionTime: string;
  tribeName: string;
  dialectName: string;
  explanation: string;
  before: string;
  after: string;
  note: string;
}
