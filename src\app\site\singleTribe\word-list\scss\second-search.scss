.second-search-layout {
    margin: 0;
    width: 100%;
    box-sizing: border-box;
}
.second-search-list {
    display: flex;
    flex-direction: column;
    border: 1px solid #65676b;
    border-radius: 5px;
    margin: 10px 0;
    box-sizing: border-box;
}
.second-search-box {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    flex-wrap: wrap;
    align-items: center;
    padding: 10px 20px;
    background-color: #4a7f42;
}
.second-search-box2 {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    flex-wrap: nowrap;
    // align-items: center;
    padding: 20px 20px;
    background-color: #fff;
    border-radius: 5px;
    .input-group {
        width: 100%;
        display: flex;
        flex-direction: column;
        margin-top: 15px;
        .second-search-item {
            font-size: 1.13em;
            font-weight: bold;
            display: flex;
            align-items: center;
        }
        .select-group {
            width: 100%;
            display: flex;
            justify-content: space-between;
            box-sizing: border-box;
            flex-wrap: nowrap;
            .select-box {
                width: 100%;
            }
            .font_to {
                font-size: 1.125em;
                display: flex;
                align-items: center;
                padding: 10px;
            }
        }
        select {
            padding: 15px 20px;
        }
        .checkbox-group {
            width: 100%;
            display: flex;
            flex-direction: row;
            flex-wrap: wrap;
            .checkbox-menu {
                display: flex;
                align-items: stretch;
                font-size: 1.125em;
                padding-left: 0;
                max-width: 140px;
                width: 100%;
                .checkbox-list {
                    width: 18px;
                }
                label {
                    padding-left: 5px;
                }
            }
        }
    }
}
.second-search-title {
    cursor: pointer;
    display: flex;
    flex-direction: row;
    // justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    .term-list-language {
        font-size: 1.13em;
        color: #65676b;
    }
    .second-search-h4 {
        font-size: 1.5em;
        font-weight: bold;
        // padding: 0 10px;
    }
}
.second-search-cont {
    width: 100%;
    display: flex;
    flex-direction: column;
    align-content: space-around;
    align-items: flex-start;
    margin-top: 10px;
    .second-search-choose {
        width: 100%;
        display: flex;
        flex-direction: row;
        align-items: center;
        border: 1px solid #4a7f42;
        border-radius: 5px;
        margin: 10px 0;
    }
}

//搜尋樣式
.search-group {
    width: 100%;
    display: flex;
    flex-direction: row;
    align-items: center;

    .search-box {
        width: 100%;
        position: relative;
        display: inline-flex;
        // margin-right: 20px;
        margin: 10px 20px 10px 0;
        .search-a1 {
            position: absolute;
            top: 14px;
            right: 0;
            display: block;
            width: 45px;
            height: 45px;
            color: #000;
        }
    }
}
.search-bar {
    width: 100%;
    // margin: 10px 0;
    padding: 10px 30px;
    background-color: #fff;
    background-image: none;
    border: 1px solid #ccc;
    border-radius: 80px;
    box-shadow: 0 1px 1px rgba(0, 0, 0, 0.075) inset;
    display: inline-block;
    transition:
        border-color 0.15s ease-in-out 0s,
        box-shadow 0.15s ease-in-out 0s;
    box-sizing: border-box;
    box-shadow: 0px 0px 5px 2px rgba(0, 0, 0, 0.2);
    &:focus {
        // border: 3px solid #00b4ff;
        // outline: 0;
        // box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 25%);
        border: 3px solid #4a7f42;
        outline: 0;
        box-shadow: 0 0 0 0.25rem #d8eed4;
    }
}

//按鈕樣式
.btns {
    margin: 10px 0;
}
.btn-box {
    // margin: 5px 5px 5px 0px;
    padding: 14px 30px;
    border-radius: 5px;
    -moz-user-select: none;
    background-image: none;
    border: 1px solid transparent;
    border-radius: 80px;
    cursor: pointer;
    display: inline-block;
    font-weight: 400;
    line-height: 1.2;
    text-align: center;
    font-size: 1.125em;
    white-space: nowrap;
    box-shadow: 0px 0px 5px 2px rgba(0, 0, 0, 0.2);
}

.btn-primary-color {
    background: #4a7f42;
    color: #fff;
    &:hover,
    &:focus {
        background: #255d1c;
        // opacity: 0.5;
    }
    &:active,
    &.active {
        background-color: #255d1c;
        border-color: #255d1c;
    }
}

//鍵盤
.keyboard-group {
    margin: 0;
    padding: 0;
    // max-width: 520px;
    width: 100%;
    box-sizing: border-box;
    // background-color: #D8EED4;
    background-color: white;
    border-radius: 0px;
    box-shadow: none;

    .keyboard-box {
        width: 100%;
        margin: 0;
        padding: 0;
        padding-left: 15px;
        display: flex;
        flex-direction: row;
        align-items: center;
        justify-content: flex-start;
        flex-wrap: wrap;

        .keyboard-btn {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            background-color: #fff;
            border: 1px solid #949494;
            border-radius: 5px;
            box-shadow: 0px 2px 2px 1px rgba(0, 0, 0, 0.2);
            max-width: 40px;
            width: 100%;
            margin: 5px;
            // padding: 5px 0;
            &:hover,
            &:focus {
                background: #255d1c;
                border-color: #255d1c;
                color: #fff;
                // opacity: 0.5;
            }
            &:active,
            &.active {
                background-color: #255d1c;
                border-color: #255d1c;
            }
            .keyboard_font {
                font-size: 0.75em;
                line-height: 2;
            }
        }
    }
}

//文字大小
.font_30 {
    font-size: 1.875em;
}
.font_24 {
    font-size: 1.5em;
}
.font_18 {
    font-size: 1.125em;
}
.font_16 {
    font-size: 1em;
}

//字體顏色
.font_g {
    color: #4a7f42;
}
.font_b {
    color: #000;
}
.font_w {
    color: #fff;
}

@media (max-width: 400px) {
    .search-group {
        flex-wrap: wrap;
        .search-box {
            margin: 10px 0;
        }
        .btns {
            width: 100%;
            .btn-box {
                width: 100%;
            }
        }
    }
}
