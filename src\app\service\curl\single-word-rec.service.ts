import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import {
  getRecListReq,
  getRecListResp,
  postMyRecReq,
} from '../../interface/single-word-rec.interface';
import { Observable } from 'rxjs';
import { defaultItem } from '../../interface/share.interface';

@Injectable({
  providedIn: 'root',
})
export class SingleWordRecService {
  constructor(private httpClient: HttpClient) {}

  /**
   * 我的單詞推薦
   * @param req 
   * @returns 
   */
  postMyRec(req: postMyRecReq): Observable<defaultItem> {
    return this.httpClient.post<defaultItem>(
      'api/app/dictionary-recommend/recommend-publish',
      req
    );
  }

  /**
   * 單辭推薦列表
   * @param req 
   * @returns 
   */
  getRecList(req: getRecListReq): Observable<getRecListResp> {
    return this.httpClient.post<getRecListResp>(
      'api/app/dictionary-recommend/recommend-select',
      req
    );
  }
}
