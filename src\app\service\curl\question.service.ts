import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import {
  addQuestionReq,
  addSysQuestionReq,
  getAnswerListResp as getAnswerReplyResp,
  getAutoCompleteQuestionResp,
  getDictionaryChineseExplanationResp,
  getDictionaryQuestionListReq,
  getDictionaryQuestionListResp,
  getDictionarySentenceResp,
  getDictionaryWordListReq,
  getDictionaryWordListResp,
  getQuestionListResp,
  getSysQuestionListReq,
  getSysQuestionListResp,
} from '../../interface/question.interface';
import { defaultItem } from '../../interface/share.interface';

@Injectable({
  providedIn: 'root',
})
export class QuestionService {
  constructor(private httpClient: HttpClient) {}

  /**
   * 取得question送出用驗證碼png+mp3
   * @returns
   */
  getCaptcha(): Observable<Blob> {
    return this.httpClient.get(
      'api/app/dictionary-verify/verify-session-bind',
      {
        responseType: 'blob',
      }
    );
  }

  /**
   * 取得自動帶入辭典內容進question
   * @param id
   * @returns
   */
  getAutoCompleteQuestion(id: string): Observable<getAutoCompleteQuestionResp> {
    return this.httpClient.post<getAutoCompleteQuestionResp>(
      'api/app/dictionary-opinion/get-opinion-fields',
      {
        dictionaryId: id,
      }
    );
  }

  getDictionaryQuestionList(
    req: getDictionaryQuestionListReq
  ): Observable<getDictionaryQuestionListResp> {
    return this.httpClient.post<getDictionaryQuestionListResp>(
      'api/app/dictionary-opinion/opinion-select',
      req
    );
  }
  getSysQuestionList(
    req: getSysQuestionListReq
  ): Observable<getSysQuestionListResp> {
    return this.httpClient.post<getSysQuestionListResp>(
      'api/app/dictionary-opinion/system-opinion-select',
      req
    );
  }

  addQuestion(req: addQuestionReq): Observable<defaultItem> {
    return this.httpClient.post<defaultItem>(
      'api/app/dictionary-opinion/opinion-publish',
      req
    );
  }

  addSysQuestion(req: addSysQuestionReq): Observable<defaultItem> {
    return this.httpClient.post<defaultItem>(
      'api/app/dictionary-opinion/system-opinion-publish',
      req
    );
  }

  getQuestionList(id: string): Observable<getQuestionListResp> {
    return this.httpClient.post<getQuestionListResp>(
      'api/app/dictionary-opinion/get-opinion',
      {
        dictionaryId: id,
      }
    );
  }

  getAnswerReply(id: string): Observable<getAnswerReplyResp> {
    return this.httpClient.post<getAnswerReplyResp>(
      'api/app/dictionary-opinion/get-opinion-reply',
      {
        opinionId: id,
      }
    );
  }

  /**
   *
   */
  getDictionaryWordList(
    req: getDictionaryWordListReq
  ): Observable<getDictionaryWordListResp> {
    return this.httpClient.post<getDictionaryWordListResp>(
      'api/app/dictionary-opinion/pick-word',
      req
    );
  }
  /**
   *
   */
  getDictionaryChineseExplanation(
    dictionaryId: string
  ): Observable<getDictionaryChineseExplanationResp> {
    return this.httpClient.post<getDictionaryChineseExplanationResp>(
      'api/app/dictionary-opinion/pick-explanation',
      {
        dictionaryId: dictionaryId,
      }
    );
  }
  /**
   *
   */
  getDictionarySentence(id: string): Observable<getDictionarySentenceResp> {
    return this.httpClient.post<getDictionarySentenceResp>(
      'api/app/dictionary-opinion/pick-sentence',
      {
        explanationId: id,
      }
    );
  }
}
