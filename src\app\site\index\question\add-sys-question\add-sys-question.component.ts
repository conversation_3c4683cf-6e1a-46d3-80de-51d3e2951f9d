import { Component, On<PERSON><PERSON>roy } from '@angular/core';
import {
  FormGroup,
  FormBuilder,
  Validators,
  FormsModule,
  ReactiveFormsModule,
} from '@angular/forms';
import { QuestionService } from '../../../../service/curl/question.service';
import { ProcessingBlobFilesService } from '../../../../service/utils/processing-blob-files.service';
import { SafeHtml } from '@angular/platform-browser';
import { addSysQuestionReq } from '../../../../interface/question.interface';
import { defaultItem } from '../../../../interface/share.interface';
import { ConfirmService } from '../../../../service/utils/confirm.service';
import { apiStatus } from '../../../../enum/apiStatus.enum';
import { Router, RouterLink } from '@angular/router';
import { UtilsService } from '../../../../service/utils/utils.service';
import { MatIcon } from '@angular/material/icon';

@Component({
  selector: 'app-add-sys-question',
  templateUrl: './add-sys-question.component.html',
  styleUrl: './add-sys-question.component.scss',
  imports: [RouterLink, FormsModule, ReactiveFormsModule, MatIcon],
})
export class AddSysQuestionComponent implements OnDestroy {
  form: FormGroup;

  img!: SafeHtml;
  audio!: string;
  sessionId: string = '';
  private currentMediaElement: HTMLAudioElement | null = null;
  constructor(
    private formBuilder: FormBuilder,
    private processingBlobFilesService: ProcessingBlobFilesService,
    private questionService: QuestionService,
    private router: Router,
    private confirmService: ConfirmService,
    private utils: UtilsService
  ) {
    this.form = this.formBuilder.group({
      name: ['', Validators.required],
      email: ['', Validators.required],
      content: ['', Validators.required],
      captcha: ['', Validators.required],
    });
  }

  ngOnInit(): void {
    this.utils.setTitle('新增系統回饋');
    this.getCaptcha();
  }
  ngOnDestroy(): void {
    // 離開頁面時停止並移除播放器
    this.stopMusic();
  }
  stopMusic() {
    if (this.currentMediaElement) {
      this.currentMediaElement.pause();
      this.currentMediaElement.remove();
      this.currentMediaElement = null;
    }
  }

  getCaptcha() {
    this.stopMusic();
    this.questionService.getCaptcha().subscribe({
      next: (resp: Blob) => {
        const reader = new FileReader();

        // 使用 FileReader 读取 Blob 数据
        reader.onload = () => {
          const data = reader.result as ArrayBuffer;

          // 转换为 Uint8Array 以便处理二进制数据
          const uint8Array = new Uint8Array(data);

          // 定义边界标记
          const boundary = new TextEncoder().encode('--file_boundary\r\n');

          // 分割多部分内容
          const parts = this.processingBlobFilesService.splitByBoundary(
            uint8Array,
            boundary
          );

          // 处理每一部分内容
          parts.forEach((part) => {
            const contentType =
              this.processingBlobFilesService.extractContentType(part);
            const contentData =
              this.processingBlobFilesService.extractContentData(part);

            if (contentType === 'image/png') {
              // 创建图片 Blob 并显示图片
              const imageBlob = new Blob([contentData], { type: 'image/png' });
              this.img =
                this.processingBlobFilesService.getSafeImageUrl(imageBlob);
            } else if (contentType === 'audio/mpeg') {
              // 创建音频 Blob 并播放音频
              const audioBlob = new Blob([contentData], { type: 'audio/mpeg' });
              this.audio = URL.createObjectURL(audioBlob);
            } else {
              // 嘗試解析 sessionId
              const sessionText = new TextDecoder().decode(contentData);
              if (
                sessionText.trim().startsWith('{') &&
                sessionText.trim().endsWith('}')
              ) {
                // 嘗試解析 JSON
                const json = JSON.parse(sessionText);
                if (json.sessionId) {
                  this.sessionId = json.sessionId;
                }
              }
            }
          });
        };

        reader.readAsArrayBuffer(resp);
      },
      error: () => {},
    });
  }

  play() {
    this.stopMusic();

    let mediaElement: HTMLAudioElement = document.createElement('audio');

    mediaElement.style.display = 'none'; // 這行讓音頻播放器隱藏
    mediaElement.setAttribute('src', this.audio); // 設置音頻源
    mediaElement.setAttribute('controls', 'true'); // 加入控制條
    document.body.appendChild(mediaElement);
    mediaElement.play();

    this.currentMediaElement = mediaElement; // 儲存當前的音樂播放器
  }

  send() {
    if (!this.form.valid) {
      this.confirmService.showWARN('必填欄位尚未填寫');
    }
    let req: addSysQuestionReq = {
      name: this.form.value.name,
      email: this.form.value.email,
      content: this.form.value.content,
      verifyCode: this.form.value.captcha,
      sessionId: this.sessionId,
    };

    this.questionService.addSysQuestion(req).subscribe({
      next: (resp: defaultItem) => {
        if (resp.status === apiStatus.SUCCESS) {
          this.confirmService
            .showSUCCESS('您的意見回覆將收錄在系統回饋頁面，您可進行查看')
            .afterClosed()
            .subscribe(() => {
              this.router.navigate(['question/sysList']);
            });
        } else {
          this.confirmService.showError(resp.message, '錯誤');
        }
      },
      error: () => {},
    });
  }

  back(event: Event) {
    event.preventDefault();
    history.back();
  }
}
