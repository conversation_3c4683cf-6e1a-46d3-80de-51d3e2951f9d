import { Component } from '@angular/core';
import { MatDialog } from '@angular/material/dialog';
import { SelectTermDialogComponent } from '../../../../dialog/select-term-dialog/select-term-dialog.component';
import { QuestionService } from '../../../../service/curl/question.service';
import { ProcessingBlobFilesService } from '../../../../service/utils/processing-blob-files.service';
import { SafeHtml } from '@angular/platform-browser';
import { LanguageService } from '../../../../service/curl/language.service';
import { getDialectAndLanguageListResp } from '../../../../interface/language.interface';
import { apiStatus } from '../../../../enum/apiStatus.enum';
import {
  addQuestionReq,
  getDictionaryChineseExplanationResp,
  getDictionarySentenceResp,
} from '../../../../interface/question.interface';
import { defaultItem } from '../../../../interface/share.interface';
import { ConfirmService } from '../../../../service/utils/confirm.service';
import { Router, RouterLink } from '@angular/router';
import { UtilsService } from '../../../../service/utils/utils.service';
import { FormsModule } from '@angular/forms';
import { NgClass } from '@angular/common';
import { MatIcon } from '@angular/material/icon';

@Component({
  selector: 'app-add-question',
  templateUrl: './add-question.component.html',
  styleUrl: './add-question.component.scss',
  imports: [RouterLink, FormsModule, NgClass, MatIcon],
})
export class AddQuestionComponent {
  tribe: string | null = null;
  dialect: string | null = null;
  chineseExplanationId: string = '';
  chineseExplanationGroup: {
    id: string;
    chineseExplanation: string;
  } | null = null;
  sentence: string | null = null;
  name: string = '';
  email: string = '';
  content: string = '';
  captcha: string = '';
  sessionId: string = '';

  languageList: { id: string; name: string }[] = [];
  chineseExplanationList: {
    id: string; // 解釋ID
    chineseExplanation: string; // 解釋
  }[] = [];
  sentenceList: string[] = [];
  tempDialectList: {
    id: string;
    name: string;
    tribeId: string;
  }[] = [];
  dialectList: {
    id: string;
    name: string;
    tribeId: string;
  }[] = [];

  dictionaryId: string = '';
  dictionaryName: string = '';

  img!: SafeHtml;
  audio!: string;
  private currentMediaElement: HTMLAudioElement | null = null;
  constructor(
    private matDialog: MatDialog,
    private confirmService: ConfirmService,
    private processingBlobFilesService: ProcessingBlobFilesService,
    private languageService: LanguageService,
    private questionService: QuestionService,
    private utils: UtilsService,
    private router: Router
  ) {}

  ngOnInit(): void {
    this.utils.setTitle('新增詞項回饋');
    this.getDialectAndLanguageList();
    this.getCaptcha();
  }

  getDialectAndLanguageList() {
    this.languageService.getDialectAndLanguageList().subscribe({
      next: (resp: getDialectAndLanguageListResp) => {
        if (resp.status === apiStatus.SUCCESS) {
          const { tribes } = resp.data;
          this.languageList = tribes;
          this.tempDialectList = tribes.flatMap((tribe) =>
            tribe.dialectList.map((dialect) => ({
              ...dialect,
            }))
          );
        }
      },
      error: () => {},
    });
  }

  selectTribe() {
    this.dialect = null;
    this.chineseExplanationGroup = null;
    this.chineseExplanationId = '';
    this.sentence = null;
    this.dictionaryId = '';
    this.dictionaryName = '';
    const selectedDialects = this.tempDialectList.filter(
      (item) => item.tribeId === this.tribe
    );
    this.dialectList = selectedDialects.length > 0 ? selectedDialects : [];
  }

  selectDialect() {
    this.chineseExplanationGroup = null;
    this.sentence = null;
    this.dictionaryId = '';
    this.dictionaryName = '';
  }

  selectTerm() {
    this.matDialog
      .open(SelectTermDialogComponent, {
        disableClose: true,
        maxWidth: '100vw',
        data: {
          tribeId: this.tribe,
          dialectId: this.dialect,
        },
      })
      .afterClosed()
      .subscribe((resp: { id: string; name: string }) => {
        this.dictionaryId = resp.id;
        this.dictionaryName = resp.name;
        this.chineseExplanationGroup = null;
        this.sentence = null;
        this.questionService
          .getDictionaryChineseExplanation(resp.id)
          .subscribe({
            next: (resp: getDictionaryChineseExplanationResp) => {
              this.chineseExplanationList = resp.data.explanationItems;
            },
            error: () => {},
          });
      });
  }

  selectChineseExplanation() {
    this.sentence = null;
    this.questionService
      .getDictionarySentence(this.chineseExplanationGroup?.id as string)
      .subscribe({
        next: (resp: getDictionarySentenceResp) => {
          this.sentenceList = resp.data.originalSentence;
        },
      });
  }

  getCaptcha() {
    this.questionService.getCaptcha().subscribe({
      next: (resp: Blob) => {
        const reader = new FileReader();

        // 使用 FileReader 读取 Blob 数据
        reader.onload = () => {
          const data = reader.result as ArrayBuffer;

          // 转换为 Uint8Array 以便处理二进制数据
          const uint8Array = new Uint8Array(data);

          // 定义边界标记
          const boundary = new TextEncoder().encode('--file_boundary\r\n');

          // 分割多部分内容
          const parts = this.processingBlobFilesService.splitByBoundary(
            uint8Array,
            boundary
          );

          // 处理每一部分内容
          parts.forEach((part) => {
            const contentType =
              this.processingBlobFilesService.extractContentType(part);
            const contentData =
              this.processingBlobFilesService.extractContentData(part);
            if (contentType === 'image/png') {
              // 创建图片 Blob 并显示图片
              const imageBlob = new Blob([contentData], { type: 'image/png' });
              this.img =
                this.processingBlobFilesService.getSafeImageUrl(imageBlob);
            } else if (contentType === 'audio/mpeg') {
              // 创建音频 Blob 并播放音频
              const audioBlob = new Blob([contentData], { type: 'audio/mpeg' });
              this.audio = URL.createObjectURL(audioBlob);
            } else {
              // 嘗試解析 sessionId
              const sessionText = new TextDecoder().decode(contentData);
              if (
                sessionText.trim().startsWith('{') &&
                sessionText.trim().endsWith('}')
              ) {
                // 嘗試解析 JSON
                const json = JSON.parse(sessionText);
                if (json.sessionId) {
                  this.sessionId = json.sessionId;
                }
              }
            }
          });
        };

        reader.readAsArrayBuffer(resp);
      },
      error: () => {},
    });
  }

  play() {
    if (this.currentMediaElement) {
      this.currentMediaElement.pause();
      this.currentMediaElement.remove();
    }

    let mediaElement: HTMLAudioElement = document.createElement('audio');

    mediaElement.style.display = 'none'; // 這行讓音頻播放器隱藏
    mediaElement.setAttribute('src', this.audio); // 設置音頻源
    mediaElement.setAttribute('controls', 'true'); // 加入控制條
    document.body.appendChild(mediaElement);
    mediaElement.play();

    this.currentMediaElement = mediaElement; // 儲存當前的音樂播放器
  }

  checkValid(): boolean {
    if (
      this.tribe &&
      this.chineseExplanationGroup &&
      this.sentence &&
      this.dictionaryId &&
      this.name &&
      this.email &&
      this.content &&
      this.captcha
    ) {
      return true; // 所有屬性都有值
    }
    return false; // 至少有一個屬性為空
  }

  send() {
    if (!this.checkValid()) {
      this.confirmService.showWARN('必填欄位尚未填寫');
      return;
    }
    let req: addQuestionReq = {
      dictionaryId: this.dictionaryId,
      chineseExplanation: this.chineseExplanationGroup
        ?.chineseExplanation as string,
      originalSentence: this.sentence as string,
      name: this.name,
      email: this.email,
      content: this.content,
      verifyCode: this.captcha,
      sessionId:this.sessionId
    };
    this.questionService.addQuestion(req).subscribe({
      next: (resp: defaultItem) => {
        if (resp.status === apiStatus.SUCCESS) {
          this.confirmService
            .showSUCCESS('您的意見回覆將收錄在詞項回饋頁面，您可進行查看')
            .afterClosed()
            .subscribe(() => {
              this.router.navigate(['question/list']);
            });
        } else {
          this.confirmService.showError(resp.message, '錯誤');
        }
      },
    });
  }

  back(event: Event) {
    event.preventDefault();
    history.back();
  }
}
