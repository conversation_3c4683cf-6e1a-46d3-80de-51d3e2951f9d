import { defaultItem } from './share.interface';

export interface getSearchOptionResp extends defaultItem {
  data: {
    mainDialectId: string | null;
    levelItems: {
      value: number;
      name: string;
    }[]; // 級別
    dialectItems: {
      id: string;
      name: string;
      selected?: boolean; // 新增 selected 属性
    }[]; // 語別(方言)
    categoryItems: {
      id: string;
      name: string;
    }[]; // 類別
  };
}

export interface getLearningDataReq {
  page: number;
  pageSize: number;
  tribeId: string; // 族ID
  dialectId: string[];
  categories: string[];
}
export interface getLearningDataResp extends defaultItem {
  data: {
    wordItems: wordItem[]; // 列表
    page: number;
    pageSize: number;
    pageTotalCount: number;
    itemTotalCount: number;
  };
}

export interface wordItem {
  id: string; // 詞ID
  level: string; // 級別
  dialect: string; // 語別(方言)
  category: string; // 類別(範疇)
  name: string; // 單字名稱
  chineseExplanation: string; // 中文解釋
}

export interface getQuestionResp extends defaultItem {
  data: {
    questionItems: {
      id: string; // 題目解釋ID (當答案送出)
      dictionaryName: string; // 題目中文
    }[];
    quizImageUrl: string; // 圖片URL
  };
}
export interface sendAnswerResp extends defaultItem {
  data: {
    correct: boolean; // 正確?
    correctAnswer: string; // 正確答案ID
    resultMessage: string; // 訊息(可無視)
  };
}

export interface getOnlineTestLinkResp extends defaultItem {
  data: {
    gameUrls: string[];
  };
}
