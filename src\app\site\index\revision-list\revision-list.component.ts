import {
  AfterViewInit,
  Component,
  ElementRef,
  OnDestroy,
  OnInit,
  Renderer2,
  ViewChild,
} from '@angular/core';
import { ActivatedRoute, Router, RouterLink } from '@angular/router';
import { LanguageService } from '../../../service/curl/language.service';
import {
  audioItem,
  dictionaryItem,
  getDialectAndLanguageListResp,
  searchDictionaryDetailResp,
} from '../../../interface/language.interface';
import { apiStatus } from '../../../enum/apiStatus.enum';
import { HttpErrorResponse } from '@angular/common/http';
import { MatDialog } from '@angular/material/dialog';
import {
  MatTabGroup,
  MatTabChangeEvent,
  MatTab,
  MatTabLabel,
} from '@angular/material/tabs';
import { DomSanitizer, SafeHtml } from '@angular/platform-browser';
import { OwlOptions, CarouselModule } from 'ngx-owl-carousel-o';
import { environment } from '../../../../environments/environment';
import { AnaphoraSentenceDialogComponent } from '../../../dialog/anaphora-sentence-dialog/anaphora-sentence-dialog.component';
import {
  autoSearchResp,
  KeyboardList,
} from '../../../interface/share.interface';
import { FileService } from '../../../service/curl/file.service';
import { ConfirmService } from '../../../service/utils/confirm.service';
import { SearhModel } from '../../../enum/SearhModel .enum';
import { RevisionListService } from '../../../service/curl/revision-list.service';
import {
  getRevisionListReq,
  getRevisionListResp,
  getRevisionResp,
  revisionItem,
  revisionListItem,
  searchRevisionListReq,
  searchRevisionListResp,
} from '../../../interface/revisionList.interface';
import { RootStructureDialogComponent } from '../../../dialog/root-structure-dialog/root-structure-dialog.component';
import { UtilsService } from '../../../service/utils/utils.service';
import { debounceTime, Subject } from 'rxjs';
import { ShareService } from '../../../service/curl/share.service';
import { FormsModule } from '@angular/forms';
import { MatIcon } from '@angular/material/icon';
import {
  MatAccordion,
  MatExpansionPanel,
  MatExpansionPanelHeader,
} from '@angular/material/expansion';
import { MatProgressSpinner } from '@angular/material/progress-spinner';
import { NgClass, DatePipe } from '@angular/common';
import { PaginatorComponent } from '../../../utils/paginator/paginator.component';

export enum ShareType {
  FB = 'fb',
  LINE = 'line',
  IG = 'ig',
}

declare const FB: any;

@Component({
  selector: 'app-revision-list',
  templateUrl: './revision-list.component.html',
  styleUrl: './revision-list.component.scss',
  imports: [
    RouterLink,
    FormsModule,
    MatIcon,
    MatTabGroup,
    MatTab,
    MatTabLabel,
    MatAccordion,
    MatExpansionPanel,
    MatExpansionPanelHeader,
    MatProgressSpinner,
    NgClass,
    CarouselModule,
    PaginatorComponent,
    DatePipe,
  ],
})
export class RevisionListComponent implements OnInit, AfterViewInit, OnDestroy {
  @ViewChild('tabGroup') tabGroup!: MatTabGroup;
  @ViewChild('keywordInput') keywordInput!: ElementRef;
  @ViewChild('searchAll') searchAll!: ElementRef;
  tab: number = 1;
  keyword: string = '';
  private keywordChanged: Subject<string> = new Subject<string>();
  autoInputList: string[] = [];
  showKeyword: string = '';

  isKeyboard: boolean = true;
  isLanguard: boolean = false;
  isDialect: boolean = false;
  fontSize: number = 1;

  revisionListSearch?: {
    keyword: string;
    selectedDialectList: string[];
    selectedLanguageList: string[];
  };

  languageList: { id: string; name: string; active?: boolean }[] = [];
  dialectList: {
    id: string;
    name: string;
    active: boolean;
    disabled: boolean;
    tribeId: string;
  }[] = [];

  accurateList: any[] = [{ name: 'bway' }, { name: 'buwa' }];
  keyboardList = KeyboardList;
  pageSize: number = 10;
  nowPage: number = 1;
  activePage: number = 1;
  totalPage: number = 0;
  totalCount: number = 0;
  pageShowCount: number = 5; //分頁器秀幾個

  dictionaryList: dictionaryItem[] = [];

  selectedDialectList: string[] = [];
  selectedLanguageList: string[] = [];

  searchGroup: SearhModel[] = [SearhModel.Accurate, SearhModel.Fuzzy];

  searhModel: SearhModel = SearhModel.Accurate;

  shareType = ShareType;

  private currentMediaElement: HTMLAudioElement | null = null;

  customOptions: OwlOptions = {
    loop: false,
    navSpeed: 700,
    dots: true,
    items: 1,
    center: true,
  };

  revisionListPageSize: number = 10;
  revisionListNowPage: number = 1;
  revisionListTotalCount: number = 0;
  dictionaryId: string | null = null;
  constructor(
    private activatedRoute: ActivatedRoute,
    private router: Router,
    private languageService: LanguageService,
    private revisionListService: RevisionListService,
    private confirmService: ConfirmService,
    private sanitizer: DomSanitizer,
    private matDialog: MatDialog,
    private fileService: FileService,
    private utils: UtilsService,
    private shareService: ShareService,
    private renderer: Renderer2
  ) { }

  ngOnInit(): void {
    this.utils.fontSize$.subscribe((size) => {
      this.fontSize = size === 0 ? 1 : size;
    });

    this.activatedRoute.queryParamMap.subscribe((queryParamMap) => {
      if (queryParamMap.get('tab')) {
        this.tab = parseInt(queryParamMap.get('tab')!);
      }
      if (
        queryParamMap.get('dictionaryId') &&
        queryParamMap.get('dictionary')
      ) {
        this.dictionaryId = queryParamMap.get('dictionaryId')!;
        this.showKeyword = queryParamMap.get('dictionary')!;
        this.selectedLanguageList = [];
        this.selectedDialectList = [];
        this.languageList = [];
        this.dialectList = [];
        this.nowPage = 1;
        this.getDialectAndLanguageList();
        this.searchAccurate(
          this.selectedLanguageList,
          this.selectedDialectList
        );
      }
    });

    if (this.tab === 1) {
      this.getDialectAndLanguageList();
      this.getRevisionList();
    } else {
      if (
        !this.revisionListSearch &&
        sessionStorage.getItem('revisionListSearch')
      ) {
        this.revisionListSearch = JSON.parse(
          sessionStorage.getItem('revisionListSearch')!
        );
      }

      this.selectedDialectList = this.revisionListSearch
        ?.selectedDialectList as string[];
      this.selectedLanguageList = this.revisionListSearch
        ?.selectedLanguageList as string[];

      this.keyword = this.revisionListSearch
        ? this.revisionListSearch?.keyword
        : '';

      if (!this.showKeyword) {
        this.showKeyword = this.keyword;
      }
      this.getDialectAndLanguageList();
      this.searchAccurate(this.selectedLanguageList, this.selectedDialectList);
    }

    this.utils.setTitle('修訂一覽表');
    this.keywordChanged.pipe(debounceTime(500)).subscribe((value: string) => {
      if (value) {
        this.callAutoInputAPI(value);
      } else {
        this.autoInputList = [];
      }
    });
  }

  ngAfterViewInit(): void {
    this.setupDocumentClickListener();
  }

  ngOnDestroy(): void {
    sessionStorage.removeItem('revisionListSearch');
    this.stopMusic();
    this.documentClickListener?.();
  }

  /**
   * 第幾頁
   * @param item number
   */
  getRevisionListPageFromPaginator(nowPage: number) {
    this.revisionListNowPage = nowPage;
    this.getRevisionList();
    window.scrollTo({
      top: 0,
      behavior: 'smooth',
    });
  }
  /**
   * 每頁資料數量
   * @param item number
   */
  getRevisionListPageSizeFromPaginator(pageSize: number) {
    this.revisionListPageSize = pageSize;
    this.revisionListNowPage = 1;
    this.getRevisionList();
    window.scrollTo({
      top: 0,
      behavior: 'smooth',
    });
  }
  homeRevisionList: revisionListItem[] = [];
  getRevisionList() {
    let req: getRevisionListReq = {
      page: this.revisionListNowPage,
      pageSize: this.revisionListPageSize,
      type: 0,
    };
    this.revisionListService.getRevisionList(req).subscribe({
      next: (resp: getRevisionListResp) => {
        this.homeRevisionList = resp.data.items;
        this.revisionListTotalCount = resp.data.itemTotalCount;
      },
      error: () => { },
    });
  }

  linkTo(dictionaryId: string, dictionary: string) {
    this.router.navigate([], {
      queryParams: {
        tab: 2,
        dictionaryId: dictionaryId,
        dictionary: dictionary,
      },
      queryParamsHandling: 'merge', // 保留其他 query 參數（如果需要）
    });

    // this.keyword = '';
    // this.showKeyword = dictionary;
    // this.selectedLanguageList = [];
    // this.selectedDialectList = [];
    // this.languageList = [];
    // this.dialectList = [];
    // this.dictionaryId = dictionaryId;
    // this.nowPage = 1;
    // this.getDialectAndLanguageList();
    // this.searchAccurate(this.selectedLanguageList, this.selectedDialectList);
  }

  // 監聽文檔點擊事件
  private documentClickListener: () => void = () => { };

  private setupDocumentClickListener() {
    setTimeout(() => {
      this.documentClickListener = this.renderer.listen(
        'document',
        'click',
        (event: MouseEvent) => {
          // 這裡需要先檢查 searchAll.nativeElement 是否存在
          if (
            this.searchAll?.nativeElement &&
            !this.searchAll.nativeElement.contains(event.target)
          ) {
            this.autoInputList = [];
          }
        }
      );
    });
  }

  // 當搜尋框獲得焦點時重新設置監聽器
  onSearchFocus() {
    this.setupDocumentClickListener();
  }

  onKeywordChange(value: string): void {
    this.keywordChanged.next(value);
  }

  callAutoInputAPI(value: string): void {
    this.shareService.autoSearch(value, null).subscribe({
      next: (resp: autoSearchResp) => {
        this.autoInputList = resp.data.candidateWords;
      },
      error: () => { },
    });
  }

  selectValue(value: string) {
    this.keyword = value;
    this.autoInputList = [];
  }

  getDialectAndLanguageList() {
    if (this.languageList.length < 1 || this.dialectList.length < 1) {
      this.languageService.getDialectAndLanguageList().subscribe({
        next: (resp: getDialectAndLanguageListResp) => {
          if (resp.status === apiStatus.SUCCESS) {
            this.languageList = resp.data.tribes.map((item) => {
              this.dialectList.push(
                ...item.dialectList.map(
                  (dialect: {
                    id: string;
                    name: string;
                    active: boolean;
                    tribeId: string;
                  }) => ({
                    ...dialect,
                    disabled: true,
                  })
                )
              );
              return { ...item, active: false };
            });
            //TODO族語
            this.revisionListSearch?.selectedLanguageList.map((item) => {
              this.languageList.map((languageListItem) => {
                if (languageListItem.id === item) {
                  languageListItem.active = true;
                }
              });
              this.dialectList.map((dialectListItem) => {
                if (dialectListItem.tribeId === item) {
                  dialectListItem.disabled = false;
                }
              });
            });
            //TODO方言
            this.revisionListSearch?.selectedDialectList.map((item) => {
              this.dialectList.map((dialectListItem) => {
                if (dialectListItem.id === item) {
                  dialectListItem.active = true;
                }
              });
            });
          }
        },
        error: () => { },
      });
    }
  }

  languageListChange(event: Event, categoryID: string) {
    const isChecked = (event.target as HTMLInputElement).checked;
    this.languageList.forEach((language) => {
      if (language.id === categoryID) {
        language.active = isChecked;
      }
    });
    this.dialectList.forEach((dialect) => {
      if (dialect.tribeId === categoryID) {
        dialect.active = true;
        dialect.disabled = !isChecked;
        if (dialect.disabled) {
          dialect.active = false;
        }
      }
    });
    this.checkSelectList();
  }

  dialectListChange(event: Event, categoryID: string, tribeId: string) {
    const isChecked = (event.target as HTMLInputElement).checked;
    this.dialectList.map((item) => {
      if (item.id === categoryID) {
        item.active = isChecked;
      }
    });
    const isUsable = this.dialectList.some(
      (item) => item.tribeId === tribeId && item.active === true
    );
    if (!isUsable) {
      const foundItem = this.languageList.find((item) => item.id === tribeId);
      if (foundItem) {
        foundItem.active = false;
      }
      this.dialectList.forEach((item) => {
        if (item.tribeId === tribeId) {
          item.disabled = true;
        }
      });
    }
    this.checkSelectList();
  }
  checkSelectList() {
    this.selectedDialectList = this.dialectList
      .filter((item) => item.active)
      .map((item) => item.id);
    this.selectedLanguageList = this.languageList
      .filter((item) => item.active)
      .map((item) => item.id);
  }

  selectTab(event: MatTabChangeEvent) {
    this.nowPage = 1;
    this.searhModel = event.tab.textLabel as SearhModel;
    let title: SearhModel = event.tab.textLabel as SearhModel;
    if (title === SearhModel.Accurate) {
      this.searchAccurate(this.selectedLanguageList, this.selectedDialectList);
    } else {
      this.searchFuzzy(this.selectedLanguageList, this.selectedDialectList);
    }
  }

  search() {
    this.router.navigate([], {
      queryParams: { tab: 2 },
    });
    this.dictionaryId = null;
    if (this.tabGroup) {
      this.tabGroup.selectedIndex = 0; // 切換到第一個 Tab
    }
    this.nowPage = 1;
    this.dictionaryList = [];
    this.showKeyword = this.keyword;
    this.searchAccurate(this.selectedLanguageList, this.selectedDialectList);
  }

  searchAccurate(
    selectedLanguageList: string[],
    selectedDialectList: string[]
  ) {
    this.isDialect = false;
    this.isLanguard = false;
    let req: searchRevisionListReq = {
      type: this.dictionaryId ? 0 : 1,
      search: {
        page: this.nowPage,
        pageSize: this.pageSize,
        keyword: this.keyword,
        dictionaryId: this.dictionaryId ? this.dictionaryId : null,
        advanceSearch: {
          tribeId: selectedLanguageList,
          dialectId: selectedDialectList,
        },
      },
    };

    this.revisionListService
      .searchRevisionList(req, SearhModel.Accurate)
      .subscribe({
        next: (resp: searchRevisionListResp) => {
          if (resp.status === apiStatus.SUCCESS) {
            this.dictionaryList = resp.data.wordItems;
            this.totalCount = resp.data.itemTotalCount;
            this.totalPage = resp.data.pageTotalCount;
            this.onPanelOpened(this.dictionaryList[0]);
            const keyword = this.keyword;
            const revisionListSearch = {
              keyword,
              selectedDialectList,
              selectedLanguageList,
            };
            sessionStorage.setItem(
              'revisionListSearch',
              JSON.stringify(revisionListSearch)
            );
          } else {
            this.confirmService.showError(resp.message, '錯誤');
          }
        },
        error: (err: HttpErrorResponse) => {
          this.confirmService.showError(err.error.error.details, '錯誤');
        },
      });
  }

  searchFuzzy(selectedLanguageList: string[], selectedDialectList: string[]) {
    this.isDialect = false;
    this.isLanguard = false;
    let req: searchRevisionListReq = {
      type: this.dictionaryId ? 0 : 1,
      search: {
        dictionaryId: this.dictionaryId ? this.dictionaryId : null,
        page: this.nowPage,
        pageSize: this.pageSize,
        keyword: this.keyword,
        advanceSearch: {
          tribeId: selectedLanguageList,
          dialectId: selectedDialectList,
        },
      },
    };
    this.revisionListService
      .searchRevisionList(req, SearhModel.Fuzzy)
      .subscribe({
        next: (resp: searchRevisionListResp) => {
          if (resp.status === apiStatus.SUCCESS) {
            this.dictionaryList = resp.data.wordItems;
            this.totalCount = resp.data.itemTotalCount;
            this.totalPage = resp.data.pageTotalCount;
            this.onPanelOpened(this.dictionaryList[0]);
            const keyword = this.keyword;
            const revisionListSearch = {
              keyword,
              selectedDialectList,
              selectedLanguageList,
            };
            sessionStorage.setItem(
              'revisionListSearch',
              JSON.stringify(revisionListSearch)
            );
          } else {
            this.confirmService.showError(resp.message, '錯誤');
          }
        },
        error: (err: HttpErrorResponse) => {
          this.confirmService.showError(err.error.error.details, '錯誤');
        },
      });
  }

  sanitizeExplanation(rawHtml: string): SafeHtml {
    return this.sanitizer.bypassSecurityTrustHtml(rawHtml);
  }

  isNameMatched(a: string, b: string): boolean {
    return a?.toLowerCase() === b?.toLowerCase();
  }

  keyboardUp(value: string, inputElement: HTMLInputElement) {
    const currentKeyword = this.keyword || '';
    const start = inputElement.selectionStart ?? currentKeyword.length;
    const end = inputElement.selectionEnd ?? currentKeyword.length;
    const newKeyword =
      currentKeyword.slice(0, start) + value + currentKeyword.slice(end);
    this.keyword = newKeyword;
    const cursorPos = start + value.length;
    setTimeout(() => {
      inputElement.focus();
      inputElement.setSelectionRange(cursorPos, cursorPos);
    });
  }

  clear() {
    this.keyword = '';
    this.showKeyword = '';
  }

  onPanelOpened(item: dictionaryItem) {
    if (!item) {
      return;
    }
    item.isOpenPanel = true;
    item.loading = true;
    this.languageService.searchDictionaryDetail(item.id).subscribe({
      next: (resp: searchDictionaryDetailResp) => {
        if (resp.status === apiStatus.SUCCESS) {
          item.loading = false;
          item.isImage = resp.data.isImage;
          item.isDerivativeRoot = resp.data.isDerivativeRoot;
          item.wordItem = resp.data.word;
        } else {
          this.confirmService.showError(resp.message, '錯誤');
        }
      },
      error: () => { },
    });
  }

  onRevisionPanelOpened(item: dictionaryItem) {
    this.revisionListService.getRevision(item.id, 1).subscribe({
      next: (resp: getRevisionResp) => {
        item.revisionList = resp.data.items;
      },
      error: () => { },
    });
  }

  onWritingSystemPanelOpened(item: dictionaryItem) {
    this.revisionListService.getRevision(item.id, 2).subscribe({
      next: (resp: getRevisionResp) => {
        item.writingSystemList = resp.data.items;
      },
      error: () => { },
    });
  }

  onPanelClose(item: dictionaryItem) {
    item.isOpenPanel = false;
  }

  clickAnaphoraSentence(id: string | null) {
    if (id === null) {
      return;
    }
    this,
      this.matDialog.open(AnaphoraSentenceDialogComponent, {
        disableClose: true,
        autoFocus: false,
        width: '60%',
        data: {
          id: id,
        },
      });
  }

  /**
   * 第幾頁
   * @param item number
   */
  getPageFromPaginator(nowPage: number) {
    this.nowPage = nowPage;
    this.activePage = nowPage;
    if (this.searhModel === SearhModel.Accurate) {
      this.searchAccurate(this.selectedLanguageList, this.selectedDialectList);
    } else {
      this.searchFuzzy(this.selectedLanguageList, this.selectedDialectList);
    }
    window.scrollTo({
      top: 0,
      behavior: 'smooth',
    });
  }
  /**
   * 每頁資料數量
   * @param item number
   */
  getPageSizeFromPaginator(pageSize: number) {
    this.pageSize = pageSize;
    this.nowPage = 1;
    if (this.searhModel === SearhModel.Accurate) {
      this.searchAccurate(this.selectedLanguageList, this.selectedDialectList);
    } else {
      this.searchFuzzy(this.selectedLanguageList, this.selectedDialectList);
    }
    window.scrollTo({
      top: 0,
      behavior: 'smooth',
    });
  }

  share(type: ShareType, id: string) {
    const shareUrl = `${environment.sitePath}/sharePage?id=${id}`;
    switch (type) {
      case this.shareType.FB:
        FB.ui(
          {
            method: 'share',
            href: shareUrl,
          },
          (response: any) => {
            if (response && !response.error_message) {
              console.log('分享成功');
            } else {
              console.error('分享失敗或取消', response);
            }
          }
        );
        break;
      case this.shareType.LINE:
        window.open(
          `https://social-plugins.line.me/lineit/share?url=${environment.sitePath}/sharePage?id=${id}`,
          '_blank',
          'noopener,noreferrer'
        );
        break;
    }
  }

  getRootStructure(dictionaryId: string) {
    this.matDialog.open(RootStructureDialogComponent, {
      disableClose: true,
      autoFocus: false,
      width: '60%',
      data: {
        dictionaryId: dictionaryId,
      },
    });
  }

  question(item: dictionaryItem) {
    window.open(
      `${environment.sitePath}/singleQuestion?id=${item.id}&tribeId=${item.tribeId}`,
      '_blank'
    );
  }

  play(item: audioItem) {
    if (this.currentMediaElement) {
      this.currentMediaElement.pause();
      this.currentMediaElement.remove();
    }
    this.fileService.getAudioFile(item.fileId).subscribe({
      next: (resp: string) => {
        let mediaElement: HTMLAudioElement = document.createElement('audio');
        mediaElement.style.display = 'none'; // 這行讓音頻播放器隱藏
        mediaElement.setAttribute('src', resp); // 設置音頻源
        mediaElement.setAttribute('controls', 'true'); // 加入控制條
        document.body.appendChild(mediaElement);
        mediaElement.play();
        mediaElement.addEventListener('ended', () => {
          this.currentMediaElement = null; // 重置當前音樂播放器
        });

        this.currentMediaElement = mediaElement; // 儲存當前的音樂播放器
      },
      error: () => { },
    });
  }

  stopMusic() {
    if (this.currentMediaElement) {
      this.currentMediaElement.pause();
      this.currentMediaElement.remove();
      this.currentMediaElement = null;
    }
  }

  back(event: Event) {
    event.preventDefault();
    history.back();
    this.keyword = '';
  }

  onCopy(event: ClipboardEvent) {
    event.preventDefault(); // 阻止預設複製行為
    const selection = window.getSelection();
    if (selection) {
      let copiedText = selection.toString()
        .replace(/\r?\n/g, ' ')  // 移除換行
        .replace(/\s+/g, ' ');   // 將多個空格替換為單一空格
      event.clipboardData?.setData('text/plain', copiedText);
    }
  }

  openImage(item: dictionaryItem) {
    item.wordItem?.explanationItems.map((item) => {
      return (item.isImage = !item.isImage);
    });
  }
}
