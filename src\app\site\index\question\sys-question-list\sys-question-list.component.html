<main class="master-pages-container-layout">
    <div class="master-pages-container-cont">
        <div class="cont pages-cont-layout">
            <div class="pages-cont-list-layout">

                <!--路徑列-->
                <div class="breadcrumb-layout">
                    <div>
                        <nav class="breadcrumb-cont">
                            <ol class="breadcrumb">
                                <li class="breadcrumb-item">
                                    <a href="" (click)="$event.defaultPrevented" [routerLink]="'/home'">
                                        <span>首頁</span>
                                        <span class="material-symbols-outlined">keyboard_arrow_right</span>
                                    </a>
                                </li>
                                <li class="breadcrumb-item active">系統回饋</li>
                            </ol>
                        </nav>
                    </div>
                    <div>
                        <nav class="breadcrumb-cont">
                            <ol class="breadcrumb">
                                <li class="breadcrumb-item">
                                    <a hre="" (click)="back($event)">
                                        <span>&lt;&lt;回上一頁</span>
                                    </a>
                                </li>
                            </ol>
                        </nav>
                    </div>
                </div>
                <div style="width: 100%;">
                    <!--內容區-->
                    <div class="search-group">
                        <div class="col-3 col-12 gutter-16px">
                            <div class="mb-12px w-full relative select-style">
                                <select class="select w-full" title="處理狀態" [(ngModel)]="status">
                                    <option [ngValue]="null" disabled selected>請選擇處理狀態</option>
                                    <option [ngValue]="0">審查中</option>
                                    <option [ngValue]="1">已處理</option>
                                </select>
                                <div class="block-select-bg absolute">
                                    <div class="row items-stretch">
                                        <div class="col">
                                            <div class="block-select-bg-rect w-full radius-card"></div>
                                        </div>
                                        <div class="col-auto shrink-0">
                                            <div class="button-dot">
                                                <span class="material-symbols-outlined">keyboard_arrow_down</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        &nbsp;&nbsp;
                        <button class="btn-list btn-primary-solid btn-search" (click)="search()">搜尋</button>
                        <button class="btn-list btn-primary-solid btn-search" style="margin-left: auto;"
                            [routerLink]="'/question/addSysQuestion'">新增</button>
                    </div>
                    <div class="table-box">
                        <table class="table-list-layout  table-list-style rwd-table01">
                            <tbody>
                                <tr class="th-no">
                                    @for (item of list; track $index) {
                                    <th [width]="item.width">
                                        {{item.title}}
                                    </th>
                                    }
                                </tr>
                                @if(questionList.length>0){
                                @for (item of questionList; let index= $index;track index) {
                                <tr>
                                    <td class="text-c">
                                        <span class="rwd-th">項次</span>
                                        {{index+1+(nowPage>1?(nowPage-1)*pageSize:0)}}
                                    </td>
                                    <td class="text-l">
                                        <span class="rwd-th">回饋建議</span>
                                        {{item.content}}
                                    </td>
                                    <td class="text-l">
                                        <span class="rwd-th">處理內容</span>
                                        {{item.reply?item.reply:'-'}}
                                    </td>
                                    <td class="text-c">
                                        <span class="rwd-th">處理狀態</span>
                                        {{item.status}}
                                    </td>
                                    <td class="text-c">
                                        <span class="rwd-th">回饋者</span>
                                        {{item.creator}}
                                    </td>
                                    <td class="text-c">
                                        <span class="rwd-th">回饋日期</span>
                                        @if(!item.creationTime){
                                        -
                                        }
                                        @else{
                                        {{item.creationTime|date:"YYYY.MM.dd"}}
                                        }
                                    </td>
                                    <td class="text-c">
                                        <span class="rwd-th">處理日期</span>
                                        @if(!item.replyCreationTime){
                                        -
                                        }
                                        @else{
                                        {{item.replyCreationTime|date:"YYYY.MM.dd"}}
                                        }
                                    </td>

                                </tr>
                                }
                                }@else{
                                <tr>
                                    <td colspan="7" style="text-align: center;">沒有找到符合條件的資料</td>
                                </tr>
                                }
                            </tbody>
                        </table>
                    </div>

                    @if(questionList.length>0){
                    <div class="table-btn-group">
                        <app-paginator [pageSize]="pageSize" [nowPage]="nowPage" [totalRecords]="totalCount"
                            [pageShowCount]="pageShowCount"
                            currentPageReportTemplate="第 {first} 到 {last} 筆，共 {totalRecords} 筆"
                            (clickPageEvent)="getPageFromPaginator($event)"
                            (pageSizeChangeEvent)="getPageSizeFromPaginator($event)"></app-paginator>
                    </div>
                    }
                </div>
            </div>
        </div>
    </div>
</main>