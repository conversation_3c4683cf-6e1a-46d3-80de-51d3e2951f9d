import { Component } from '@angular/core';
import { GetEthnicityService } from '../../../service/utils/get-ethnicity.service';
import { LanguageService } from '../../../service/curl/language.service';
import { ConfirmService } from '../../../service/utils/confirm.service';
import {
  annotationsItem,
  getWordCommentResp,
} from '../../../interface/language.interface';
import { apiStatus } from '../../../enum/apiStatus.enum';
import { SpinnerService } from '../../../service/utils/spinner.service';
import { UtilsService } from '../../../service/utils/utils.service';
import { RouterLink } from '@angular/router';
import { FormsModule } from '@angular/forms';

@Component({
    selector: 'app-word-comment',
    templateUrl: './word-comment.component.html',
    styleUrl: './word-comment.component.scss',
    imports: [RouterLink, FormsModule],
})
export class WordCommentComponent {
  ethnicityId: string | null = null;
  ethnicity: string | null = null;
  language: string = '';
  result: annotationsItem[] = [];
  constructor(
    private getEthnicityService: GetEthnicityService,
    private languageService: LanguageService,
    private confirmService: ConfirmService,
    private spinnerService: SpinnerService,
    private utils: UtilsService
  ) {
    this.ethnicityId = this.getEthnicityService.GetEthnicityId();
    this.ethnicity = this.getEthnicityService.GetEthnicityName();
    this.utils.setTitle(`${this.ethnicity}-逐詞註解`);
  }

  getComment() {
    if (!this.language) {
      return;
    }
    this.spinnerService.show();
    this.languageService
      .getWordComment(this.ethnicityId as string, this.language)
      .subscribe({
        next: (resp: getWordCommentResp) => {
          this.spinnerService.hide();
          if (resp.status === apiStatus.SUCCESS) {
            this.result = resp.data.annotations;
          } else {
            this.confirmService.showError(resp.message, '錯誤');
          }
        },
        error: () => {
          this.spinnerService.hide();
        },
      });
  }

  getFormattedResult(): string {
    return this.result
      .map(
        (item) => `${item.name}` + '[' + item.explanations + ']' // 每個解釋換行
      )
      .join('\n'); // 不同單字之間換行
  }

  clear() {
    this.language = '';
    this.result = [];
  }

  back(event: Event) {
    event.preventDefault();
    history.back;
  }
}
