import { Component, Inject, Sanitizer } from '@angular/core';
import { NewWordService } from '../../service/curl/new-word.service';
import { MAT_DIALOG_DATA, MatDialogRef, MatDialogTitle, MatDialogContent, MatDialogActions, MatDialogClose } from '@angular/material/dialog';
import {
  getNewWordDetailResp,
  newWordDetailItem,
} from '../../interface/newWord.interface';
import { MatIcon } from '@angular/material/icon';
import { CdkScrollable } from '@angular/cdk/scrolling';
import { MatProgressSpinner } from '@angular/material/progress-spinner';
import { DomSanitizer, SafeHtml } from '@angular/platform-browser';

@Component({
  selector: 'app-new-word-dialog',
  templateUrl: './new-word-dialog.component.html',
  styleUrl: './new-word-dialog.component.scss',
  imports: [
    MatDialogTitle,
    MatIcon,
    CdkScrollable,
    MatDialogContent,
    MatProgressSpinner,
    MatD<PERSON>ogA<PERSON>,
    MatDialogClose,
  ],
})
export class NewWordDialogComponent {
  isLoading: boolean = false;
  newWordDetailItem?: newWordDetailItem;

  constructor(
    @Inject(MAT_DIALOG_DATA)
    public data: {
      id: string;
      name: string;
    },
    private newWordService: NewWordService,
    private dialogRef: MatDialogRef<NewWordDialogComponent>,
    private sanitizer: DomSanitizer
  ) { }

  ngOnInit(): void {
    this.getNewWordDetail();
  }

  getNewWordDetail() {
    this.isLoading = true;
    this.newWordService.getNewWordDetail(this.data.id).subscribe({
      next: (resp: getNewWordDetailResp) => {
        this.isLoading = false;
        this.newWordDetailItem = resp.data;
      },
      error: (err) => {
        this.isLoading = false;
        console.error('取得單字詳細資料失敗：', err);
        // 可以顯示錯誤提示給使用者，例如：
      },
    });
  }

  close() {
    this.dialogRef.close();
  }

  getFormattedSentence(sentence: string): SafeHtml {
    const targetText = this.data.name;
    const regex = new RegExp(targetText, 'gi'); // 加上 'i' 為不分大小寫，'g' 為全域取代

    const formattedText = sentence.replace(regex, (match) =>
      `<span class="highlighted">${match}</span>`
    );
    return formattedText

  }
}
