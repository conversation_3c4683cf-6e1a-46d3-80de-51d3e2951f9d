//列表表格
.table-list-layout {
	border-collapse: collapse;
	margin: 10px 0;
	max-width: 100%;
	width: 100%;
	// display: block;
	overflow-x: auto;
	// white-space: nowrap;
	> thead > tr > th,
	tbody > tr > th,
	tfoot > tr > th,
	thead > tr > td,
	tbody > tr > td,
	tfoot > tr > td {
		margin: 0;
		border-bottom-width: 1px;
		border-bottom-style: solid;
		padding: 10px 8px;
		vertical-align: center;
		font-size: 1.125em;
		line-height: 1.6;
		// width: 100%;
	}
	> tbody > tr > th {
		vertical-align: middle;
	}
	> thead > tr > th {
		border-bottom-width: 0px;
		vertical-align: bottom;
	}
	> caption + thead > tr:first-child > th,
	colgroup + thead > tr:first-child > th,
	thead:first-child > tr:first-child > th,
	caption + thead > tr:first-child > td,
	colgroup + thead > tr:first-child > td,
	thead:first-child > tr:first-child > td {
		border-top-width: 0px;
		border-top-style: solid;
	}
	tbody + tbody {
		border-top-width: 0px;
		border-top-style: solid;
	}
}

//排序
.table-sort-list {
	position: relative;
	display: inline-flex;
	flex-direction: column;
	justify-content: center;
	align-items: center;
	cursor: pointer;
	top: 0.1875em;
	img {
		width: 0.5em;
		height: 1.1875em;
	}
}

//攔位表格
.table-block-style {
	th {
		text-align: right;
	}
}

//RWD表格
.rwd-th,
.rwd-th02 {
	display: none;
}
.table-th-middle {
	vertical-align: middle !important;
}
@media (max-width: 640px) {
	.rwd-table01 {
		// display: contents;
		width: 100%;
		tr,
		th,
		td {
			display: block;
			width: auto !important;
			text-align: center;
			&.th_no {
				display: none;
			}
		}
		tr {
			margin: 0 0 5px;
			border-width: 1px;
			border-style: solid;
			tr {
				margin: 0;
				border: 0;
			}
		}
		td {
			border: 0px !important;
		}
	}

	.rwd-table02 {
		width: 100%;
		tr,
		th,
		td {
			display: block;
			width: auto;
			text-align: center;
		}
		tr {
			margin: 0 0 5px;
			padding: 10px 10px 0;
			border-width: 1px;
			border-style: solid;
			tr {
				margin: 0;
				border: 0;
			}
		}
		td {
			border: 0px !important;
		}
	}

	.rwd-th {
		margin: 0 0 10px;
		padding: 10px 8px;
		display: block;
		width: calc(100% - 16px) !important;
		text-align: center !important;
		line-height: 1.6;
		font-weight: bold;
	}

	.rwd-th02 {
		margin: 0 0 10px;
		padding: 5px 0;
		display: block;
		width: 100% !important;
		text-align: center;
		line-height: 1.6;
	}

	.th-no {
		display: none !important;
	}

	.table-box {
		width: 90%;
		margin: 0 10px;
		padding: 10px;
	}
}
