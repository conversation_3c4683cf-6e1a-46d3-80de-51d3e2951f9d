import { Component, Inject } from '@angular/core';
import { MAT_DIALOG_DATA, MatDialogRef, MatDialogTitle, MatDialogContent, MatDialogClose } from '@angular/material/dialog';
import { FormBuilder, FormGroup, Validators, FormsModule, ReactiveFormsModule } from '@angular/forms';
import { HttpResponse } from '@angular/common/http';
import { downloadImageReq } from '../../../interface/download.interface';
import { DownloadService } from '../../../service/curl/download.service';
import { SpinnerService } from '../../../service/utils/spinner.service';
import { MatIcon } from '@angular/material/icon';
import { CdkScrollable } from '@angular/cdk/scrolling';
import { MatProgressSpinner } from '@angular/material/progress-spinner';

@Component({
    selector: 'app-image-download-dialog',
    templateUrl: './image-download-dialog.component.html',
    styleUrl: './image-download-dialog.component.scss',
    imports: [
        MatDialogTitle,
        MatIcon,
        CdkScrollable,
        MatDialogContent,
        MatProgressSpinner,
        FormsModule,
        ReactiveFormsModule,
        MatDialogClose,
    ],
})
export class ImageDownloadDialogComponent {
  isLoading: boolean = false;
  form: FormGroup;
  constructor(
    @Inject(MAT_DIALOG_DATA)
    public data: {
      req: downloadImageReq;
    },
    private dialogRef: MatDialogRef<ImageDownloadDialogComponent>,
    private downloadService: DownloadService,
    private spinnerService: SpinnerService,
    private formBuilder: FormBuilder
  ) {
    this.form = this.formBuilder.group({
      fileType: ['1', Validators.required],
    });
  }

  send() {
    let req: downloadImageReq = {
      dictionaryIds: this.data.req.dictionaryIds,
      tribeId: this.data.req.tribeId,
      imageClassId: this.data.req.imageClassId,
      fileType: this.form.value.fileType,
      isAll: this.data.req.isAll,
      deleteDictionaryIds: this.data.req.deleteDictionaryIds,
    };
    this.spinnerService.show();
    this.downloadService.downloadImage(req).subscribe({
      next: (resp: HttpResponse<Blob>) => {
        this.spinnerService.hide();
        const contentDisposition = resp.headers.get('content-disposition');
        const filename =
          this.downloadService.getFilenameFromContentDisposition(
            contentDisposition
          );
        const link = document.createElement('a');
        const url = window.URL.createObjectURL(resp.body as Blob);
        link.href = url;
        link.download = filename || 'downloaded_file.pdf';
        link.click();
        window.URL.revokeObjectURL(url);
        this.dialogRef.close();
      },
      error: () => {
        this.spinnerService.hide();
      },
    });
  }

  close() {
    this.dialogRef.close();
  }
}
