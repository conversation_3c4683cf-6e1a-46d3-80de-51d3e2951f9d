import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { OAuthService } from 'angular-oauth2-oidc';

@Injectable({
  providedIn: 'root',
})
export class LineAuthService {
  // clientId: number = 1657509996;
  clientId: string = '1657509996';
  clientSecret: string = 'fc1c2dc570e31b9ae7222eb34e6bb420';
  constructor(
    private oauthService: OAuthService,
    private httpClient: HttpClient
  ) {}
  login(redirectUri: string) {
    // 生成隨機的 state
    const state = this.generateState();
    // 保存 state（可以儲存在 sessionStorage 或 sessionStorage）
    sessionStorage.setItem('authState', state);
    const loginUrl = `https://access.line.me/oauth2/v2.1/authorize?response_type=code&client_id=${this.clientId}&redirect_uri=${redirectUri}&scope=openid profile&state=${state}`;
    // 重定向到 Line 登入頁面
    window.location.href = loginUrl;
  }

  logout() {
    this.oauthService.logOut();
  }

  // 隨機生成 state
  private generateState(): string {
    const array = new Uint32Array(1);
    window.crypto.getRandomValues(array);
    return array[0].toString(36); // 轉換為 base36 字串
  }

  getAccessToken(code: string, redirectUri: string) {
    const tokenUrl = 'https://api.line.me/oauth2/v2.1/token';
    const body = new URLSearchParams();
    body.set('grant_type', 'authorization_code');
    body.set('code', code);
    body.set('redirect_uri', redirectUri);
    body.set('client_id', this.clientId);
    body.set('client_secret', this.clientSecret); // 請從 LINE Developers 控制台取得
    const headers = new HttpHeaders().set(
      'Content-Type',
      'application/x-www-form-urlencoded'
    );

    return this.httpClient.post(tokenUrl, body.toString(), { headers });
  }

  // 獲取 LINE 用戶資料
  getUserInfo(accessToken: string) {
    const url = 'https://api.line.me/v2/profile';
    const headers = new HttpHeaders().set(
      'Authorization',
      `Bearer ${accessToken}`
    );
    return this.httpClient.get(url, { headers });
  }
}
