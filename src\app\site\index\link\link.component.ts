import { Component } from '@angular/core';
import { Router, RouterLink } from '@angular/router';
import { LinkService } from '../../../service/curl/link.service';
import {
  getLinkListReq,
  getLinkListResp,
  linkItem,
} from '../../../interface/link.interface';
import { DomSanitizer } from '@angular/platform-browser';
import { UtilsService } from '../../../service/utils/utils.service';
import { FormsModule } from '@angular/forms';
import { PaginatorComponent } from '../../../utils/paginator/paginator.component';

@Component({
    selector: 'app-link',
    templateUrl: './link.component.html',
    styleUrl: './link.component.scss',
    imports: [
        RouterLink,
        FormsModule,
        PaginatorComponent,
    ],
})
export class LinkComponent {
  keyword: string = '';
  linkList: linkItem[] = [];

  pageSize: number = 10; //一頁幾筆資料
  nowPage: number = 1;
  totalCount: number = 0; //總筆數
  pageShowCount: number = 5; //分頁器秀幾個

  list: {
    column: string;
    title: string;
    width: string;
    sort: boolean;
  }[] = [
    {
      column: 'index',
      title: '項次',
      width: '',
      sort: false,
    },
    {
      column: 'websiteName',
      title: '網站名稱',
      width: '',
      sort: false,
    },
    {
      column: 'websiteLink',
      title: '網址',
      width: '',
      sort: false,
    },
  ];

  constructor(
    private linkService: LinkService,
    private sanitizer: DomSanitizer,
    private utils:UtilsService
  ) {}

  ngOnInit(): void {
    this.utils.setTitle('相關連結')
    this.getLinkList();
  }
  search() {
    this.nowPage = 1;
    this.getLinkList();
  }

  getLinkList() {
    let req: getLinkListReq = {
      keyword: this.keyword,
      page: this.nowPage,
      pageSize: this.pageSize,
    };
    this.linkService.getLinkList(req).subscribe({
      next: (resp: getLinkListResp) => {
        this.totalCount = resp.data.itemTotalCount;
        this.linkList = resp.data.searchData;
      },
      error: () => {},
    });
  }

  /**
   * 第幾頁
   * @param item number
   */
  getPageFromPaginator(nowPage: number) {
    this.nowPage = nowPage;
    this.getLinkList();
  }
  /**
   * 每頁資料數量
   * @param item number
   */
  getPageSizeFromPaginator(pageSize: number) {
    this.pageSize = pageSize;
    this.nowPage = 1;
    this.getLinkList();
  }

  back(event: Event) {
    event.preventDefault();
    history.back();
  }

  safeUrl(url: string) {
    return this.sanitizer.bypassSecurityTrustUrl(url);
  }
}
