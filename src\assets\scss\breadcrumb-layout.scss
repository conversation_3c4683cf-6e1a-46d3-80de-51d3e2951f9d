// Scss Document
.breadcrumb-layout {
	// padding: 0 24px;
	.breadcrumb-cont {
		padding: 0 10px;
		display: flex;
		.breadcrumb {
			display: flex;
			list-style: none;
			padding: 0px;
			.breadcrumb-item {
				display: flex;
				flex-direction: row;
				align-items: center;
				justify-content: flex-start;
				a {
					display: flex;
					flex-direction: row;
					align-items: center;
					justify-content: flex-start;
					text-decoration: none;
					span {
						display: inline-flex;
					}
				}
				&.active {
					color: #333333;
					font-weight: bold;
				}
				&:before {
					font-weight: normal;
				}
			}
		}
	}
}
