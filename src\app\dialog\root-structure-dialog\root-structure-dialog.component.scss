.mat-mdc-dialog-title {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    margin: 0;
}

label {
    font-size: 1.5em;
    line-height: 2;
}
span {
    font-size: 1.3em;
    line-height: 2;
}

.btn-cancel-color {
    border-width: 2px;
    border-style: solid;
}

.text-center {
    text-align: center;
}

button {
    outline: none;
}

.title {
    font-size: 2em;
    font-weight: bold;
}

.group-msg {
    display: flex;
    flex-direction: column;
    text-align: left;
}

.spinner-wrapper-index {
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 998;
    app-spinner {
        width: 6rem;
        height: 6rem;
    }
}

.font-deep {
    border-bottom: solid 1px gray;
}

.bottom-border {
    cursor: pointer;
    border-bottom: 3px solid; /* 底線顏色 & 粗細 */
    padding-bottom: 2px; /* 控制底線與文字的距離 */
}
