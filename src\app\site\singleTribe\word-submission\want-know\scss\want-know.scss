.want-know-layout{
    margin: 0;
    padding:  20px 50px;
    width: 100%;
    box-sizing: border-box;
}
.want-know-cont{
    font-size: 1.13em;
    color: #000;
}
.want-know-item{
    font-size: 1.13em;
    font-weight: bold;
    display: flex;
    align-items: center;
}
.input-group{
    display: flex;
    flex-direction: column;
    margin-top: 15px;
    .input-list{
        display: flex;
    }
}

.btn-list{
	margin: 10px 10px 10px 0;
	padding: 20px 30px;
	border-radius: 5px;
	-moz-user-select: none;
	background-image: none;
	border: 1px solid transparent;
	border-radius: 5px;
	cursor: pointer;
	display: inline-block;
	font-weight: 400;
	line-height: 1.2;
	text-align: center;
	font-size: 1.0em;
	white-space: nowrap;
}
.btn-list2{
    margin: 10px 10px 10px 0;
    padding: 10px 30px;
    border-radius: 5px;
    -moz-user-select: none;
    background-image: none;
    border: 1px solid transparent;
    border-radius: 5px;
    cursor: pointer;
    display: inline-block;
    font-weight: 400;
    line-height: 1.2;
    text-align: center;
    font-size: 1em;
    white-space: nowrap;
    border-radius: 50px;
}

.btn-primary-color{
	background: #4A7F42;
	color:#fff;
    &:hover, &:focus{
		background: #255d1c;
		// opacity: 0.5;
    }
	&:active, &.active{
		background-color: #255d1c;
		border-color: #255d1c;
    }
}
.btn-green-color{
	background: #fff;
	color:#4A7F42;
    border: 1px solid #4A7F42;
    &:hover, &:focus{
		background: #4A7F42;
        color: #fff;
    }
	&:active, &.active{
		background-color: #4A7F42;
		border-color: #4A7F42;
        color: #fff;
    }
}
a{
    text-decoration: none;
}
.material-symbols-outlined {
    color: #4A7F42;
  font-variation-settings:
  'FILL' 1,
  'wght' 400,
  'GRAD' 0,
  'opsz' 24
}

//字體顏色
.font_r{
    color: #E41E3F;
}

@media (max-width: 640px) {
    .input-group{
        .input-list{
            display: flex;
            flex-wrap: wrap;
        }
    }
}
