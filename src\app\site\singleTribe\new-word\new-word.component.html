<main class="master-pages-container-layout">
    <div class="master-pages-container-cont">
        <div class="cont pages-cont-layout">
            <div class="pages-cont-list-layout">
                <!--路徑列-->
                <!--路徑列-->
                <div class="breadcrumb-layout">
                    <div>
                        <nav class="breadcrumb-cont">
                            <ol class="breadcrumb">
                                <li class="breadcrumb-item">
                                    <a href="" (click)="$event.defaultPrevented" [routerLink]="'/home'">
                                        <span>首頁</span>
                                        <span class="material-symbols-outlined">keyboard_arrow_right</span>
                                    </a>
                                </li>
                                <li class="breadcrumb-item">
                                    <a href="" (click)="$event.defaultPrevented">
                                        <span>{{ethnicity}}</span>
                                        <span class="material-symbols-outlined">keyboard_arrow_right</span>
                                    </a>
                                </li>
                                <li class="breadcrumb-item active">公告新詞</li>
                            </ol>
                        </nav>
                    </div>
                    <div>
                        <nav class="breadcrumb-cont">
                            <ol class="breadcrumb">
                                <li class="breadcrumb-item">
                                    <a href (click)="back($event)">
                                        <span>&lt;&lt;回上一頁</span>
                                    </a>
                                </li>
                            </ol>
                        </nav>
                    </div>
                </div>
                <div style="width: 100%;">
                    <form [formGroup]="form">
                        <main class="announcement-new-words-layout">
                            <div class="input-group">

                                <div class="col-lg col-12 gutter-16px">
                                    <div class="mb-12px w-full relative select-style">
                                        <select class="select w-full" title="年度" formControlName="year">
                                            <option [ngValue]="null">請選擇年度</option>
                                            @for (item of yearList; track item) {
                                            <option [value]="item">{{ item }} 年度</option>
                                            }
                                        </select>

                                        <!-- 自訂樣式外殼 -->
                                        <div class="block-select-bg absolute">
                                            <div class="row items-stretch">
                                                <div class="col">
                                                    <div class="block-select-bg-rect w-full radius-card"></div>
                                                </div>
                                                <div class="col-auto shrink-0">
                                                    <div class="button-dot">
                                                        <span
                                                            class="material-symbols-outlined">keyboard_arrow_down</span>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-lg col-12 gutter-16px">
                                    <div class="mb-12px w-full relative select-style">
                                        <select class="select w-full" title="消息分類" formControlName="mainCategoryId"
                                            (change)="selectMainCategory()">
                                            <option [ngValue]="null" disabled selected>請選擇主類別</option>
                                            <option [ngValue]="null">不拘</option>
                                            @for (item of mainCategoryList; track item) {
                                            <option [value]="item.id">{{ item.name }}</option>
                                            }
                                        </select>

                                        <!-- 自訂樣式區塊 -->
                                        <div class="block-select-bg absolute">
                                            <div class="row items-stretch">
                                                <div class="col">
                                                    <div class="block-select-bg-rect w-full radius-card"></div>
                                                </div>
                                                <div class="col-auto shrink-0">
                                                    <div class="button-dot">
                                                        <span
                                                            class="material-symbols-outlined">keyboard_arrow_down</span>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-lg col-12 gutter-16px">
                                    <div class="mb-12px w-full relative select-style">
                                        <select class="select w-full sub-select" title="次類別"
                                            formControlName="subCategoryId">
                                            <option [ngValue]="null" disabled selected>請選擇次類別</option>
                                            @if (subCategoryList.length > 1) {
                                            <option [ngValue]="null">不拘</option>
                                            }
                                            @for (item of subCategoryList; track item) {
                                            <option [value]="item.id">{{ item.name }}</option>
                                            }
                                        </select>

                                        <!-- 下拉樣式裝飾區塊 -->
                                        <div class="block-select-bg absolute">
                                            <div class="row items-stretch">
                                                <div class="col">
                                                    <div class="block-select-bg-rect w-full radius-card"></div>
                                                </div>
                                                <div class="col-auto shrink-0">
                                                    <div class="button-dot">
                                                        <span
                                                            class="material-symbols-outlined">keyboard_arrow_down</span>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-lg col-12 gutter-16px">
                                    <span class="select-item">
                                        <input type="text" title="keyword" placeholder="請輸入族語/中文" class="form-control"
                                            formControlName="keyword" style="width: 100%;" #inputKeyword>
                                    </span>
                                </div>

                                <ul class="input-list">

                                    <li class="input-list btn-box">
                                        <input class="btn-list btn-primary-color" value="虛擬鍵盤" type="button"
                                            (click)="isKeyboard=!isKeyboard">
                                        <input class="btn-list btn-primary-color" value="搜尋" type="button"
                                            (click)="search()">
                                    </li>
                                </ul>
                                @if(isKeyboard){
                                <div class="keyboard-group">
                                    <div class="keyboard-box">
                                        @for (item of keyboardList; track item) {
                                        <button class="keyboard-btn" type="button" (click)="keyUp(item,inputKeyword)">
                                            <span class="keyboard_font">{{item}}</span>
                                        </button>
                                        }
                                    </div>
                                </div>
                                }
                            </div>
                            <!--表格1-->
                            <table class="table-list-layout rwd-table03">
                                <tbody>
                                    <tr class="bg_g1">
                                        <th class="th_no table_g" style="width:8%;">項次</th>
                                        <th class="th_no table_g" style="width: 8%;">年度</th>
                                        <th class="th_no table_g" style="width: 12%;">主類別</th>
                                        <th class="th_no table_g" style="width: 12%;">次類別</th>
                                        <th class="th_no table_g">詞彙</th>
                                        <th class="th_no table_g" style="width: 15%;">中文</th>
                                        <th class="th_no table_g" style="width: 12%;">發音</th>
                                        <th class="th_no table_g" style="width: 12%;">詳細資料</th>
                                    </tr>
                                    @if(newWordList.length>0){
                                    @for (item of newWordList;let index=$index; track item ) {
                                    <tr>
                                        <td class="text_c">
                                            <span class="rwd-th">項次</span>
                                            {{index+1+(nowPage>1?(nowPage-1)*pageSize:0)}}
                                        </td>
                                        <td class="text_c">
                                            <span class="rwd-th">年度</span>
                                            {{item.year}}年
                                        </td>
                                        <td class="text_c">
                                            <span class="rwd-th">主類別</span>
                                            {{item.mainCategory}}
                                        </td>
                                        <td class="text_c">
                                            <span class="rwd-th">次類別</span>
                                            {{item.subCategory}}
                                        </td>
                                        <td class="text_l">
                                            <span class="rwd-th">詞彙</span>
                                            {{item.dictionaryName}}
                                        </td>
                                        <td class="text_c">
                                            <span class="rwd-th">中文</span>
                                            {{item.chineseExplanation}}
                                        </td>
                                        <td class="text_c">
                                            <span class="rwd-th">發音</span>
                                            @if(item.audioFileId){
                                            <mat-icon class="material-symbols-outlined font_g play"
                                                [ngClass]="{'play-disabled':!item.audioFileId}"
                                                (click)="play(item.audioFileId)">volume_up</mat-icon>
                                            }@else{
                                            無
                                            }

                                        </td>
                                        <td class="text_c">
                                            <span class="rwd-th">詳細資料</span>
                                            <button class="btn-list btn-primary-solid"
                                                (click)="openDetail($event,item.id,item.dictionaryName)">詳細資料</button>
                                        </td>
                                    </tr>
                                    }
                                    }@else{
                                    <tr>
                                        <td colspan="8" style="text-align: center;">沒有找到符合條件的資料</td>
                                    </tr>
                                    }
                                </tbody>
                            </table>
                            @if(newWordList.length>0){
                            <div class="table-btn-group">
                                <app-paginator [pageSize]="pageSize" [nowPage]="nowPage" [totalRecords]="totalCount"
                                    [pageShowCount]="pageShowCount"
                                    currentPageReportTemplate="第 {first} 到 {last} 筆，共 {totalRecords} 筆"
                                    (clickPageEvent)="getPageFromPaginator($event)"
                                    (pageSizeChangeEvent)="getPageSizeFromPaginator($event)"></app-paginator>
                            </div>
                            }
                        </main>
                    </form>
                </div>
            </div>
        </div>
    </div>
</main>