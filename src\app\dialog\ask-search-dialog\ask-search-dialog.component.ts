import { Component, Inject } from '@angular/core';
import { MAT_DIALOG_DATA, MatDialogRef, MatDialogTitle, MatDialogContent, MatDialogActions, MatDialogClose } from '@angular/material/dialog';
import { MatIcon } from '@angular/material/icon';
import { CdkScrollable } from '@angular/cdk/scrolling';

@Component({
    selector: 'app-ask-search-dialog',
    templateUrl: './ask-search-dialog.component.html',
    styleUrl: './ask-search-dialog.component.scss',
    imports: [
        MatDialogTitle,
        MatIcon,
        CdkScrollable,
        MatDialogContent,
        MatDialogActions,
        MatDialogClose,
    ],
})
export class AskSearchDialogComponent {
  constructor(
    // @Inject(MAT_DIALOG_DATA) public data: any,
    private dialogRef: MatDialogRef<AskSearchDialogComponent>
  ) {}

  close() {
    this.dialogRef.close();
  }
}
