import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import {
  getIntroductionListResp,
  getIntroductionResp,
} from '../../interface/introduction.interface';

@Injectable({
  providedIn: 'root',
})
export class IntroductionService {
  constructor(private httpClient: HttpClient) {}

  getIntroductionList(tribeId: string): Observable<getIntroductionListResp> {
    return this.httpClient.post<getIntroductionListResp>(
      'api/app/dictionary/get-intro',
      {
        tribeId: tribeId,
      }
    );
  }
  
  getIntroduction(introId: string): Observable<getIntroductionResp> {
    return this.httpClient.post<getIntroductionResp>(
      'api/app/dictionary/get-intro-detail',
      {
        introId: introId,
      }
    );
  }
}
