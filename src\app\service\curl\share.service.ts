import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import {
  autoSearchResp,
  getAboutListResp,
  getAboutResp,
  getBrowseVisitorsResp,
  getTodaySentenceResp,
  getTribeDataResp,
} from '../../interface/share.interface';

@Injectable({
  providedIn: 'root',
})
export class ShareService {
  constructor(private httpClient: HttpClient) {}

  getBrowseVisitors(): Observable<getBrowseVisitorsResp> {
    return this.httpClient.get<getBrowseVisitorsResp>(
      'api/app/number-of-browse/number-of-browse'
    );
  }

  /**
   *取得每日一句
   * @returns
   */
  getTodaySentence(): Observable<getTodaySentenceResp> {
    return this.httpClient.get<getTodaySentenceResp>(
      'api/app/sentence-per-day/sentence'
    );
  }

  /**
   * 取得網站資訊列表
   * @returns
   */
  getAboutList(): Observable<getAboutListResp> {
    return this.httpClient.get<getAboutListResp>('api/app/about/about-list');
  }

  /**
   * 取得網站資訊
   * @param aboutId
   * @returns
   */
  getAbout(aboutId: string) {
    return this.httpClient.get<getAboutResp>(
      'api/app/about/about-content-by-id',
      {
        params: {
          AboutId: aboutId,
        },
      }
    );
  }

  /**
   * 使用族別or語別ID找族名和logo圖
   * @param tribeDialectId
   * @returns
   */
  getTribeData(tribeDialectId: string): Observable<getTribeDataResp> {
    return this.httpClient.post<getTribeDataResp>(
      'api/app/dictionary/get-tribe-header-info',
      {
        tribeDialectId: tribeDialectId,
      }
    );
  }

  /**
   *輸入後autoinput
   * @param keyword
   * @param tribeId
   * @returns
   */
  autoSearch(
    keyword: string,
    tribeId: string | null
  ): Observable<autoSearchResp> {
    return this.httpClient.post<autoSearchResp>(
      'api/app/dictionary/get-search-candidate',
      { keyword: keyword, tribeId: tribeId }
    );
  }
}
