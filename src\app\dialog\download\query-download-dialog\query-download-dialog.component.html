<div mat-dialog-title class="success-title">
    <mat-icon class="dialog-close-btn" tabindex="0" (click)="close()">close</mat-icon>
</div>
<mat-dialog-content>
    @if(isLoading){
    <div class="spinner-wrapper-index">
        <mat-spinner class="mat-spinner-color"></mat-spinner>
    </div>
    }@else{
    <form [formGroup]="form">
        <!--下載-->
        <div class="input-group">
            <span class="query-download-item2">
                <span class="font_r">*</span>內容設定
            </span>

            <ul class="advanced-search-cont">
                <li class="advanced-search-item">
                    <span class="checkbox-list">
                        <input id="sourceCheckbox" class="checkbox-list" type="checkbox" disabled="true" checked="true">
                        <label for="sourceCheckbox">詞項</label>
                    </span>
                </li>
                @for (item of formatList; let i=$index; track item) {
                <li class="advanced-search-item">
                    <span class="checkbox-list">
                        <input id="contentCheckbox{{i}}" class="checkbox-list" type="checkbox"
                            (change)="selectContent($event,item.id)">
                        <label for="contentCheckbox{{i}}">{{item.name}}</label>
                    </span>
                </li>
                }
            </ul>
        </div>
        <div class="input-group">
            <span class="query-download-item2">
                <span class="font_r">*</span>格式設定
            </span>
            <!-- 依詞根、衍生詞排序 -->
            <div class="checkbox-group">
                <ul class="checkbox-menu">
                    <label class="select_title">
                        <input id="format-1" type="radio" name="1" checked="true" (change)="selectFormat(1)">
                        <label for="format-1">依詞根、衍生詞排序：</label>
                    </label>
                </ul>
                @if(format===1){
                <ul class="checkbox-menu">
                    <label><input class="checkbox-list" type="checkbox"
                            (change)="selectShowDerivedDetail($event)">顯示衍生詞細部資料</label>
                </ul>
                }
            </div>
            <!-- 依詞項符號順序排序 -->
            <div class="checkbox-group">
                <ul class="checkbox-menu">
                    <label class="select_title">
                        <input id="format-2" type="radio" name="1" (change)="selectFormat(2)">
                        <label for="format-2">依詞項符號順序排序：</label>
                    </label>
                </ul>
                @if(format===2){
                <ul class="checkbox-menu">
                    <label class="select_title">
                        <input id="column1" type="radio" name="2" checked="true" (change)="selectColumn(1)">
                        <label for="column1">單欄</label>
                    </label>
                </ul>
                <ul class="checkbox-menu">
                    <label class="select_title">
                        <input id="column2" type="radio" name="2" (change)="selectColumn(2)">
                        <label for="column2">雙欄</label>
                    </label>
                </ul>
                <ul class="checkbox-menu">
                    <label class="select_title">
                        <label>是否顯示詞根/詞幹：</label>
                    </label>
                </ul>
                <ul class="checkbox-menu">
                    <label class="select_title">
                        <input id="showWordRoot1" type="radio" name="3" checked="true"
                            (change)="selectShowWordRoot(true)">
                        <label for="showWordRoot1">顯示</label>
                    </label>
                </ul>
                <ul class="checkbox-menu">
                    <label class="select_title">
                        <input id="showWordRoot2" type="radio" name="3" (change)="selectShowWordRoot(false)">
                        <label for="showWordRoot2">不顯示</label>
                    </label>
                </ul>
                }

            </div>
            <!-- 依詞根、衍生詞排序 -->
            <div class="checkbox-group">
                <ul class="checkbox-menu">
                    <label class="select_title">
                        <input id="format-3" type="radio" name="1" (change)="selectFormat(3)">
                        <label for="format-3">依華語筆畫順序排序：</label>
                    </label>
                </ul>
            </div>
        </div>
        <div class="input-group">
            <span class="query-download-item2">
                <span class="font_r">*</span>字體設定
            </span>
            <!-- 檔案格式 -->
            <div class="checkbox-group">
                <ul class="checkbox-menu">
                    <label class="select_title">
                        <input id="fontSize1" type="radio" name="4" checked="true" (change)="selectFontSize(3)">
                        <label for="fontSize1">大</label>
                    </label>
                </ul>
                <ul class="checkbox-menu">
                    <label class="select_title">
                        <input id="fontSize2" type="radio" name="4" (change)="selectFontSize(2)">
                        <label for="fontSize2">中</label>
                    </label>
                </ul>
                <ul class="checkbox-menu">
                    <label class="select_title">
                        <input id="fontSize3" type="radio" name="4" (change)="selectFontSize(1)">
                        <label for="fontSize3">小</label>
                    </label>
                </ul>
            </div>
        </div>
        <div class="input-group">
            <span class="query-download-item2">
                <span class="font_r">*</span>字型設定
            </span>
            <!-- 字體 -->
            <div class="checkbox-group">
                <ul class="checkbox-menu">
                    <label class="select_title">
                        <input id="fontFamily1" type="radio" name="5" checked="true" (change)="selectFontFamily(1)">
                        <label for="fontFamily1">Time New
                            Roman</label>
                    </label>
                </ul>
                <ul class="checkbox-menu">
                    <label class="select_title">
                        <input id="fontFamily2" type="radio" name="5" (change)="selectFontFamily(2)">
                        <label for="fontFamily2">Arial</label>
                    </label>
                </ul>
                <ul class="checkbox-menu">
                    <label class="select_title">
                        <input id="fontFamily3" type="radio" name="5" (change)="selectFontFamily(3)">
                        <label for="fontFamily3">Ms Mincho</label>
                    </label>
                </ul>
            </div>
        </div>
        <div class="input-group">
            <span class="query-download-item2">
                <span class="font_r">*</span>檔案格式設定
            </span>
            <!-- 字型設定 -->
            <div class="checkbox-group">
                <ul class="checkbox-menu">
                    <label class="select_title">
                        <input id="fileFormat2" type="radio" name="6" checked="true" (change)="selectFileFormat(2)">
                        <label for="fileFormat2">WORD檔</label>
                    </label>
                </ul>
                <ul class="checkbox-menu">
                    <label class="select_title">
                        <input id="fileFormat1" type="radio" name="6" (change)="selectFileFormat(1)">
                        <label for="fileFormat1">PDF檔</label>
                    </label>
                </ul>
            </div>
        </div>

        <!-- 驗證碼 -->
        <div class="input-group">
            <span class="query-download-item2">
                <span class="font_r">*</span>驗證碼
            </span>
            <div class="input-list">
                <input type="text" title="captcha" placeholder="請輸入驗證碼" class="form-control" style="width: 100%;"
                    formControlName="captcha">
                <img class="input-img" [src]="img" alt="驗證碼">
                <div class="a-box a-color"> <mat-icon (click)="getCaptcha()">refresh</mat-icon></div>
                <span class="a-voice">
                    <div class="material-symbols-outlined font_g"> <mat-icon (click)="play()">volume_up</mat-icon></div>
                </span>
            </div>
        </div>
        <div class="btn-group">
            <input class="btn-list btn-secondary-color" value="取消" type="button" [mat-dialog-close]="false">
            <input class="btn-list btn-primary-color" value="確定" type="button" (click)="download()">
        </div>
    </form>
    }
</mat-dialog-content>