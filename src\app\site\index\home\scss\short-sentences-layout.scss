// Scss Document

.short-sentences-layout {
	margin: 0 auto;
	padding: 30px 50px;
	max-width: 634px;
	display: flex;
	flex-direction: column;
	justify-content: flex-start;
	align-items: center;
	background: #fff url("../../../../../assets/image/short-sentences-bg.png") center top no-repeat;
	background-size: cover;
	width: 100%;
	min-height: 490px;
	box-sizing: border-box;
}

.short-sentences-title {
	margin: 0 auto;
	padding: 0 0  110px;
	display: flex;
	flex-direction: column;
	justify-content: center;
	align-items: center;
	text-align: center;
	font-weight: bold;
	line-height: 1.2;
	.short-sentences-subtitle {
		font-size: 2.5em;
	}

	.short-sentences-main-title {
		font-size: 2.008em;
	}
}

.short-sentences-cont {
	padding-bottom: 32px;
	font-size: 3.125em;
	color: #232127;
	font-weight: bold;
	text-align: center;
	line-height: 1;
}
.short-sentences-translate {
	font-size: 1.875em;
}

@media (max-width: 1368px) {
	.short-sentences-layout {
		padding: 30px 30px;
		max-width: 100%;
	}
	.short-sentences-title {
		.short-sentences-subtitle {
			font-size: 2em;
		}
		.short-sentences-main-title {
			font-size: 1.5em;
		}
	}
	.short-sentences-cont {
		font-size: 2.8em;
	}
	.short-sentences-translate {
		font-size: 1.25em;
	}
}
