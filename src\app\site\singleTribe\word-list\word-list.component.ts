import { HttpErrorResponse } from '@angular/common/http';
import { Component } from '@angular/core';
import { MatDialog } from '@angular/material/dialog';
import { DomSanitizer, SafeHtml } from '@angular/platform-browser';
import { Router, RouterLink } from '@angular/router';
import { environment } from '../../../../environments/environment';
import { AnaphoraSentenceDialogComponent } from '../../../dialog/anaphora-sentence-dialog/anaphora-sentence-dialog.component';
import { apiStatus } from '../../../enum/apiStatus.enum';
import {
  dictionaryItem,
  searchDictionaryDetailResp,
  getShareDetailResp,
  shareDetailWordItem,
  wordItem,
  getWordMarkResp,
  getMarkListResp,
  audioItem,
} from '../../../interface/language.interface';
import { LanguageService } from '../../../service/curl/language.service';
import { ConfirmService } from '../../../service/utils/confirm.service';
import { FileService } from '../../../service/curl/file.service';
import { OwlOptions, CarouselModule } from 'ngx-owl-carousel-o';
import { RootStructureDialogComponent } from '../../../dialog/root-structure-dialog/root-structure-dialog.component';
import { UtilsService } from '../../../service/utils/utils.service';
import { MatIcon } from '@angular/material/icon';
import { NgClass } from '@angular/common';
import {
  MatAccordion,
  MatExpansionPanel,
  MatExpansionPanelHeader,
} from '@angular/material/expansion';
import { GetEthnicityService } from '../../../service/utils/get-ethnicity.service';

export enum ShareType {
  FB = 'fb',
  LINE = 'line',
  IG = 'ig',
}

declare const FB: any;
@Component({
  selector: 'app-word-list',
  templateUrl: './word-list.component.html',
  styleUrl: './word-list.component.scss',
  imports: [
    RouterLink,
    MatIcon,
    NgClass,
    MatAccordion,
    MatExpansionPanel,
    MatExpansionPanelHeader,
    CarouselModule,
  ],
})
export class WordListComponent {
  singleTribe?: {
    id: string;
    name: string;
  };
  ethnicity: string;
  ethnicityId: string;

  isKeyboard: boolean = true;
  isMark: boolean = true;

  keyboardList: { id: string; name: string }[] = []; //虛擬鍵盤list
  wordMarkList: { id: string; name: string }[] = [];

  activePage: number = 1;

  dictionaryList: dictionaryItem[] = [];
  wordItem: shareDetailWordItem = {
    audioItems: [],
    id: '',
    name: '',
    pinyin: '',
    variant: '',
    formationWord: '',
    derivativeRoot: '',
    frequency: 0,
    hit: 0,
    dictionaryNote: '',
    sources: [],
    explanationItems: [],
    dialect: '',
    tribe: '',
    tribeId: '',
    isDerivativeRoot: false,
  };

  selectedDialect: string[] = [];
  selectedSource: string[] = [];

  selectId: string = '';

  shareType = ShareType;

  private currentMediaElement: HTMLAudioElement | null = null;

  customOptions: OwlOptions = {
    loop: false,
    navSpeed: 700,
    dots: true,
    items: 1,
    center: true,
  };
  isDerivativeRoot: boolean = false;
  isImage: boolean = false;
  fontSize: number = 1;
  keyword: string = '';

  constructor(
    private router: Router,
    private languageService: LanguageService,
    private confirmService: ConfirmService,
    private sanitizer: DomSanitizer,
    private matDialog: MatDialog,
    private fileService: FileService,
    private utils: UtilsService,
    private getEthnicityService: GetEthnicityService
  ) {
    this.utils.fontSize$.subscribe((size) => {
      this.fontSize = size === 0 ? 1 : size;
    });

    this.ethnicity = this.getEthnicityService.GetEthnicityName() as string;
    this.ethnicityId = this.getEthnicityService.GetEthnicityId() as string;
  }

  ngOnInit(): void {
    this.utils.setTitle(`${this.ethnicity}-詞項列表`);
    this.initializationSearchOptions();
  }

  ngOnDestroy(): void {
    this.stopMusic();
  }

  initializationSearchOptions() {
    this.languageService.getMarkList(this.ethnicityId).subscribe({
      next: (resp: getMarkListResp) => {
        this.keyboardList = resp.data.items.filter(
          (item) => item.name !== '不拘'
        );
      },
      error: (err: HttpErrorResponse) => {
        console.error(err);
      },
    });
  }

  getWorItem(item: { id: string; name: string }) {
    this.keyword = item.name;
    this.selectId = item.id;
    this.languageService.getShareDetail(item.id).subscribe({
      next: (resp: getShareDetailResp) => {
        if (resp.status === apiStatus.SUCCESS) {
          this.wordItem = resp.data.word;
          this.onPanelOpened(this.dictionaryList[0]);
        } else {
          this.confirmService.showError(resp.message, '錯誤');
        }
      },
      error: (err: HttpErrorResponse) => {
        this.confirmService.showError(err.error.error.details, '錯誤');
      },
    });
    window.scrollTo({
      top: 0,
      behavior: 'smooth',
    });
  }

  sanitizeExplanation(rawHtml: string): SafeHtml {
    return this.sanitizer.bypassSecurityTrustHtml(rawHtml);
  }

  isNameMatched(a: string, b: string): boolean {
    return a?.toLowerCase() === b?.toLowerCase();
  }

  onPanelOpened(item: dictionaryItem) {
    if (!item) {
      return;
    }

    item.isOpenPanel = true;
    item.loading = true;
    this.languageService.searchDictionaryDetail(item.id).subscribe({
      next: (resp: searchDictionaryDetailResp) => {
        if (resp.status === apiStatus.SUCCESS) {
          item.loading = false;
          this.isDerivativeRoot = resp.data.isDerivativeRoot;
          this.isImage = resp.data.isImage;
          item.wordItem = resp.data.word;
        } else {
          this.confirmService.showError(resp.message, '錯誤');
        }
      },
      error: () => {},
    });
  }

  onPanelClose(item: dictionaryItem) {
    item.isOpenPanel = false;
  }

  clickAnaphoraSentence(id: string | null) {
    if (id === null) {
      return;
    }
    this,
      this.matDialog.open(AnaphoraSentenceDialogComponent, {
        disableClose: true,
        autoFocus: false,
        width: '60%',
        data: {
          id: id,
        },
      });
  }

  share(type: ShareType, id: string) {
    const shareUrl = `${environment.sitePath}/sharePage?id=${id}`;
    switch (type) {
      case this.shareType.FB:
        FB.ui(
          {
            method: 'share',
            href: shareUrl,
          },
          (response: any) => {
            if (response && !response.error_message) {
              console.log('分享成功');
            } else {
              console.error('分享失敗或取消', response);
            }
          }
        );
        break;
      case this.shareType.LINE:
        window.open(
          `https://social-plugins.line.me/lineit/share?url=${environment.sitePath}/sharePage?id=${id}`,
          '_blank',
          'noopener,noreferrer'
        );
        break;
    }
  }

  getRootStructure(dictionaryId: string) {
    this.matDialog.open(RootStructureDialogComponent, {
      disableClose: true,
      autoFocus: false,
      width: '60%',
      data: {
        dictionaryId: dictionaryId,
      },
    });
  }

  question(item: wordItem) {
    window.open(
      `${environment.sitePath}/singleQuestion?id=${item.id}&tribeId=${this.ethnicityId}`,
      '_blank'
    );
  }

  keyboardUp(id: string) {
    const targetDiv = document.getElementById('word-description-list');
    if (targetDiv) {
      targetDiv.scrollIntoView({
        behavior: 'smooth',
        block: 'start', // This will scroll the element to the top of the viewport
      });
    }
    this.languageService.getWordList(this.ethnicityId, id).subscribe({
      next: (resp: getWordMarkResp) => {
        if (resp.status === apiStatus.SUCCESS) {
          this.wordMarkList = resp.data.wordItems;
        } else {
        }
      },
      error: () => {},
    });
  }

  play(item: audioItem) {
    if (this.currentMediaElement) {
      this.currentMediaElement.pause();
      this.currentMediaElement.remove();
    }
    this.fileService.getAudioFile(item.fileId).subscribe({
      next: (resp: string) => {
        let mediaElement: HTMLAudioElement = document.createElement('audio');
        mediaElement.style.display = 'none'; // 這行讓音頻播放器隱藏
        mediaElement.setAttribute('src', resp); // 設置音頻源
        mediaElement.setAttribute('controls', 'true'); // 加入控制條
        document.body.appendChild(mediaElement);
        mediaElement.play();
        mediaElement.addEventListener('ended', () => {
          this.currentMediaElement = null; // 重置當前音樂播放器
        });

        this.currentMediaElement = mediaElement; // 儲存當前的音樂播放器
      },
      error: () => {},
    });
  }

  stopMusic() {
    if (this.currentMediaElement) {
      this.currentMediaElement.pause();
      this.currentMediaElement.remove();
      this.currentMediaElement = null;
    }
  }

  onCopy(event: ClipboardEvent) {
    event.preventDefault(); // 阻止預設複製行為
    const selection = window.getSelection();
    if (selection) {
      let copiedText = selection
        .toString()
        .replace(/\r?\n/g, ' ') // 移除換行
        .replace(/\s+/g, ' '); // 將多個空格替換為單一空格
      event.clipboardData?.setData('text/plain', copiedText);
    }
  }

  openImage(item: shareDetailWordItem) {
    item.explanationItems.map((item) => {
      return (item.isImage = !item.isImage);
    });
  }
}
