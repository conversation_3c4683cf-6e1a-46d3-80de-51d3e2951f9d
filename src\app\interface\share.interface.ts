export interface defaultItem {
  status: string;
  message: string;
}

export const KeyboardList = [
  '’',
  '^',
  ':',
  'ṟ',
  '_',
  'ē',
  'é',
  'ɨ',
  'ʉ',
  '.',
  '-',
];

export interface getBrowseVisitorsResp extends defaultItem {
  data: {
    dayBrowseCount: number;
    totalBrowseCount: number;
  };
}

export interface getTodaySentenceResp extends defaultItem {
  data: {
    id: string;
    tribe: string; // 族
    originalSentence: string; // 原句
    chineseSentence: string; // 中文句子
  };
}

export interface getAboutListResp extends defaultItem {
  data: aboutItem[];
}

export interface aboutItem {
  id: string;
  title: string;
  position: number;
}

export interface getAboutResp extends defaultItem {
  data: {
    id: string;
    title: string;
    content: string;
  };
}

export interface getTribeDataResp extends defaultItem {
  data: {
    tribeBannerFileUrl: string;
    tribeName: string;
  };
}

export interface autoSearchResp extends defaultItem {
  data: {
    candidateWords: string[];
  };
}
