@use "./scss/index-search-layout.scss";
@use "./scss/news-layout.scss";
@use "./scss/short-sentences-layout.scss";
@use "./scss/fb-layout.scss";
@use "./scss/index-search.scss";

//進階搜尋
.advanced-search-layout {
    margin: 20px 0;
    padding: 0;
    display: flex;
    overflow: hidden;
    border-radius: 10px;
    flex-direction: column;
    align-items: stretch;
    justify-content: space-between;
    background-color: #fff;
    box-shadow: 0 0 10px #36425029;
    width: 100%;
    .advanced-search-title {
        margin: 10px 0 0 10px;
        padding: 5px 5px;
        font-size: 1.6em;
        text-align: left;
        font-weight: bold;
        border-left: #4a7f42 10px solid;
        span {
            color: red;
        }
    }
    .advanced-search-cont-layout {
        padding: 10px 10px;
        .advanced-search-cont {
            margin: 0;
            padding: 0;
            display: flex;
            flex-wrap: wrap; // 允許多行排列
            list-style: none;
            align-items: center;
            .advanced-search-item {
                padding: 5px 0; // 調整間距以確保上下對齊
                display: flex;
                flex-direction: row;
                align-items: center;

                .checkbox-list {
                    padding: 0;
                    display: flex;
                    align-items: baseline; // 讓 checkbox 和 label 垂直對齊
                }
                .theme-count {
                    color: #0a91d4;
                }

                label {
                    font-size: 1.3em;
                    margin-left: 5px; // 調整 checkbox 和 label 之間的間距
                }
            }
        }
    }
}

@media (max-width: 2048px) {
    .advanced-search-cont-layout {
        .advanced-search-cont {
            .advanced-search-item {
                width: 20%; // 每行顯示 2 個 checkbox，留一些空白間距
            }
        }
    }
}
@media (max-width: 1061px) {
    .advanced-search-cont-layout {
        .advanced-search-cont {
            .advanced-search-item {
                width: 23%; // 每行顯示 2 個 checkbox，留一些空白間距
            }
        }
    }
}

@media (max-width: 893px) {
    .advanced-search-cont-layout {
        .advanced-search-cont {
            .advanced-search-item {
                width: 48%; // 每行顯示 2 個 checkbox，留一些空白間距
            }
        }
    }
}
@media (max-width: 466px) {
    .advanced-search-cont-layout {
        .advanced-search-cont {
            .advanced-search-item {
                width: 100%; // 每行顯示 1 個 checkbox
            }
        }
    }
}

.btn-list {
    display: flex;
    align-items: center;
}

.keyboard-group {
    width: 100%;
    button {
        background-color: #c7c7c7;
        margin: 0.5em;
        padding: 10px 20px;
        border-radius: 5px;
        -moz-user-select: none;
        background-image: none;
        border: 1px solid transparent;
        border-radius: 5px;
        cursor: pointer;
        display: inline-block;
        font-weight: 400;
        line-height: 1.2;
        text-align: center;
        font-size: 2em;
        white-space: nowrap;
    }
}

.select-list {
    color: red;
    font-size: 1.5em;
    .select-list-content {
        text-underline-offset: 3px; /* 調整底線距離 */
    }
}

.search-bar {
    padding-right: 100px;
}

//搜尋樣式
.search-group {
    width: 100%;
    display: flex;
    flex-direction: row;
    // align-items: center;
    align-items: flex-start;

    .search-box1 {
        width: 18%;
        position: relative;
        display: inline-flex;
        // margin-right: 20px;
        margin: 10px 20px 10px 0;
        min-width: 296px;
        .search-a1 {
            position: absolute;
            top: 24px;
            right: 0;
            display: block;
            width: 55px;
            height: 55px;
            color: #000;
        }
    }
    .search-all {
        width: 70%;
        margin: 0 20px 10px 0;
        display: flex;
        flex-direction: column;
        align-items: center;
        position: relative;
        .search-box2 {
            width: 100%;
            // width: 70%;
            position: relative;
            display: inline-flex;
            // margin-right: 20px;
            // margin: 10px 20px 10px 0;
            min-width: 296px;
            .search-a2 {
                position: absolute;
                top: 24px;
                right: 40px;
                display: block;
                width: 55px;
                height: 55px;
                color: #000;
            }
            .search-a3 {
                position: absolute;
                top: 24px;
                right: 0;
                display: block;
                width: 55px;
                height: 55px;
                color: #000;
            }
        }
        //搜尋框架
        .search-frame {
            padding: 20px 10px;
            // max-width: 900px;
            width: 97%;
            box-sizing: border-box;
            background-color: #ffffff;
            border-radius: 10px;
            box-shadow: 0px 0px 5px 2px rgba(0, 0, 0, 0.2);
            position: absolute;
            top: 100%;
        }
        .search-frame-info {
            cursor: pointer;
            font-size: 1.5em;
            // padding: 5px 10px;
            border-radius: 10px;
            width: 100%;
            &:hover,
            &:focus {
                background: #e4e6eb;
                // opacity: 0.5;
            }
            &:active,
            &.active {
                background-color: #e4e6eb;
                border-color: #e4e6eb;
            }
            .search-frame-name {
                padding-left: 10px;
            }
        }
    }
}
.search-bar {
    width: 100%;
    // margin: 10px 0;
    padding: 16px 35px;
    background-color: #fff;
    background-image: none;
    border: 1px solid #ccc;
    border-radius: 80px;
    box-shadow: 0 1px 1px rgba(0, 0, 0, 0.075) inset;
    display: inline-block;
    transition:
        border-color 0.15s ease-in-out 0s,
        box-shadow 0.15s ease-in-out 0s;
    box-sizing: border-box;
    box-shadow: 0px 0px 5px 2px rgba(0, 0, 0, 0.2);
    &:focus {
        // border: 3px solid #00b4ff;
        // outline: 0;
        // box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 25%);
        border: 3px solid #4a7f42;
        outline: 0;
        box-shadow: 0 0 0 0.25rem #d8eed4;
    }
}

@media (max-width: 1000px) {
    .search-group {
        .search-box1 {
            .font_24 {
                font-size: 1.125em;
            }
            .search-a1 {
                top: 20px;
            }
        }
        .search-all {
            width: 48%;
            .search-box2 {
                .font_24 {
                    font-size: 1.125em;
                }
                .search-a2 {
                    top: 20px;
                }
                .search-a3 {
                    top: 20px;
                }
            }
        }
        .btns {
            .btn-box {
                width: 100%;
                font-size: 1.125em;
            }
        }
    }
    .keyboard-group {
        max-width: 350px;
        padding: 5px;
        .keyboard-box {
            .keyboard-btn {
                max-width: 45px;
                width: 100%;
                padding: 0;
                margin: 4px;
                .keyboard_font {
                    font-size: 0.75em;
                }
            }
        }
    }
}

@media (max-width: 805px) {
    .index-search-layout {
        flex-wrap: wrap;
        padding: 20px;
        .search-group {
            flex-wrap: wrap;
            .search-box1 {
                width: 100%;
                margin: 10px 0;
            }
            .search-all {
                width: 100%;
                margin: 10px 0;
                // .search-box2{
                // }
            }
            .btns {
                width: 100%;
            }
        }
    }
}
