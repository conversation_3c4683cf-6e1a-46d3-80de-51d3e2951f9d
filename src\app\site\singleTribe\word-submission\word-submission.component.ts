import { AfterViewInit, Component, OnInit, ViewChild } from '@angular/core';
import { MatTabGroup, MatTab } from '@angular/material/tabs';
import { GetEthnicityService } from '../../../service/utils/get-ethnicity.service';
import { ActivatedRoute, RouterLink } from '@angular/router';
import { UtilsService } from '../../../service/utils/utils.service';
import { MethodDescriptionComponent } from './method-description/method-description.component';
import { NgIf } from '@angular/common';
import { WantContributeComponent } from './want-contribute/want-contribute.component';
import { WantKnowComponent } from './want-know/want-know.component';
import { SubmissionListComponent } from './submission-list/submission-list.component';

@Component({
  selector: 'app-word-submission',
  templateUrl: './word-submission.component.html',
  styleUrl: './word-submission.component.scss',
  imports: [
    RouterLink,
    MatTabGroup,
    MatTab,
    MethodDescriptionComponent,
    NgIf,
    WantContributeComponent,
    WantKnowComponent,
    SubmissionListComponent,
  ],
})
export class WordSubmissionComponent implements OnInit, AfterViewInit {
  @ViewChild('tabGroup') tabGroup!: MatTabGroup;
  ethnicity: string | null = null;
  wantKnowValue: string = '';
  previousTabIndex: number = 0; // 記錄前一個分頁索引
  constructor(
    private getEthnicityService: GetEthnicityService,
    private activatedRoute: ActivatedRoute,
    private utils: UtilsService
  ) {}

  ngOnInit(): void {
    this.ethnicity = this.getEthnicityService.GetEthnicityName();
    this.utils.setTitle(`${this.ethnicity}-創詞投稿`);
  }
  ngAfterViewInit(): void {
    this.activatedRoute.queryParamMap.subscribe((queryParamMap) => {
      if (queryParamMap.get('tab')) {
        this.tabGroup.selectedIndex = parseInt(
          queryParamMap.get('tab') as string
        );
      }
    });
  }

  onTabChange(index: number) {
    // 如果是從「我想知道」(index 2) 到「我要投稿」(index 1)，保持 wantKnowValue
    if (this.previousTabIndex === 2 && index === 1) {
      this.previousTabIndex = index; // 更新前一個索引
      return;
    }

    // 如果從「我要投稿」(index 1) 切換到其他分頁，清空 wantKnowValue
    if (this.previousTabIndex === 1 && index !== 1) {
      this.wantKnowValue = '';
    }

    this.previousTabIndex = index; // 更新前一個索引
  }

  wantKnow(value: string) {
    this.wantKnowValue = value;
  }

  back(event: Event) {
    event.preventDefault();
    history.back();
  }
}
