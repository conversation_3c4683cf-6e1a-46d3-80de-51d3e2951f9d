<form [formGroup]="form">
    <main class="master-pages-container-layout">
        <!--族語分類-->
        <div class="index-search-layout">
            <!-- <h2 class="master-index-title">跨族語檢索</h2>
            <div class="index-search-cont">
                <input type="text" class="form-control" title="請輸入中文或族語" placeholder="請輸入中文或族語"
                    formControlName="keyword" #keywordInput>
                <div class="index-search-btn">
                    <button class="btn-list btn-primary-solid" (click)="openLanguageList()">選擇族語語別
                        @if(isLanguage){
                        <mat-icon>keyboard_arrow_down</mat-icon>
                        }@else{
                        <mat-icon>keyboard_arrow_up</mat-icon>
                        }
                    </button>
                    <input class="btn-list btn-primary-solid" value="搜尋" type="button" (click)="search()">
                </div>
            </div> -->


            <div class="index-search-title">
                <span class="material-symbols-outlined font_30 font_r10">translate</span>
                <h2>跨族語檢索</h2>
            </div>
            <div class="search-group">
                <div class="search-box1" style="min-width: 296px;" (click)="openLanguageList($event)">
                    <input class="search-bar font_24" type="text" title="language" placeholder="族語類別(不拘)" readonly>
                    <a class="search-a1" href="" (click)="$event.preventDefault()">
                        @if(isLanguage){
                        <mat-icon>keyboard_arrow_down</mat-icon>
                        }@else{
                        <mat-icon>keyboard_arrow_up</mat-icon>
                        }
                    </a>
                </div>
                <div class="search-all" #searchAll>
                    <div class="search-box2">
                        <input class="search-bar font_24" type="text" placeholder="請輸入族語或中文" formControlName="keyword"
                            (keydown.enter)="search($event); keywordInput.blur()" #keywordInput>
                        <a class="search-a2" href="" (click)="clear($event)"><span
                                class="material-symbols-outlined">close</span></a>
                        <a class="search-a3" href="" (click)="openKeyboard($event)"><span
                                class="material-symbols-outlined">keyboard_alt</span></a>
                    </div>
                    <!--搜尋框架-->
                    @if(autoInputList.length>0){
                    <div class="search-frame">
                        @for (value of autoInputList; track value) {
                        <div class="search-frame-info" (click)="selectValue(value)">
                            <span class="search-frame-name">{{value}}</span>
                        </div>
                        }
                    </div>
                    }
                </div>

                <div class="btns">
                    <input class="btn-box btn-primary-color" value="搜尋" type="button" (click)="search($event)">
                </div>
            </div>
            @if(isKeyboard){
            <div class="keyboard-group">
                <div class="keyboard-box">
                    @for (item of keyboardList; track item) {
                    <button class="keyboard-btn" type="button" (click)="keyboardUp(item,keywordInput)">
                        <span class="keyboard_font">{{item}}</span>
                    </button>
                    }
                </div>
            </div>
            }

            <!--進階搜尋-->
            @if(isLanguage){
            <div class="advanced-search-layout">
                <div class="advanced-search-title">請選擇族語 <span>(可多選)</span> </div>
                <div class="advanced-search-cont-layout">
                    <ul class="advanced-search-cont">
                        @for ( item of languageList; let i=$index; track i) {
                        <li class="advanced-search-item">
                            <span class="checkbox-list">
                                <input id="languageCheckbox{{i}}" type="checkbox" [value]="item.id"
                                    (change)="languageListChange($event, item.id)" [checked]="item.active">
                                <label for="languageCheckbox{{i}}">{{item.name}}</label>
                            </span>
                        </li>
                        }
                    </ul>
                </div>
                <div class="advanced-search-title">請選擇語別 <span>(可多選)</span> </div>
                <div class="advanced-search-cont-layout">
                    <ul class="advanced-search-cont">
                        @for ( item of dialectList; let i=$index; track i) {
                        <li class="advanced-search-item">
                            <span class="checkbox-list">
                                <input id="dialectCheckbox{{i}}" type="checkbox" [value]="item.id"
                                    (change)="dialectListChange($event, item.id,item.tribeId)" [checked]="item.active"
                                    [disabled]="item.disabled">
                                <label for="dialectCheckbox{{i}}">{{item.name}}</label>
                            </span>
                            <!-- <span class="theme-count">({{item.count}})</span> -->
                        </li>
                        }
                    </ul>
                </div>
            </div>
            }
            @if(!isLanguage&&selectList.length>0){
            <div style="width: 100%;">
                <span class="select-list">
                    已選擇 :
                    <span class="select-list-content">
                        @for ( item of selectList; track $index) {
                        {{item}}
                        @if(selectList.length-1>$index){
                        、
                        }
                        }
                    </span>
                </span>
            </div>
            }

            <div class="index-search-title">
                <span class="material-symbols-outlined font_30 font_r10">local_library</span>
                <h2>族別分類</h2>
                <!-- <h2 class="master-index-title">族別分類</h2> -->
                <div class="index-search-cont">
                    <ul class="index-search-list-layout">
                        @for ( item of groupList; track $index) {
                        <li class="index-search-list">
                            <a href="" (click)="singleTribeSearch($event,item)" class="index-search-list-item">
                                <span><img [src]="item.src" [alt]="item.name"></span>
                                <span>{{item.name}}</span>
                            </a>
                        </li>
                        }
                    </ul>
                </div>
            </div>
        </div>
        <div class="cont-row">
            <!--每日一句-->
            <div class="cont short-sentences-layout">
                <h2 class="short-sentences-title">
                    <span class="short-sentences-main-title">每日一句</span>
                    <span class="short-sentences-subtitle">
                        {{todaySentence.tribe}}
                    </span>
                </h2>

                <div class="short-sentences-cont">
                    {{todaySentence.originalSentence}}
                </div>
                <div class="short-sentences-translate">
                    {{todaySentence.chineseSentence}}
                </div>
            </div>
            <!--最新消息-->
            <div class="cont news-layout">
                <div class="index-search-title">
                    <span class="material-symbols-outlined font_30 font_r10">brand_awareness</span>
                    <h2>最新消息</h2>
                </div>
                <ul class="news-cont">
                    @for ( item of newsList; track $index) {
                    <li class="news-cont-list">
                        <a [routerLink]="'/news/detail'" [queryParams]="{ id: item.id }" class="news-cont-item">
                            <span class="news-cont-item-date">{{item.date |date :'YYYY/MM/dd'}}</span>
                            <span class="news-cont-item-text">{{item.title}}</span>
                        </a>
                    </li>
                    }
                </ul>
            </div>
            <!--FB粉絲團-->
            <!-- <div class="cont fb-layout">
                <div id="fb-root"></div>
                <div class="fb-page" data-href="https://www.facebook.com/edictionary.ilrdf/" data-tabs="timeline"
                    data-width="400" data-height="497" data-small-header="true" data-adapt-container-width="true"
                    data-hide-cover="false" data-show-facepile="true">
                    <blockquote cite="https://www.facebook.com/edictionary.ilrdf/" class="fb-xfbml-parse-ignore"><a
                            href="https://www.facebook.com/edictionary.ilrdf/">原住民族語言線上辭典</a></blockquote>
                </div>
            </div> -->
        </div>
    </main>
</form>