<main class="master-pages-container-layout">
    <div class="master-pages-container-cont">
        <div class="cont pages-cont-layout">
            <div class="pages-cont-list-layout">

                <!--路徑列-->
                <div class="breadcrumb-layout">
                    <div>
                        <nav class="breadcrumb-cont">
                            <ol class="breadcrumb">
                                <li class="breadcrumb-item">
                                    <a href="" (click)="$event.defaultPrevented" [routerLink]="'/home'">
                                        <span>首頁</span>
                                        <span class="material-symbols-outlined">keyboard_arrow_right</span>
                                    </a>
                                </li>
                                <li class="breadcrumb-item">
                                    <a href="" (click)="$event.defaultPrevented">
                                        <span>{{ethnicity}}</span>
                                        <span class="material-symbols-outlined">keyboard_arrow_right</span>
                                    </a>
                                </li>
                                <li class="breadcrumb-item active">全文下載</li>
                            </ol>
                        </nav>
                    </div>
                    <div>
                        <nav class="breadcrumb-cont">
                            <ol class="breadcrumb">
                                <li class="breadcrumb-item">
                                    <a hre="" (click)="back($event)">
                                        <span>&lt;&lt;回上一頁</span>
                                    </a>
                                </li>
                            </ol>
                        </nav>
                    </div>
                </div>
                <div style="width: 100%;">
                    <main class="text-download-layout">
                        <!--表格1-->
                        <table class="table-list-layout rwd-table03">
                            <tbody>
                                <tr class="bg_g1">
                                    @for (item of list; track $index) {
                                    <th class="th_no table_g" [style.width]="item.width">{{item.title}}</th>
                                    }
                                </tr>
                                @if(fileList.length>0){
                                @for (item of fileList;let index=$index; track item) {
                                <tr>
                                    <td class="text_c">
                                        <span class="rwd-th">項次</span>
                                        {{index+1}}
                                    </td>
                                    <td class="text_l">
                                        <span class="rwd-th">文件</span>
                                        {{item.name}}<span style="color: #21abb3;">{{item.lastModificationText}}</span>
                                    </td>
                                    <td class="text_c">
                                        <span class="rwd-th">格式</span>
                                        <div class="table-download">
                                            @for (fileItem of item.fileItems; track fileItem) {
                                            @if(fileItem.url){
                                            <a [href]="fileItem.url">
                                                @if(fileItem.fileType==='odt'){
                                                <img src="icons/icon-odt.svg" alt="ODT">
                                                <div class="a-font font_odt">ODT</div>
                                                }@else{
                                                <img src="icons/icon-pdf.svg" alt="PDF">
                                                <div class="a-font font_pdf">PDF</div>
                                                }
                                            </a>
                                            }@else {
                                            <a href="" (click)="$event.preventDefault()">
                                                <div class="disabled">
                                                    @if(fileItem.fileType==='odt'){
                                                    <img src="icons/icon-odt.svg" alt="ODT">
                                                    <div class="a-font font_odt">ODT</div>
                                                    }@else{
                                                    <img src="icons/icon-pdf.svg" alt="PDF">
                                                    <div class="a-font font_pdf">PDF</div>
                                                    }
                                                </div>
                                            </a>
                                            }

                                            }
                                        </div>
                                    </td>
                                    <td class="text_c">
                                        <span class="rwd-th">時間</span>
                                        {{item.lastModificationTime|date:"YYYY-MM-dd"}}
                                    </td>
                                </tr>
                                }
                                }@else{
                                <tr>
                                    <td colspan="4" style="text-align: center;">沒有找到符合條件的資料</td>
                                </tr>
                                }

                            </tbody>
                        </table>
                    </main>
                </div>
            </div>
        </div>
    </div>
</main>