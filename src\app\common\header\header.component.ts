import { Component } from '@angular/core';
import { AppComponent } from '../../app.component';
import { IntroductionService } from '../../service/curl/introduction.service';
import { getIntroductionListResp } from '../../interface/introduction.interface';
import { ShareService } from '../../service/curl/share.service';
import {
  getAboutListResp,
  getTribeDataResp,
} from '../../interface/share.interface';
import { ActivatedRoute, RouterLink, RouterLinkActive } from '@angular/router';
import { NgClass } from '@angular/common';
import { UtilsService } from '../../service/utils/utils.service';

export enum FontSizeEunm {
  big = 1.5,
  middle = 1,
  small = 0.75,
}

@Component({
  selector: 'app-header',
  templateUrl: './header.component.html',
  styleUrl: './header.component.scss',
  imports: [RouterLink, NgClass, RouterLinkActive],
})
export class HeaderComponent {
  fontSize: FontSizeEunm = FontSizeEunm.middle;
  FontSize = FontSizeEunm;
  aboutId: string = '';
  singleTribe?: {
    id: string;
    name: string;
  };
  tribeName?: string;
  tribeId?: string;

  introductionList: { id: string; title: string }[] = [];
  constructor(
    private appComponent: AppComponent,
    private introductionService: IntroductionService,
    private shareService: ShareService,
    private activatedRoute: ActivatedRoute,
    private utilsService: UtilsService
  ) {}

  changeFontsize(event: Event, fontSize: FontSizeEunm) {
    event.preventDefault();
    this.fontSize = fontSize;
    this.appComponent.changeFontSize(fontSize);
    this.utilsService.setFontSize(fontSize);
  }

  ngOnInit() {
    this.activatedRoute.queryParamMap.subscribe((queryParam) => {
      if (queryParam && queryParam.get('tribeId')) {
        this.tribeName = queryParam.get('tribeName') as string;
        this.tribeId = queryParam.get('tribeId') as string;

        this.shareService
          .getTribeData(queryParam.get('tribeId') as string)
          .subscribe({
            next: (resp: getTribeDataResp) => {
              let singleTribe = JSON.stringify({
                id: queryParam.get('tribeId') as string,
                name: resp.data.tribeName,
                src: resp.data.tribeBannerFileUrl,
              });
              sessionStorage.setItem('singleTribe', singleTribe);
              this.initialization();
            },
            error: () => {},
          });
      } else {
        this.initialization();
      }
    });
  }

  initialization() {
    this.getAboutList();
    this.getIntroductionList();
  }

  getIntroductionList() {
    this.introductionService
      .getIntroductionList(this.tribeId as string)
      .subscribe({
        next: (resp: getIntroductionListResp) => {
          this.introductionList = resp.data.items;
        },
      });
  }

  getAboutList() {
    this.shareService.getAboutList().subscribe({
      next: (resp: getAboutListResp) => {
        this.aboutId = resp.data[0].id;
      },
      error: () => {},
    });
  }

  closeMenu() {
    const checkbox: HTMLInputElement | null =
      document.querySelector('#burger-menu');
    if (checkbox) checkbox.checked = false;
  }
}
