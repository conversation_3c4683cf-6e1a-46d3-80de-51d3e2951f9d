@use "./scss/want-contribute.scss";

textarea {
    resize: vertical; /* 只允許垂直調整 */
    min-height: 200px; /* 最小高度 */
}

.captcha-box {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    mat-icon {
        cursor: pointer;
        padding: 20px 0.2em;
        font-size: 2em;
        color: green;
    }
}

.tip-btn {
    cursor: pointer;
}

.search-box {
    width: 100%;
    position: relative;
    display: inline-flex;
    margin: 10px 0 10px 0;
}
.search-box .search-a1 {
    position: absolute;
    top: 22px;
    right: 0;
    display: block;
    width: 40px;
    height: 40px;
    color: #000;
}

.search-bar {
    width: 100%;
    padding: 11px 20px;
    background-color: #fff;
    background-image: none;
    border: 1px solid #ccc;
    border-radius: 5px;
    display: inline-block;
    transition:
        border-color 0.15s ease-in-out 0s,
        box-shadow 0.15s ease-in-out 0s;
    box-sizing: border-box;
}
.search-bar:focus {
    border: 3px solid #4a7f42;
    outline: 0;
    box-shadow: 0 0 0 0.25rem #d8eed4;
}

.keyboard2 {
    margin: 20px 0px 20px 10px;
}
