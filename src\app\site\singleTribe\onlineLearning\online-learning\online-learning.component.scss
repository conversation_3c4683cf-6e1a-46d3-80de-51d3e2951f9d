@use "./scss/online-learning.scss";
@use "./scss/second-search.scss";

.breadcrumb-layout {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
}

.breadcrumb-item {
    cursor: pointer;
}

// Scss Document
.word-description-list-layout {
    margin: 0.5em 0;
    padding: 0;
    display: block;
    min-width: 64vw;
}

.advanced-search-title-group {
    display: flex;
    justify-content: space-between;
    align-items: center;
    mat-icon {
        width: 30px;
        font-size: 2em;
        cursor: pointer;
    }
}

.advanced-search-title {
    margin: 10px 0 0 10px;
    padding: 5px 5px;
    font-size: 1.6em;
    text-align: left;
    font-weight: bold;
    border-left: #4a7f42 10px solid;
    span {
        color: red;
    }
}
.advanced-search-cont-layout {
    padding: 10px 10px;
    .advanced-search-cont {
        margin: 0;
        padding: 0;
        display: flex;
        flex-wrap: wrap; // 允許多行排列
        list-style: none;
        align-items: center;
        .advanced-search-item {
            box-sizing: border-box;
            padding: 5px 0; // 調整間距以確保上下對齊
            display: flex;
            align-items: center;
            .checkbox-list {
                padding: 0;
                display: flex;
                align-items: baseline; // 讓 checkbox 和 label 垂直對齊
            }
            .theme-count {
                color: #0a91d4;
            }
            label {
                font-size: 1em;
                margin-left: 5px; // 調整 checkbox 和 label 之間的間距
                // white-space: nowrap;
            }
        }
    }
}

@media (max-width: 3000px) {
    .advanced-search-cont-layout {
        .advanced-search-cont {
            .advanced-search-item {
                width: 50%; // 每行顯示 2 個 checkbox，留一些空白間距
            }
        }
    }
}

@media (max-width: 1200px) {
    .advanced-search-cont-layout {
        .advanced-search-cont {
            .advanced-search-item {
                width: 20%; // 每行顯示 2 個 checkbox，留一些空白間距
            }
        }
    }
}
@media (max-width: 850px) {
    .advanced-search-cont-layout {
        .advanced-search-cont {
            .advanced-search-item {
                width: 25%; // 每行顯示 2 個 checkbox，留一些空白間距
            }
        }
    }
}

@media (max-width: 750px) {
    .advanced-search-cont-layout {
        .advanced-search-cont {
            .advanced-search-item {
                width: 50%; // 每行顯示 2 個 checkbox，留一些空白間距
            }
        }
    }
}
@media (max-width: 410px) {
    .advanced-search-cont-layout {
        .advanced-search-cont {
            .advanced-search-item {
                width: 100%; // 每行顯示 2 個 checkbox，留一些空白間距
            }
        }
    }
}

.word-description-container {
    display: flex;
    align-items: flex-start;
    max-width: 68vw;
}

.advanced-container {
    min-width: 22vw;
    margin-right: 2em;
}

@media (max-width: 1200px) {
    .word-description-container {
        display: flex;
        flex-direction: column;
        max-width: 100%;
        width: 100%;
    }
    .advanced-container {
        width: 90%;
        margin: 0 10px;
        padding: 10px;
    }
    .word-description-list-layout {
        width: 90%;
        margin: 0 10px;
        padding: 10px;
    }
}

.notfound-group {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-top: 10vh;

    .notfound-text {
        font-size: 2em;
        font-weight: bold;
    }
}

mat-icon {
    cursor: pointer;
}

.bottom-border {
    border-bottom: 3px solid; /* 底線顏色 & 粗細 */
    padding-bottom: 2px; /* 控制底線與文字的距離 */
}
.bottom-border-hidden {
    border-bottom: 3px solid transparent; /* 底線顏色 & 粗細 */
    padding-bottom: 2px; /* 控制底線與文字的距離 */
}

.spinner-wrapper-index {
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 998;

    app-spinner {
        width: 6rem;
        height: 6rem;
    }
}

