import {
  After<PERSON>iewInit,
  Component,
  ElementRef,
  OnDestroy,
  OnInit,
  Renderer2,
  ViewChild,
} from '@angular/core';
import { GetEthnicityService } from '../../../../service/utils/get-ethnicity.service';
import { LanguageService } from '../../../../service/curl/language.service';
import { debounceTime, forkJoin, Subject } from 'rxjs';
import { HttpErrorResponse } from '@angular/common/http';
import { autoSearchResp } from '../../../../interface/share.interface';
import { DownloadService } from '../../../../service/curl/download.service';
import {
  getQueryDownloadListReq,
  getQueryDownloadListResp,
  queryDownloadItem,
} from '../../../../interface/download.interface';
import { QueryDownloadDialogComponent } from '../../../../dialog/download/query-download-dialog/query-download-dialog.component';
import { MatDialog } from '@angular/material/dialog';
import { ConfirmService } from '../../../../service/utils/confirm.service';
import { UtilsService } from '../../../../service/utils/utils.service';
import { ShareService } from '../../../../service/curl/share.service';
import { RouterLink } from '@angular/router';
import { FormsModule } from '@angular/forms';
import { MatIcon } from '@angular/material/icon';
import { NgIf } from '@angular/common';
import { PaginatorComponent } from '../../../../utils/paginator/paginator.component';

@Component({
  selector: 'app-query-download',
  templateUrl: './query-download.component.html',
  styleUrl: './query-download.component.scss',
  imports: [RouterLink, FormsModule, MatIcon, NgIf, PaginatorComponent],
})
export class QueryDownloadComponent
  implements OnInit, AfterViewInit, OnDestroy
{
  @ViewChild('keywordInput') keywordInput!: ElementRef;
  @ViewChild('searchAll') searchAll!: ElementRef;
  ethnicity: string | null = null;
  tribeId: string | null = null;

  keyword: string = '';
  private keywordChanged: Subject<string> = new Subject<string>();
  autoInputList: string[] = [];
  showKeyword: string = '';
  keyboardList: string[] = [];
  isKeyboard: boolean = true;
  isDialect: boolean = false;
  isAdvanced: boolean = false;

  dialectList: {
    id: string;
    name: string;
    selected: boolean;
  }[] = []; //方言list
  symbolList: { id: string; name: string }[] = []; //符號List
  categoryList: { id: string; name: string }[] = []; //範疇List
  partOfSpeechList: { id: string; name: string }[] = []; //詞類List
  restrictSearchList: { value: number; name: string }[] = []; //僅搜尋List
  sourceList: { id: string; name: string; selected: boolean }[] = []; //來源List

  symbolStart: string | null = null;
  symbolEnd: string | null = null;
  category: string | null = null;
  partOfSpeech: string | null = null;
  restrictSearch: number = 0;
  source: string | null = null;

  pageSize: number = 10;
  nowPage: number = 1;
  activePage: number = 1;
  totalCount: number = 0;
  pageShowCount: number = 5; //分頁器秀幾個

  selectedDialect: string[] = [];
  selectedSource: string[] = [];
  queryDownloadList: queryDownloadItem[] = [];

  selectList: string[] = [];
  deleteList: string[] = [];
  isAllStatus: boolean = false;

  searchReq!: getQueryDownloadListReq;

  constructor(
    private getEthnicityService: GetEthnicityService,
    private languageService: LanguageService,
    private downloadService: DownloadService,
    private matDialog: MatDialog,
    private confirmService: ConfirmService,
    private utils: UtilsService,
    private shareService: ShareService,
    private renderer: Renderer2
  ) {}

  ngOnInit(): void {
    this.ethnicity = this.getEthnicityService.GetEthnicityName();
    this.utils.setTitle(`${this.ethnicity}-查詢下載`);
    this.tribeId = this.getEthnicityService.GetEthnicityId();
    this.initializationSearchOptions();
    this.keywordChanged.pipe(debounceTime(500)).subscribe((value: string) => {
      if (value) {
        this.callAutoInputAPI(value);
      } else {
        this.autoInputList = [];
      }
    });
  }

  // 組件銷毀時移除監聽器
  ngOnDestroy() {
    this.documentClickListener?.();
  }

  ngAfterViewInit(): void {
    this.setupDocumentClickListener();
  }

  // 監聽文檔點擊事件
  private documentClickListener: () => void = () => {};

  private setupDocumentClickListener() {
    setTimeout(() => {
      this.documentClickListener = this.renderer.listen(
        'document',
        'click',
        (event: MouseEvent) => {
          // 這裡需要先檢查 searchAll.nativeElement 是否存在
          if (
            this.searchAll?.nativeElement &&
            !this.searchAll.nativeElement.contains(event.target)
          ) {
            this.autoInputList = [];
          }
        }
      );
    });
  }

  // 當搜尋框獲得焦點時重新設置監聽器
  onSearchFocus() {
    this.setupDocumentClickListener();
  }

  onKeywordChange(value: string): void {
    this.keywordChanged.next(value);
  }

  callAutoInputAPI(value: string): void {
    this.shareService.autoSearch(value, this.tribeId).subscribe({
      next: (resp: autoSearchResp) => {
        this.autoInputList = resp.data.candidateWords;
      },
      error: () => {},
    });
  }

  selectValue(value: string) {
    this.keyword = value;
    this.autoInputList = [];
  }

  initializationSearchOptions() {
    forkJoin({
      ethnicityKeyboard: this.getEthnicityKeyboard(),
      advanceSearchData: this.getAdvanceSearch(),
      languageData: this.getEthnicityLanguage(),
    }).subscribe({
      next: (result) => {
        this.dialectList = result.languageData.data.items.map(
          (item: { id: string; name: string }) => ({
            ...item,
            selected: false,
          })
        );
        this.keyboardList = result.ethnicityKeyboard.data.symbolList;
        this.symbolList = result.advanceSearchData.data.symbolItems;
        this.categoryList = result.advanceSearchData.data.categoryItems;
        this.partOfSpeechList = result.advanceSearchData.data.partOfSpeechItems;
        this.restrictSearchList =
          result.advanceSearchData.data.restrictSearchItems;
        this.sourceList = result.advanceSearchData.data.sourceItems.map(
          (item: { id: string; name: string }) => ({
            ...item,
            selected: false,
          })
        );
      },
      error: (err: HttpErrorResponse) => {
        console.error(err);
      },
    });
  }

  getEthnicityKeyboard() {
    return this.languageService.getEthnicityKeyboard(this.tribeId as string);
  }
  getAdvanceSearch() {
    return this.languageService.getAdvanceSearch(this.tribeId as string);
  }
  getEthnicityLanguage() {
    return this.languageService.getEthnicityLanguage(this.tribeId as string);
  }

  search() {
    if (!this.keyword) {
      this.confirmService.showWARN('請輸入關鍵字', '警告');
      return;
    }
    this.isAdvanced = false;
    this.isDialect = false;
    this.nowPage = 1;
    this.showKeyword = this.keyword;
    this.selectList = [];
    this.deleteList = [];
    this.getQueryDownloadList();
  }

  getQueryDownloadList() {
    let req: getQueryDownloadListReq = {
      page: this.nowPage,
      pageSize: this.pageSize,
      keyword: this.keyword,
      tribeDialectId: this.tribeId as string,
      advanceSearch: {
        startSymbolId: this.symbolStart, // 符號起始ID
        endSymbolId: this.symbolEnd, // 符號結束 ID
        categoryId: this.category, // 範疇ID
        partOfSpeechId: this.partOfSpeech,
        dialectId: this.selectedDialect,
        searchRestrict: this.restrictSearch,
        sources: this.selectedSource,
      },
    };
    this.searchReq = req;
    this.downloadService.getQueryDownloadList(req).subscribe({
      next: (resp: getQueryDownloadListResp) => {
        this.queryDownloadList = resp.data.searchData;
        this.totalCount = resp.data.itemTotalCount;
      },
    });
  }

  keyboardUp(value: string, inputElement: HTMLInputElement) {
    const currentKeyword = this.keyword || '';
    const start = inputElement.selectionStart ?? currentKeyword.length;
    const end = inputElement.selectionEnd ?? currentKeyword.length;
    const newKeyword =
      currentKeyword.slice(0, start) + value + currentKeyword.slice(end);
    this.keyword = newKeyword;
    const cursorPos = start + value.length;
    setTimeout(() => {
      inputElement.focus();
      inputElement.setSelectionRange(cursorPos, cursorPos);
    });
  }

  clear() {
    this.keyword = '';
  }

  dialectListChange(item: any) {
    if (item.selected) {
      this.selectedDialect.push(item.id);
    } else {
      this.selectedDialect = this.selectedDialect.filter(
        (selectedDialectId) => {
          return selectedDialectId !== item.id;
        }
      );
    }
  }
  sourceListChange(item: any) {
    if (item.selected) {
      this.selectedSource.push(item.id);
    } else {
      this.selectedSource = this.selectedSource.filter((selectedDialectId) => {
        return selectedDialectId !== item.id;
      });
    }
  }

  isAll(event: Event) {
    const checked = (event.target as HTMLInputElement).checked;
    this.selectList = [];
    this.deleteList = [];
    this.isAllStatus = checked;
  }

  changeSelect(event: Event, id: string) {
    const checked = (event.target as HTMLInputElement).checked;
    if (this.isAllStatus) {
      if (!checked) {
        this.deleteList.push(id);
      } else {
        this.deleteList = this.deleteList.filter((item) => item !== id);
      }
    } else {
      if (checked) {
        this.selectList.push(id);
      } else {
        this.selectList = this.selectList.filter((item) => item !== id);
      }
    }
  }

  checkSelect(id: string) {
    if (this.isAllStatus) {
      if (id) {
        return this.deleteList.includes(id) ? false : true;
      }
      return true;
    } else {
      if (id) {
        return this.selectList.includes(id) ? true : false;
      }
      return false;
    }
  }

  download() {
    if (
      (!this.isAllStatus && this.selectList.length < 1) ||
      (this.isAllStatus && this.deleteList.length === this.totalCount)
    ) {
      this.confirmService.showWARN('請選擇必填欄位。');
      return;
    }
    this.matDialog.open(QueryDownloadDialogComponent, {
      disableClose: true,
      autoFocus: false,
      width: '40vw',
      data: {
        req: this.searchReq,
        isAll: this.isAllStatus,
        selectList: this.selectList,
        deleteList: this.deleteList,
      },
    });
  }
  back(event: Event) {
    event.preventDefault();
    history.back();
  }

  /**
   * 第幾頁
   * @param item number
   */
  getPageFromPaginator(nowPage: number) {
    this.nowPage = nowPage;
    this.getQueryDownloadList();
    window.scrollTo({
      top: 0,
      behavior: 'smooth',
    });
  }
  /**
   * 每頁資料數量
   * @param item number
   */
  getPageSizeFromPaginator(pageSize: number) {
    this.pageSize = pageSize;
    this.nowPage = 1;
    this.getQueryDownloadList();
    window.scrollTo({
      top: 0,
      behavior: 'smooth',
    });
  }
}
