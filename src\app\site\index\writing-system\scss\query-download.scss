.query-download-layout {
    margin: 0;
    // padding: 20px 50px;
    width: 100%;
    box-sizing: border-box;
}
.query-download-title {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    .query-download-item {
        font-size: 1.13em;
    }
}
.input-group {
    display: flex;
    flex-direction: column;
    margin-top: 15px;
    .input-list {
        display: flex;
        flex-direction: row;
        align-items: center;
        .input-img {
            margin: 10px 0;
            min-height: 60px;
        }
        .a-box {
            padding: 18px;
            margin: 10px 0;
            border-radius: 0 5px 5px 0;
            span {
                display: flex;
            }
        }
        .a-voice {
            padding: 18px;
            margin: 10px 0;
        }
    }
    .textarea-list {
        display: contents;
    }
}
.btn-group {
    display: flex;
    flex-direction: row;
    justify-content: center;
}
.query-download-item2 {
    font-size: 1.13em;
    font-weight: bold;
    display: flex;
    align-items: center;
}
.checkbox-group {
    width: 100%;
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
}
.checkbox-group2 {
    width: 100%;
    display: flex;
    .input-list {
        width: calc(100% - 100px);
    }
}
ul {
    padding: 20px 40px 0 0;
    margin: 0;
}
.checkbox-menu {
    display: flex;
    align-items: stretch;
}
.material-symbols-outlined {
    font-variation-settings:
        "FILL" 1,
        "wght" 400,
        "GRAD" 0,
        "opsz" 24;
}
.btn-list {
    margin: 10px 0 10px 10px;
    padding: 14px 30px;
    border-radius: 5px;
    -moz-user-select: none;
    background-image: none;
    border: 1px solid transparent;
    border-radius: 5px;
    cursor: pointer;
    display: inline-block;
    font-weight: 400;
    line-height: 1.2;
    text-align: center;
    font-size: 1em;
    white-space: nowrap;
}

.a-color {
    background: #4a7f42;
    color: #fff;
    &:hover,
    &:focus {
        background: #255d1c;
        // opacity: 0.5;
    }
    &:active,
    &.active {
        background-color: #255d1c;
        border-color: #255d1c;
    }
}
.btn-list2 {
    margin: 10px 0;
    padding: 20px 30px;
    border-radius: 5px;
    -moz-user-select: none;
    background-image: none;
    border: 1px solid transparent;
    border-radius: 5px;
    cursor: pointer;
    display: inline-block;
    font-weight: 400;
    line-height: 1.2;
    text-align: center;
    font-size: 1em;
    white-space: nowrap;
}

.btn-primary-color {
    background: #4a7f42;
    color: #fff;
    &:hover,
    &:focus {
        background: #255d1c;
        // opacity: 0.5;
    }
    &:active,
    &.active {
        background-color: #255d1c;
        border-color: #255d1c;
    }
}
.btn-secondary-color {
    background: #fff;
    color: #4a7f42;
    border: 1px solid #4a7f42;

    &:hover,
    &:focus {
        background: #e7e8e9;
        // opacity: 0.5;
    }
    &:active,
    &.active {
        background-color: #e7e8e9;
        border-color: #4a7f42;
    }
}

//字體顏色
.font_r {
    color: #e41e3f;
}

//字體排版
.text_c {
    text-align: center;
}
.text_l {
    text-align: left;
}
.text_r {
    text-align: right;
}

//表格通用
tr {
    &:nth-child(odd) {
        background-color: #e5efe3 !important;
    }
}

.table_g {
    background-color: #4a7f42;
    color: #fff;
}

@media (max-width: 640px) {
    .input-group {
        .input-list {
            display: flex;
            flex-wrap: wrap;
        }
    }
    .btn-list {
        margin: 10px 0 10px 0;
    }

    .rwd-table03 {
        width: 100%;
        tr,
        th,
        td {
            display: block;
            width: auto !important;
            text-align: center;
            &.th_no {
                display: none;
            }
        }
        tr {
            margin: 0 0 5px;
            border-width: 1px;
            border-style: solid;
            tr {
                margin: 0;
                border: 0;
            }
        }
        td {
            border: 0px !important;
        }
        .rwd-th02 {
            color: #fff;
        }
    }
}
