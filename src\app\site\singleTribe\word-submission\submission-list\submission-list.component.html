<main class="submission-list-layout">
    <div class="input-group">
        <div class="col-3 col-12 gutter-16px">
            <div class="mb-12px w-full relative select-style">
                <select class="select w-full" title="詞項狀態" [(ngModel)]="status">
                    <option [ngValue]="null" disabled selected>請選擇詞項狀態</option>
                    <option [ngValue]="null">不拘</option>
                    <option [ngValue]="2">審查中</option>
                    <option [ngValue]="1">已收錄</option>
                    <option [ngValue]="0">未收錄</option>
                </select>

                <div class="block-select-bg absolute">
                    <div class="row items-stretch">
                        <div class="col">
                            <div class="block-select-bg-rect w-full radius-card"></div>
                        </div>
                        <div class="col-auto shrink-0">
                            <div class="button-dot">
                                <span class="material-symbols-outlined">keyboard_arrow_down</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-3 col-12 gutter-16px">
            <div class="mb-12px w-full relative select-style">
                <select class="select w-full" title="語別" [(ngModel)]="dialectId">
                    <option [ngValue]="null" disabled selected>請選語別</option>
                    @if(dialectList.length>1){
                    <option [ngValue]="null" selected>不拘</option>
                    }
                    @for (option of dialectList; track option) {
                    <option [ngValue]="option.id">{{option.name}}</option>
                    }
                </select>

                <!-- 下拉選單樣式裝飾 -->
                <div class="block-select-bg absolute">
                    <div class="row items-stretch">
                        <div class="col">
                            <div class="block-select-bg-rect w-full radius-card"></div>
                        </div>
                        <div class="col-auto shrink-0">
                            <div class="button-dot">
                                <span class="material-symbols-outlined">keyboard_arrow_down</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>


        <span class="select-item">
            <input type="text" title="keyword" placeholder="請輸入族語/中文" class="form-control" style="width: 100%;"
                [(ngModel)]="keyword">
        </span>
        <input class="btn-list btn-primary-color" value="搜尋" type="button" (click)="search()">

    </div>
    <!--表格1-->
    <table class="table-list-layout rwd-table03">
        <tbody>
            <tr class="bg_g1">
                @for (item of list; track $index) {
                <th class="th_no table_g" [style.width]="item.width">{{item.title}}</th>
                }
            </tr>
            @if(submissionList.length>0){
            @for (item of submissionList;let index=$index; track item) {
            <tr>
                <td class="text_c">
                    <span class="rwd-th">項次</span>
                    {{index+1+(nowPage>1?(nowPage-1)*pageSize:0)}}
                </td>
                <td class="text_c">
                    <span class="rwd-th">語別</span>
                    {{item.dialect}}
                </td>
                <td class="text_l">
                    <span class="rwd-th">族語創詞</span>
                    {{item.dictionaryName}}
                </td>
                <td class="text_l">
                    <span class="rwd-th">華語創詞</span>
                    {{item.chineseExplanation}}
                </td>
                <td class="text_c">
                    <span class="rwd-th">推薦者</span>
                    {{item.creator}}
                </td>
                <td class="text_c">
                    <span class="rwd-th">處理狀態</span>
                    {{item.status}}
                </td>
                <td class="text_c">
                    <span class="rwd-th">推薦數</span>
                    {{item.like}}
                </td>
                <td class="text_c">
                    <span class="rwd-th">處理日期</span>
                    @if(!item.checkTime){
                    -
                    }
                    @else{
                    {{item.checkTime|date:"YYYY.MM.dd"}}
                    }
                </td>
            </tr>
            }
            }@else {
            <tr>
                <td colspan="7" style="text-align: center;">沒有找到符合條件的資料</td>
            </tr>
            }

        </tbody>
    </table>
    @if(submissionList.length>0){
    <app-paginator [pageSize]="pageSize" [totalRecords]="totalCount" [pageShowCount]="pageShowCount" [nowPage]="nowPage"
        currentPageReportTemplate="第 {first} 到 {last} 筆，共 {totalRecords} 筆"
        (clickPageEvent)="getPageFromPaginator($event)"
        (pageSizeChangeEvent)="getPageSizeFromPaginator($event)"></app-paginator>
    }
</main>