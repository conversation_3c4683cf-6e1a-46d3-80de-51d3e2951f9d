import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { SearhModel } from '../../enum/SearhModel .enum';
import { Observable } from 'rxjs';
import {
  getRevisionListReq,
  getRevisionListResp,
  getRevisionResp,
  searchRevisionListReq,
  searchRevisionListResp,
} from '../../interface/revisionList.interface';

@Injectable({
  providedIn: 'root',
})
export class RevisionListService {
  constructor(private httpClient: HttpClient) {}

  /**
   * 精準or模糊搜尋一覽列表
   * @param req
   * @param searhModel
   * @returns
   */
  searchRevisionList(
    req: searchRevisionListReq,
    searhModel: SearhModel
  ): Observable<searchRevisionListResp> {
    let url: string;
    if (searhModel === SearhModel.Accurate) {
      url = 'api/app/dictionary-revision-notice/search';
    } else {
      url = 'api/app/dictionary-revision-notice/fuzzy-search';
    }
    return this.httpClient.post<searchRevisionListResp>(url, req);
  }

  /**
   * 取得修訂一覽表
   * @param id string
   * @returns
   */
  getRevision(id: string, type: number): Observable<getRevisionResp> {
    return this.httpClient.get<getRevisionResp>(
      `api/app/dictionary-revision-notice/dictionary-revision-notice/${id}`,
      {
        params: {
          Type: type,
        },
      }
    );
  }

  //
  getRevisionList(req: getRevisionListReq): Observable<getRevisionListResp> {
    return this.httpClient.post<getRevisionListResp>(
      'api/app/dictionary-revision-notice/search-dictionary-revision-notice-list-for-home',
      req
    );
  }
}
