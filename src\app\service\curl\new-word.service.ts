import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import {
  getNewWordDetailResp,
  getNewWordListReq,
  getNewWordListResp,
  getNewWordYearListResp,
} from '../../interface/newWord.interface';
import { Observable } from 'rxjs';
import { defaultItem } from '../../interface/share.interface';

@Injectable({
  providedIn: 'root',
})
export class NewWordService {
  constructor(private httpClient: HttpClient) {}

  getNewWordList(req: getNewWordListReq): Observable<getNewWordListResp> {
    return this.httpClient.post<getNewWordListResp>(
      'api/app/forum-dictionary-notice/select-notice',
      req
    );
  }

  getNewWordYearList(
    tribeId: string | null
  ): Observable<getNewWordYearListResp> {
    return this.httpClient.post<getNewWordYearListResp>(
      'api/app/forum-dictionary-notice/get-year',
      {
        tribeId: tribeId,
      }
    );
  }

  getNewWordDetail(tribeId: string): Observable<getNewWordDetailResp> {
    return this.httpClient.post<getNewWordDetailResp>(
      'api/app/forum-dictionary-notice/get-detail',
      {
        forumDictionaryNoticeId: tribeId,
      }
    );
  }
}
