.online-learning-layout {
    margin: 0;
    width: 100%;
    box-sizing: border-box;
}
.online-learning-list {
    display: flex;
    flex-direction: column;
    border: 1px solid #65676b;
    border-radius: 5px;
    margin: 20px 0;
    box-sizing: border-box;
}
.online-learning-box {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    flex-wrap: wrap;
    align-items: center;
    padding: 10px 20px;
    background-color: #4a7f42;
}
.online-learning-box2 {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    flex-wrap: nowrap;
    align-items: center;
    padding: 20px 20px;
    background-color: #fff;
    border-radius: 5px;
    img {
        width: 100%;
    }
}
.online-learning-title {
    cursor: pointer;
    display: flex;
    flex-direction: row;
    // justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    .term-list-language {
        font-size: 1.13em;
        color: #65676b;
    }
    .online-learning-h4 {
        font-size: 1.5em;
        font-weight: bold;
        // padding: 0 10px;
    }
}
.online-learning-cont {
    width: 100%;
    display: flex;
    flex-direction: column;
    align-content: space-around;
    align-items: flex-start;
    margin-top: 10px;
    .online-learning-choose {
        width: 100%;
        display: flex;
        flex-direction: row;
        align-items: center;
        border: 1px solid #4a7f42;
        border-radius: 5px;
        margin: 10px 0;
    }
}
.btn-list {
    margin: 5px 5px 5px 0px;
    padding: 14px 30px;
    border-radius: 5px;
    -moz-user-select: none;
    background-image: none;
    border: 1px solid transparent;
    border-radius: 5px;
    cursor: pointer;
    display: inline-block;
    font-weight: 400;
    line-height: 1.2;
    text-align: center;
    font-size: 1rem;
    white-space: nowrap;
}

.btn-primary-table-color {
    background: #4a7f42;
    color: #fff;
    &:hover,
    &:focus {
        background: #255d1c;
        // opacity: 0.5;
    }
    &:active,
    &.active {
        background-color: #255d1c;
        border-color: #255d1c;
    }
}
.btn-choose {
    margin: 5px 5px 5px 0px;
    padding: 14px 30px;
    width: 100%;
    border-radius: 5px;
    -moz-user-select: none;
    background-image: none;
    border: 1px solid transparent;
    border-radius: 5px;
    cursor: pointer;
    display: inline-block;
    font-weight: 400;
    line-height: 1.2;
    text-align: left;
    font-size: 1.13em;
    white-space: nowrap;
    display: flex;
    justify-content: space-between;
}
//選擇答案
.btn-choose-color {
    background: #fff;
    color: #000;
    border: 1px solid #ccc;
    &:hover,
    &:focus {
        background: #255d1c;
        color: #fff;
    }
    &:active,
    &.active {
        background-color: #255d1c;
        border-color: #255d1c;
        color: #fff;
    }
}
//正確答案
.btn-correct-color {
    background: #fff;
    color: #000;
    border: 1px solid #ccc;
    &:hover {
        background: #255d1c;
        color: #fff;
    }
}
//錯誤答案
.btn-mistake-color {
    background: #fff;
    color: #000;
    border: 1px solid #ccc;
    &:hover {
        background: #255d1c;
        color: #fff;
    }
}

.question-error {
    background: #ffd2d0;
    color: #e41e3f;
    border: 1px solid #e41e3f;
}
.question-correct {
    background: #d8eed4;
    color: #4a7f42;
    border: 1px solid #4a7f42;
}
//字體顏色
.font_g {
    color: #4a7f42;
}
.font_b {
    color: #000;
}
.font_w {
    color: #fff;
}

//背景顏色
.bg_0 {
    background-color: #e7e8e9;
}
.bg_g {
    background-color: #4a7f42;
}

//字體排版
.text_c {
    text-align: center;
}
.text_l {
    text-align: left;
}
.text_r {
    text-align: right;
}

//表格通用
tr {
    &:nth-child(odd) {
        background-color: #e5efe3 !important;
    }
}

.table_g {
    background-color: #4a7f42;
    color: #fff;
}

@media (max-width: 640px) {
    .rwd-table03 {
        width: 100%;
        tr,
        th,
        td {
            display: block;
            width: auto !important;
            text-align: center;
            &.th_no {
                display: none;
            }
        }
        tr {
            margin: 0 0 5px;
            border-width: 1px;
            border-style: solid;
            tr {
                margin: 0;
                border: 0;
            }
        }
        td {
            border: 0px !important;
        }
        .rwd-th02 {
            color: #fff;
        }
    }
}
