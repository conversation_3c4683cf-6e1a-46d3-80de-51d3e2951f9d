{"$schema": "./node_modules/@angular/cli/lib/config/schema.json", "version": 1, "newProjectRoot": "projects", "projects": {"ilrdfDictionaryFontStage": {"projectType": "application", "schematics": {"@schematics/angular:component": {"style": "scss", "standalone": true}, "@schematics/angular:directive": {"standalone": true}, "@schematics/angular:pipe": {"standalone": true}}, "root": "", "sourceRoot": "src", "prefix": "app", "architect": {"build": {"builder": "@angular-devkit/build-angular:application", "options": {"outputPath": "dist/ilrdf-dictionary-font-stage", "index": "src/index.html", "browser": "src/main.ts", "polyfills": ["zone.js"], "tsConfig": "tsconfig.app.json", "inlineStyleLanguage": "scss", "assets": [{"glob": "**/*", "input": "public"}], "styles": ["@angular/material/prebuilt-themes/azure-blue.css", "src/styles.scss", "node_modules/froala-editor/css/froala_editor.pkgd.min.css", "node_modules/froala-editor/css/froala_style.min.css", "node_modules/ngx-owl-carousel-o/lib/styles/prebuilt-themes/owl.carousel.min.css", "node_modules/ngx-owl-carousel-o/lib/styles/prebuilt-themes/owl.theme.default.min.css"], "scripts": []}, "configurations": {"production": {"outputPath": "dist/prod", "aot": true, "budgets": [{"type": "initial", "maximumWarning": "400mb", "maximumError": "400mb"}, {"type": "anyComponentStyle", "maximumWarning": "400mb", "maximumError": "400mb"}], "outputHashing": "all", "fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.production.ts"}]}, "staging": {"outputPath": "dist/staging", "aot": true, "budgets": [{"type": "initial", "maximumWarning": "400mb", "maximumError": "400mb"}, {"type": "anyComponentStyle", "maximumWarning": "400mb", "maximumError": "400mb"}], "outputHashing": "all", "fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.staging.ts"}]}, "development": {"optimization": true, "outputPath": "dist/dev", "extractLicenses": false, "outputHashing": "all", "sourceMap": true, "aot": true, "fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.development.ts"}]}}, "defaultConfiguration": "development"}, "serve": {"builder": "@angular-devkit/build-angular:dev-server", "configurations": {"production": {"buildTarget": "ilrdfDictionaryFontStage:build:production"}, "development": {"buildTarget": "ilrdfDictionaryFontStage:build:development"}}, "defaultConfiguration": "development"}, "extract-i18n": {"builder": "@angular-devkit/build-angular:extract-i18n"}, "test": {"builder": "@angular-devkit/build-angular:karma", "options": {"polyfills": ["zone.js", "zone.js/testing"], "tsConfig": "tsconfig.spec.json", "inlineStyleLanguage": "scss", "assets": [{"glob": "**/*", "input": "public"}], "styles": ["@angular/material/prebuilt-themes/azure-blue.css", "src/styles.scss"], "scripts": []}}}}}, "cli": {"analytics": "b9dc86f4-224c-4590-b73a-1605caaa3d96"}}