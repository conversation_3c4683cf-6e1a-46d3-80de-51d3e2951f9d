import { Component, OnInit } from '@angular/core';
import { SubscribeService } from '../../../service/curl/subscribe.service';
import { FormBuilder, FormGroup, Validators, FormsModule, ReactiveFormsModule } from '@angular/forms';
import { ConfirmService } from '../../../service/utils/confirm.service';
import {
  subscribeReq,
  subscribeResp,
} from '../../../interface/subscribe.interface';
import { apiStatus } from '../../../enum/apiStatus.enum';
import { SpinnerService } from '../../../service/utils/spinner.service';
import { HttpErrorResponse } from '@angular/common/http';
import { ActivatedRoute, RouterLink } from '@angular/router';
import { UtilsService } from '../../../service/utils/utils.service';

@Component({
    selector: 'app-subscribe',
    templateUrl: './subscribe.component.html',
    styleUrl: './subscribe.component.scss',
    imports: [
        RouterLink,
        FormsModule,
        ReactiveFormsModule,
    ],
})
export class SubscribeComponent implements OnInit {
  form: FormGroup;
  email: string = '';
  unSubscribe: string = '';

  constructor(
    private subscribeService: SubscribeService,
    private formBuilder: FormBuilder,
    private confirmService: ConfirmService,
    private spinnerService: SpinnerService,
    private activatedRoute: ActivatedRoute,
    private utils: UtilsService
  ) {
    this.activatedRoute.queryParamMap.subscribe((queryParams) => {
      this.unSubscribe = queryParams.get('unSubscribe') as string;
    });

    this.form = this.formBuilder.group({
      email: ['', Validators.required],
    });
  }

  ngOnInit(): void {
    this.utils.setTitle('訂閱我們');
  }

  check() {
    this.confirmService
      .showSUCCESS('是否要取消訂閱?')
      .afterClosed()
      .subscribe((isSubscribe) => {
        if (isSubscribe) {
          this.spinnerService.show();
          let req: subscribeReq = {
            email: this.unSubscribe,
          };
          this.subscribeService.unSubscribe(req).subscribe({
            next: (resp: subscribeResp) => {
              this.spinnerService.hide();
              if (resp.status === apiStatus.SUCCESS) {
                this.confirmService.showSUCCESS(resp.message);
              } else {
                this.confirmService.showError(resp.message);
              }
            },
            error: (err: HttpErrorResponse) => {
              this.spinnerService.hide();
              this.confirmService.showError('取消訂閱失敗');
            },
          });
        }
      });
  }

  subscribe() {
    if (!this.form.valid) {
      this.confirmService.showWARN('Email尚未填寫');
      return;
    }
    let req: subscribeReq = {
      email: this.form.value.email,
    };
    this.confirmService
      .showSUCCESS('是否要訂閱?')
      .afterClosed()
      .subscribe((isSubscribe) => {
        if (isSubscribe) {
          this.spinnerService.show();
          this.subscribeService.subscribe(req).subscribe({
            next: (resp: subscribeResp) => {
              this.spinnerService.hide();
              if (resp.status === apiStatus.SUCCESS) {
                this.confirmService.showSUCCESS(resp.message);
              } else {
                this.confirmService.showError(resp.message);
              }
            },
            error: (err: HttpErrorResponse) => {
              this.spinnerService.hide();
              this.confirmService.showError('訂閱失敗');
            },
          });
        }
      });
  }

  back(event: Event) {
    event.preventDefault();
    history.back();
  }
}
