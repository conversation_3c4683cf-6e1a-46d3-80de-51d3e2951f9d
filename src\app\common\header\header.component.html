<!--頁首-->
<header class="master-header-layout">
	<a class="accesskey" id="U" accesskey="U" [routerLink]="[]" fragment="U" title="上方功能區塊">:::</a>
	<a href="#main" id="gotocenter" title="跳到主要內容" class="sr-only">跳到主要內容</a>
	<!--LOGO-->
	<h1 class="logo-list">
		<a href="" (click)="closeMenu();$event.preventDefault()" [routerLink]="'/home'">
			<img src="image/logo.svg" alt="LOGO">
		</a>
	</h1>
	<!--漢堡選單-->
	<input type="checkbox" id="burger-menu">
	<label for="burger-menu" class="burger-menu-list-icon" aria-label="開啟主選單">
		<span style="display: none;">漢堡選單</span>
		<div class="burger-menu-icon"></div>
	</label>
	<!--主內容區-->
	<div class="master-header-cont-layout">
		<!--右上方快速選單-->
		<div class="master-top-info">
			<!--上方功能列-->
			<ul class="master-top-info-cont">
				<!--文字大中小-->
				<li class="master-top-info-item">
					<div class="font-Change-lsit">
						<!--標題
					<p class="font-Change-title">字級</p>-->
						<!--內容-->
						<ul class="font-Change-about">
							<!-- <li class="font-Change-no">
								<span class="material-symbols-outlined material-style font_g text_l">graph_2</span>
								<a href="/" class="a-link">網站導覽</a>
							</li> -->
							<li class="font-Change-no">
								<span class="material-symbols-outlined">local_library</span>
								<a class="a-link" [routerLink]="'/about'" [queryParams]="{'id':aboutId}"
									(click)="closeMenu()">關於本站</a>
							</li>
							<!-- <li class="font-Change-no">
								<span class="material-symbols-outlined">download</span>
								<a class="a-link">操作手冊下載</a>
							</li> -->
						</ul>
						<ul class="font-Change-cont">
							<li class="font-Change-item" [ngClass]="{'active':fontSize===FontSize.big}">
								<a href="" (click)="changeFontsize($event,FontSize.big);closeMenu()"
									class="font_l">大</a>
							</li>
							<li class="font-Change-item active" [ngClass]="{'active':fontSize===FontSize.middle}">
								<a href="" (click)="changeFontsize($event,FontSize.middle);closeMenu()"
									class="font_m">中</a>
							</li>
							<li class="font-Change-item" [ngClass]="{'active':fontSize===FontSize.small}">
								<a href="" (click)="changeFontsize($event,FontSize.small);closeMenu()"
									class="font_s">小</a>
							</li>
						</ul>
					</div>
				</li>
			</ul>
		</div>
		<!--主選單1-->
		<div class="master-header-menu-layout">
			<!--主選單-->
			<!--選單靠左：master-menu-left，
				選單置中：master-menu-center，
				選單靠右：master-menu-right,
			CSS放置位置再<nav class="master-menu-layout --選單位置CSS--">-->
			<!--左右鍵預設隱藏，選單項目超過再出現就可以了-->
			<nav class="master-menu-layout master-menu-right">
				<!--向左-->
				<div class="master-menu-icon">
					<a href="#">
						<span class="material-symbols-outlined">chevron_left</span>
					</a>
				</div>
				<ul class="master-menu-cont">
					<!--01-->
					<li class="master-menu-cont-item">
						<a class="master-menu-drawer-item " [routerLink]="'/home'" routerLinkActive="active"
							(click)="closeMenu()">首頁</a>
					</li>
					<li class="master-menu-cont-item">
						<a class="master-menu-drawer-item " [routerLink]="'/news'" routerLinkActive="active"
							(click)="closeMenu()">最新消息
						</a>
					</li>
					<li class="master-menu-cont-item">
						<a class="master-menu-drawer-item " [routerLink]="'/link'" routerLinkActive="active"
							(click)="closeMenu()">相關連結</a>
					</li>
					<li class="master-menu-cont-item">
						<a class="master-menu-drawer-item" [routerLink]="'/question'" routerLinkActive="active"
							(click)="closeMenu()">意見回饋</a>
						<div class="master-dropdown-menu-layout">
							<ul class="master-dropdown-menu-cont">
								<li>
									<a class="master-dropdown-menu-item" [routerLink]="'/question/list'"
										routerLinkActive="active" (click)="closeMenu()">詞項回饋</a>
								</li>
								<li>
									<a class="master-dropdown-menu-item" [routerLink]="'/question/sysList'"
										routerLinkActive="active" (click)="closeMenu()">系統回饋</a>
								</li>
							</ul>
						</div>
					</li>
					<li class="master-menu-cont-item">
						<a class="master-menu-drawer-item " [routerLink]="'/subscribe'" routerLinkActive="active"
							(click)="closeMenu()">訂閱我們</a>
					</li>
					<li class="master-menu-cont-item">
						<a class="master-menu-drawer-item " [routerLink]="'/termRelease'" routerLinkActive="active"
							(click)="closeMenu()">公告新詞</a>
					</li>
					<li class="master-menu-cont-item">
						<a class="master-menu-drawer-item" [routerLink]="'/revisionNotice'" routerLinkActive="active"
							(click)="closeMenu()">修訂公告</a>
						<div class="master-dropdown-menu-layout">
							<ul class="master-dropdown-menu-cont">
								<li>
									<a class="master-dropdown-menu-item" [routerLink]="'/revisionNotice/revisionList'"
										[queryParams]="{'tab':1}" routerLinkActive="active"
										(click)="closeMenu()">修訂一覽表</a>
								</li>
								<li>
									<a class="master-dropdown-menu-item" [routerLink]="'/revisionNotice/writingSystem'"
										[queryParams]="{'tab':1}" routerLinkActive="active"
										(click)="closeMenu()">書寫系統修訂</a>
								</li>
							</ul>
						</div>
					</li>
				</ul>
				<!--向右-->
				<div class="master-menu-icon">
					<a href="#">
						<span class="material-symbols-outlined">chevron_right</span>
					</a>
				</div>
			</nav>
		</div>
		@if(tribeName&&tribeId){
		<div class="master-header-menu-layout">
			<!--主選單2-->
			<div class="language-item">
				<img [src]="'image/'+tribeId+'.png'" alt="LOGO" style="width: 50px;">
				<div class="language-title">{{tribeName}}</div>
			</div>
			<nav class="master-menu-layout2 master-menu-right">
				<ul class="master-menu-cont">

					<li class="master-menu-cont-item" routerLinkActive="active">
						<a href="" (click)="$event.preventDefault()" class="master-menu-drawer-item">簡介</a>
						<div class="master-dropdown-menu-layout">
							<ul class="master-dropdown-menu-cont">
								@for (item of introductionList; track item) {
								<li>
									<a class="master-dropdown-menu-item" [routerLink]="'/introduction'"
										[queryParams]="{'tribeId':tribeId,'tribeName':tribeName,'id':item.id}"
										routerLinkActive="active" (click)="closeMenu()">{{item.title}}</a>
								</li>
								}

							</ul>
						</div>
					</li>
					<li class="master-menu-cont-item" routerLinkActive="active">
						<a href="" (click)="$event.preventDefault()" class="master-menu-drawer-item">辭典查詢</a>
						<div class="master-dropdown-menu-layout">
							<ul class="master-dropdown-menu-cont">
								<li>
									<a class="master-dropdown-menu-item" [routerLink]="'/singleSearch'"
										[queryParams]="{'tribeId':tribeId,'tribeName':tribeName}"
										routerLinkActive="active" (click)="closeMenu()">檢索</a>
								</li>
								<li>
									<a class="master-dropdown-menu-item" [routerLink]="'/wordList'"
										[queryParams]="{'tribeId':tribeId,'tribeName':tribeName}"
										routerLinkActive="active" (click)="closeMenu()">詞項列表</a>
								</li>
								<li>
									<a class="master-dropdown-menu-item" [routerLink]="'/wordComment'"
										[queryParams]="{'tribeId':tribeId,'tribeName':tribeName}"
										routerLinkActive="active" (click)="closeMenu()">逐詞註解</a>
								</li>
								<li style="display: none;">
									<a class="master-dropdown-menu-item" [routerLink]="'/singleQuestion'"
										[queryParams]="{'tribeId':tribeId,'tribeName':tribeName}"
										routerLinkActive="active" (click)="closeMenu()">單筆詞項</a>
								</li>
							</ul>
						</div>
					</li>
					<!--02-->
					<li class="master-menu-cont-item" routerLinkActive="active">
						<a href="" (click)="$event.preventDefault()" class="master-menu-drawer-item">線上學習</a>
						<div class="master-dropdown-menu-layout">
							<ul class="master-dropdown-menu-cont">
								<li>
									<a class="master-dropdown-menu-item" [routerLink]="'/onlineLearning'"
										[queryParams]="{'tribeId':tribeId,'tribeName':tribeName}"
										routerLinkActive="active" (click)="closeMenu()">線上學習</a>
								</li>
								<li>
									<a class="master-dropdown-menu-item" [routerLink]="'/onlineTest'"
										[queryParams]="{'tribeId':tribeId,'tribeName':tribeName}"
										routerLinkActive="active" (click)="closeMenu()">線上測驗</a>
								</li>
							</ul>
						</div>
					</li>
					<li class="master-menu-cont-item">
						<a class="master-menu-drawer-item" [routerLink]="'/singleWordRec'"
							[queryParams]="{'tribeId':tribeId,'tribeName':tribeName}" routerLinkActive="active"
							(click)="closeMenu()">單詞推薦</a>
					</li>
					<li class="master-menu-cont-item">
						<a class="master-menu-drawer-item" [routerLink]="'/wordSubmission'"
							[queryParams]="{'tribeId':tribeId,'tribeName':tribeName}" routerLinkActive="active"
							(click)="closeMenu()">創詞投稿</a>
					</li>
					<li class="master-menu-cont-item">
						<a class="master-menu-drawer-item" [routerLink]="'/newWord'"
							[queryParams]="{'tribeId':tribeId,'tribeName':tribeName}" routerLinkActive="active"
							(click)="closeMenu()">公告新詞</a>
					</li>
					<li class="master-menu-cont-item">
						<a href="" (click)="$event.preventDefault()" class="master-menu-drawer-item"
							routerLinkActive="active">下載專區</a>
						<div class="master-dropdown-menu-layout">
							<ul class="master-dropdown-menu-cont">
								<li>
									<a class="master-dropdown-menu-item" [routerLink]="'/queryDownload'"
										[queryParams]="{'tribeId':tribeId,'tribeName':tribeName}"
										routerLinkActive="active" (click)="closeMenu()">查詢下載</a>
								</li>
								<li>
									<a class="master-dropdown-menu-item" [routerLink]="'/cardDownload'"
										[queryParams]="{'tribeId':tribeId,'tribeName':tribeName}"
										routerLinkActive="active" (click)="closeMenu()">圖卡下載</a>
								</li>
								<li>
									<a class="master-dropdown-menu-item" [routerLink]="'/textDownload'"
										[queryParams]="{'tribeId':tribeId,'tribeName':tribeName}"
										routerLinkActive="active" (click)="closeMenu()">全文下載</a>
								</li>
							</ul>
						</div>
					</li>
				</ul>
			</nav>
		</div>
		}
	</div>
</header>