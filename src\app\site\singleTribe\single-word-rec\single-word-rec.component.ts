import { AfterViewInit, Component, OnInit, ViewChild } from '@angular/core';
import { MatTabGroup, MatTab } from '@angular/material/tabs';
import { GetEthnicityService } from '../../../service/utils/get-ethnicity.service';
import { ActivatedRoute, RouterLink } from '@angular/router';
import { UtilsService } from '../../../service/utils/utils.service';
import { RecInfoComponent } from './rec-info/rec-info.component';
import { NgIf } from '@angular/common';
import { MyRecComponent } from './my-rec/my-rec.component';
import { RecListComponent } from './rec-list/rec-list.component';

@Component({
    selector: 'app-single-word-rec',
    templateUrl: './single-word-rec.component.html',
    styleUrl: './single-word-rec.component.scss',
    imports: [
        RouterLink,
        MatTabGroup,
        MatTab,
        RecInfoComponent,
        NgIf,
        MyR<PERSON>Component,
        RecListComponent,
    ],
})
export class SingleWordRecComponent implements OnInit, AfterViewInit {
  @ViewChild('tabGroup') tabGroup!: MatTabGroup;
  ethnicity: string | null = null;

  constructor(
    private getEthnicityService: GetEthnicityService,
    private activatedRoute: ActivatedRoute,
    private utils:UtilsService
  ) {}

  ngOnInit(): void {
    this.ethnicity = this.getEthnicityService.GetEthnicityName();
    this.utils.setTitle(`${this.ethnicity}-單詞推薦`)
  }

  ngAfterViewInit(): void {
    this.activatedRoute.queryParamMap.subscribe((queryParamMap) => {
      if (queryParamMap.get('tab')) {
        this.tabGroup.selectedIndex = parseInt(
          queryParamMap.get('tab') as string
        );
      }
    });
  }

  back(event:Event) {
    event.preventDefault();
    history.back();
  }
}
