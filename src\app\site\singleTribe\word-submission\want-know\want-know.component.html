<main class="want-know-layout">
    <div class="want-know-cont">
        <p>族語也要跟上時代，為了提升族語符合現代生活的使用，您可以用中文提出想知道但又不會的族語新詞，讓熱愛族語的朋友們，一起透過線上集思廣益，為族語創造更具前瞻性的未來！</p>
    </div>
    <form [formGroup]="form">
        <div class="input-group">
            <span class="want-know-item">
                <span class="font_r">*</span>族語創詞中文詞彙
            </span>
            <div class="input-list">
                <input type="text" title="content" placeholder="請輸入您想要知道的族語創詞中文詞彙，例：銀行" class="form-control"
                    style="width: 100%;" formControlName="content">
            </div>
        </div>
        <div class="input-group">
            <span class="want-know-item">
                <span class="font_r">*</span>投稿者
            </span>
            <div class="input-list">
                <input type="text" title="name" placeholder="請輸入姓名" class="form-control" style="width: 100%;"
                    formControlName="name">
            </div>
        </div>
        <div class="input-group">
            <span class="want-know-item">
                <span class="font_r">*</span>Email(前端不顯示)
            </span>
            <div class="input-list">
                <input type="text" title="email" placeholder="請輸入Email" class="form-control" style="width: 100%;"
                    formControlName="email">
            </div>
        </div>
    </form>
    <div class="input-list">
        <input class="btn-list btn-primary-color" value="匿名送出" type="button" (click)="sendAnonymously()">
        <!-- <input class="btn-list btn-primary-color" value="Line登入並送出" type="button" (click)="login()"> -->
        <input class="btn-list btn-primary-color" value="送出" type="button" (click)="send()">
    </div>
    <div class="input-group">
        <span class="want-know-item">
            這些詞尚未有人會用族語講
        </span>
    </div>
    <div class="input-list">
        @for (item of wantToKnowList; track item) {
        <input class="btn-list2 btn-green-color" [value]="item" (click)="wantKnow(item)" type="button">

        }
    </div>
</main>