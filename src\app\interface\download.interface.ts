import { defaultItem } from './share.interface';

export interface getImageDownloadClassListResp extends defaultItem {
  data: imageClassItem[];
}
export interface imageClassItem {
  imageClassId: string;
  imageClassName: string;
}

export interface getImageDownloadListReq {
  tribeId: string;
  imageClassId: string;
  page: number;
  pageSize: number;
}
export interface getImageDownloadListResp extends defaultItem {
  data: {
    searchData: imageDownloadItem[];
    itemTotalCount: number;
  };
}

export interface imageDownloadItem {
  dictionaryId: string;
  imageUrl: string;
  word: string;
}

export interface downloadImageReq {
  dictionaryIds: string[];
  tribeId: string;
  imageClassId: string;
  fileType: number;
  isAll: boolean;
  deleteDictionaryIds: string[];
}

export interface downloadTextFileResp extends defaultItem {
  data: {
    fullTextItems: fullTextItem[];
  };
}

export interface fullTextItem {
  id: string;
  name: string; // 全文下載標題
  fileItems: {
    id: string;
    url: string; // 檔案 URL
    fileType: string; // ODT || PDF ||-
  }[];
  lastModificationText: string; // 更新版本(X年X月)
  lastModificationTime: string; // 更新時間
}

export interface getQueryDownloadListReq {
  page: number;
  pageSize: number;
  keyword: string;
  tribeDialectId: string;
  advanceSearch: {
    startSymbolId?: string | null; // 符號起始ID
    endSymbolId?: string | null; // 符號結束 ID
    categoryId?: string | null; // 範疇ID
    partOfSpeechId?: string | null;
    dialectId: string[];
    searchRestrict?: number;
    sources?: string[];
  };
}
export interface getQueryDownloadListResp extends defaultItem {
  data: {
    searchData: queryDownloadItem[];
    itemTotalCount: number;
  };
}

export interface queryDownloadItem {
  dictionaryId: string;
  word: string;
  chineseExplanation: string[];
  frequency: number;
}

export interface downloadQueryFileReq {
  dictionaryIds: string[]; //選擇
  fileType: number; //:1.PDF, 2.ODT
  selectedContent: number[]; //  1.詞頻等級, 2.變體, 3.詞類, 4.焦點, 5.例句, 6.範疇分類, 7.構詞, 8.相關焦點, 9.備註, 10.相關詞, 11.圖片, 12.書寫系統異動
  format: number; //1.依詞根、衍生詞排序, 2.依詞項符號順序排序, 3.依華語筆畫順序排序,
  derivedDetail: {
    showDerivedDetail: boolean; //顯示衍生詞細部資料
  };
  symbolSortDetail: {
    column: number; //1.單欄, 2.雙欄,
    showWordRoot: boolean; //顯示詞根/詞幹
  };
  fontSize: number; //1.小, 2.中, 3.大,
  fontType: number; // 1.Time New Roman, 2.Arial, 3.Ms Mincho
  isAll: boolean;
  deleteDictionaryIds: string[]; //全選刪除
  dictionarySearchRequest: Omit<getQueryDownloadListReq, 'page' | 'pageSize'>;
  verifyCode: string;
  sessionId: string;
}
