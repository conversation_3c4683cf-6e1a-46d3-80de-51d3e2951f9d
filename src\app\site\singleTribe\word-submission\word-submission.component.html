<main class="master-pages-container-layout">
    <div class="master-pages-container-cont">
        <div class="cont pages-cont-layout">
            <div class="pages-cont-list-layout">
                <!--路徑列-->
                <div class="breadcrumb-layout">
                    <div>
                        <nav class="breadcrumb-cont">
                            <ol class="breadcrumb">
                                <li class="breadcrumb-item">
                                    <a href="" (click)="$event.defaultPrevented" [routerLink]="'/home'">
                                        <span>首頁</span>
                                        <span class="material-symbols-outlined">keyboard_arrow_right</span>
                                    </a>
                                </li>
                                <li class="breadcrumb-item">
                                    <a href="" (click)="$event.defaultPrevented">
                                        <span>{{ethnicity}}</span>
                                        <span class="material-symbols-outlined">keyboard_arrow_right</span>
                                    </a>
                                </li>
                                <li class="breadcrumb-item active">創詞投稿</li>
                            </ol>
                        </nav>
                    </div>
                    <div>
                        <nav class="breadcrumb-cont">
                            <ol class="breadcrumb">
                                <li class="breadcrumb-item">
                                    <a hre="" (click)="back($event)">
                                        <span>&lt;&lt;回上一頁</span>
                                    </a>
                                </li>
                            </ol>
                        </nav>
                    </div>
                </div>
                <div style="width: 100%;">
                    <mat-tab-group #tabGroup (selectedIndexChange)="onTabChange($event)">
                        <mat-tab label="辦法說明">
                            <app-method-description [tabGroup]="tabGroup"></app-method-description> </mat-tab>
                        <mat-tab label="我要投稿">
                            <app-want-contribute *ngIf="tabGroup.selectedIndex === 1" [tabGroup]="tabGroup"
                                [wantKnowValue]="wantKnowValue"></app-want-contribute> </mat-tab>
                        <mat-tab label="我想知道">
                            <app-want-know *ngIf="tabGroup.selectedIndex === 2" [tabGroup]="tabGroup"
                                (wantKnowValue)="wantKnow($event)"></app-want-know>
                        </mat-tab>
                        <mat-tab label="投稿列表">
                            <app-submission-list *ngIf="tabGroup.selectedIndex === 3"></app-submission-list> </mat-tab>
                    </mat-tab-group>
                </div>
            </div>
        </div>
    </div>
</main>