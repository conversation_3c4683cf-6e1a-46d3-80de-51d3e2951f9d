@use "./scss/announcement-new-words.scss";
@use "../../../../../public/scss/selectHama";

.breadcrumb-layout {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
}

.breadcrumb-item {
    cursor: pointer;
}

.play {
    cursor: pointer;
    color: #4a7f42;
}

.play-disabled {
    color: gray;
}

select,
input {
    width: 100%;
}
.input-list {
    span {
        display: block;
        width: 100%;
    }

    li {
        width: calc(100% / 4);
    }

    input[type="button"] {
        width: auto;
    }

    @media (max-width: 650px) {
        li {
            width: 100%;
        }

        .btn-box {
            margin: 5px;
            flex-direction: row !important;

            input[type="button"] {
                margin-right: 10px;
            }
        }
    }
}


.form-control {
    border-top-width: 1px !important;
    margin-top: 0px !important;
}