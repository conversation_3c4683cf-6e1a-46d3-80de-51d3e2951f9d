.breadcrumb-layout {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
}

.breadcrumb-item {
    cursor: pointer;
}

.title {
    font-size: 1.8em;
    font-weight: bold;
    background-color: #4a7f42;
    color: white;
    margin: 1em 0;
    padding: 0.5em;
}
span {
    font-size: 1.2em;
}

.question-group {
    display: flex;
    flex-direction: column;
    justify-content: center;
    border: solid gray;
    margin: 1em 0;
    padding: 4em;
    .question-dec {
        display: flex;
        flex-direction: column;
        label {
            font-size: 1.4em;
            font-weight: bold;
        }
    }
    .question-input-box {
        span {
            span {
                color: red;
            }
            font-size: 1.2em;
        }
        display: flex;
        flex-direction: column;
    }
}
