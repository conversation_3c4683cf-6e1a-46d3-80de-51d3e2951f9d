.card-download-layout{
    margin: 0;
    padding:  20px 50px;
    width: 100%;
    box-sizing: border-box;
}
.input-group{
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
    margin-top: 15px;
    width: 100%;
    ul{
        width: 100%;
        display: flex;
        flex-direction: row;
        flex-wrap: wrap;
        justify-content: space-between;
        padding: 0;
        margin: 0;
        font-size: 1.13em;
        // .input-list{
        //     width: 45%;
        // }
        li{
            list-style-type: none;
            display: flex;
            .select-item{
                padding: 0 10px;
                select{
                    width: 100%;
                }
            }
            .card-download-item{
                font-size: 1.13em;
                display: flex;
                align-items: center;
                // min-width: 82px;
                justify-content: flex-end;
                select{
                    width: 100%;
                }
            }
        }
    }
    .textarea-list{
        display: contents;
    }
}
.input-group2{
    display: flex;
    flex-direction: column;
    margin-top: 15px;
    ul{
        padding: 20px 40px 0 0;
        margin: 0;
        font-size: 1.13em;
    }
}
.btn-group{
    display: flex;
    flex-direction: row;
    justify-content: center;
}
.card-download-item2{
    font-size: 1.13em;
    font-weight: bold;
    display: flex;
    align-items: center;
}
.checkbox-group{
    width: 100%;
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
}
.checkbox-group2{
    width: 100%;
    display: flex;
    .input-list{
        width: calc(100% - 100px);
    }
}
.checkbox-menu{
    display: flex;
    align-items: stretch;
}
.material-symbols-outlined {
    font-variation-settings:
    'FILL' 1,
    'wght' 400,
    'GRAD' 0,
    'opsz' 24
}
.btn-list{
	margin: 10px 0 10px 10px;
	padding: 14px 30px;
	border-radius: 5px;
	-moz-user-select: none;
	background-image: none;
	border: 1px solid transparent;
	border-radius: 5px;
	cursor: pointer;
	display: inline-block;
	font-weight: 400;
	line-height: 1.2;
	text-align: center;
	font-size: 1.0em;
	white-space: nowrap;
}
a{
    text-decoration: none;
    display: block !important;
}
.btn-list2{
	margin: 10px 0 ;
	padding: 20px 30px;
	border-radius: 5px;
	-moz-user-select: none;
	background-image: none;
	border: 1px solid transparent;
	border-radius: 5px;
	cursor: pointer;
	display: inline-block;
	font-weight: 400;
	line-height: 1.2;
	text-align: center;
	font-size: 1.0em;
	white-space: nowrap;
}

.btn-primary-color{
	background: #4A7F42;
	color:#fff;
    &:hover, &:focus{
		background: #255d1c;
		// opacity: 0.5;
    }
	&:active, &.active{
		background-color: #255d1c;
		border-color: #255d1c;
    }
}
.btn-secondary-color{
	background: #fff;
	color:#4A7F42;
    border: 1px solid #4A7F42;
    
    &:hover, &:focus{
		background: #E7E8E9;
		// opacity: 0.5;
    }
	&:active, &.active{
		background-color: #E7E8E9;
		border-color: #4A7F42;
    }
}

//字體顏色
.font_r{
    color: #E41E3F;
}

//字體排版
.text_c{
    text-align: center;
}
.text_l{
    text-align: left;
}
.text_r{
    text-align: right;
}

//表格通用
tr {
    &:nth-child(odd) {
      background-color: #e5efe3 !important;
    }
  }

.table_g{
    background-color: #4A7F42;
    color: #fff;
}

@media (max-width: 640px) {
    .input-group{
        ul{
            flex-direction: column;
            li{
                display: flex;
                flex-direction: column;
                align-items: flex-start;
                .select-item{
                    padding: 0;
                }
            }
        }
    }
    .btn-list{
        margin: 10px 0 10px 0;
    }
	.rwd-table03 {
		width: 100%;
		tr,
		th,
		td {
			display: block;
			width: auto !important;
			text-align: center;
			&.th_no {
				display: none;
			}
		}
		tr {
			margin: 0 0 5px;
			border-width: 1px;
			border-style: solid;
			tr {
				margin: 0;
				border: 0;
			}
		}
		td {
			border: 0px !important;
		}
        .rwd-th02{
            color: #fff;
        }
	}
}