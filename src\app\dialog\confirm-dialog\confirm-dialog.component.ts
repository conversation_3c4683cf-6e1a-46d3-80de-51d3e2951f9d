import { Component, Inject } from '@angular/core';
import { MAT_DIALOG_DATA, MatDialogRef, MatDialogTitle, MatDialogContent, MatDialogActions, MatDialogClose } from '@angular/material/dialog';
import { DialogType } from '../../enum/dialogType.enum';
import { MatIcon } from '@angular/material/icon';
import { CdkScrollable } from '@angular/cdk/scrolling';

@Component({
    selector: 'app-confirm-dialog',
    templateUrl: './confirm-dialog.component.html',
    styleUrl: './confirm-dialog.component.scss',
    imports: [MatDialogTitle, MatIcon, CdkScrollable, MatDialogContent, MatDialogActions, MatDialogClose]
})
export class ConfirmDialogComponent {
  dialogType = DialogType;

  constructor(
    @Inject(MAT_DIALOG_DATA)
    public data: {
      dialogType: string;
      statusMsg?: string;
      mainMsg?: string;
      groupMessage?: string[];
    },
    private dialogRef: MatDialogRef<ConfirmDialogComponent>
  ) {}

  close() {
    this.dialogRef.close(false);
  }
}

//confirm呼叫用法
// this.matDialog.open(ConfirmDialogComponent, {
//   width: '20vw',
//   autoFocus: false,
//   data: {
//     dialogType: DialogType.WARN, statusMsg: '成功', mainMsg: '上傳成功'
//   },
// })
