<main class="master-pages-container-layout">
    <div class="master-pages-container-cont">
        <div class="cont pages-cont-layout">
            <div class="pages-cont-list-layout">

                <!--路徑列-->
                <div class="breadcrumb-layout">
                    <div>
                        <nav class="breadcrumb-cont">
                            <ol class="breadcrumb">
                                <li class="breadcrumb-item">
                                    <a href="" (click)="$event.defaultPrevented" [routerLink]="'/home'">
                                        <span>首頁</span>
                                        <span class="material-symbols-outlined">keyboard_arrow_right</span>
                                    </a>
                                </li>
                                <li class="breadcrumb-item">
                                    <a href="" (click)="$event.defaultPrevented">
                                        <span>{{ethnicity}}</span>
                                        <span class="material-symbols-outlined">keyboard_arrow_right</span>
                                    </a>
                                </li>
                                <li class="breadcrumb-item active">圖卡下載</li>
                            </ol>
                        </nav>
                    </div>
                    <div>
                        <nav class="breadcrumb-cont">
                            <ol class="breadcrumb">
                                <li class="breadcrumb-item">
                                    <a hre="" (click)="back($event)">
                                        <span>&lt;&lt;回上一頁</span>
                                    </a>
                                </li>
                            </ol>
                        </nav>
                    </div>
                </div>
                <div style="width: 100%;">
                    <main class="card-download-layout">
                        <div class="input-group">
                            <ul class="input-list">
                                <form [formGroup]="form">
                                    <li>

                                        <div class="col-lg col-12 gutter-16px">
                                            <div class="mb-12px w-full relative select-style">
                                                <select class="select w-full" title="圖片分類" formControlName="imageClass">
                                                    @for ( item of imageClassList; let index=$index; track item) {
                                                    <option [ngValue]="item.imageClassId" [selected]="index===0">
                                                        {{item.imageClassName}}</option>
                                                    }
                                                </select>

                                                <!-- 下拉選單樣式裝飾 -->
                                                <div class="block-select-bg absolute">
                                                    <div class="row items-stretch">
                                                        <div class="col">
                                                            <div class="block-select-bg-rect w-full radius-card"></div>
                                                        </div>
                                                        <div class="col-auto shrink-0">
                                                            <div class="button-dot">
                                                                <span
                                                                    class="material-symbols-outlined">keyboard_arrow_down</span>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>


                                        <input class="btn-list btn-primary-color" value="搜尋" type="button"
                                            (click)="search()">
                                    </li>
                                </form>
                                <li class="input-list">
                                    <input class="btn-list btn-primary-color" value="下載" type="button"
                                        (click)="download()">
                                </li>
                            </ul>
                        </div>
                        <!--表格1-->
                        <table class="table-list-layout rwd-table03">
                            <tbody>
                                <tr class="bg_g1">
                                    <th class="th_no table_g" style="width: 5%;"><label><input class="checkbox-list"
                                                type="checkbox" (change)="isAll($event)">全選</label></th>
                                    <th class="th_no table_g" style="width: 20%;">詞項</th>
                                    <th class="th_no table_g">圖卡</th>
                                    <th class="th_no table_g" style="width: 5%;"></th>
                                    <th class="th_no table_g" style="width: 20%;">詞項</th>
                                    <th class="th_no table_g">圖卡</th>
                                </tr>
                                @if(imageDownloadList.length>0){
                                <tr *ngFor="let i of imageDownloadList; let idx = index;" [hidden]="idx % 2 !== 0">
                                    <td class="text_c">
                                        <input class="checkbox-list" type="checkbox"
                                            [checked]="checkImage(imageDownloadList[idx].dictionaryId)"
                                            (change)="changeImage($event, imageDownloadList[idx].dictionaryId)">
                                    </td>
                                    <td class="text_c">
                                        <span class="rwd-th">詞項</span> {{ imageDownloadList[idx].word }}
                                    </td>
                                    <td class="text_c">
                                        <span class="rwd-th">圖卡</span>
                                        <img [src]="imageDownloadList[idx].imageUrl"
                                            [alt]="imageDownloadList[idx].word">
                                    </td>

                                    <!-- 確保 idx + 1 存在時才渲染 -->
                                    <td class="text_c" *ngIf="imageDownloadList[idx + 1]">
                                        <input class="checkbox-list" type="checkbox"
                                            [checked]="checkImage(imageDownloadList[idx + 1].dictionaryId)"
                                            (change)="changeImage($event, imageDownloadList[idx + 1].dictionaryId)">
                                    </td>
                                    <td class="text_c" *ngIf="imageDownloadList[idx + 1]">
                                        <span class="rwd-th">詞項</span> {{ imageDownloadList[idx + 1].word }}
                                    </td>
                                    <td class="text_c" *ngIf="imageDownloadList[idx + 1]">
                                        <span class="rwd-th">圖卡</span>
                                        <img [src]="imageDownloadList[idx + 1].imageUrl"
                                            [alt]="imageDownloadList[idx+1].word">
                                    </td>
                                </tr>

                                }@else {
                                <tr>
                                    <td colspan="6" style="text-align: center;">沒有找到符合條件的資料</td>
                                </tr>
                                }
                            </tbody>
                        </table>
                        @if(imageDownloadList.length>0){
                        <app-paginator [pageSize]="pageSize" [totalRecords]="totalCount" [pageShowCount]="pageShowCount"
                            [nowPage]="nowPage" currentPageReportTemplate="第 {first} 到 {last} 筆，共 {totalRecords} 筆"
                            (clickPageEvent)="getPageFromPaginator($event)"
                            (pageSizeChangeEvent)="getPageSizeFromPaginator($event)"></app-paginator>
                        }
                    </main>
                </div>
            </div>
        </div>
    </div>
</main>