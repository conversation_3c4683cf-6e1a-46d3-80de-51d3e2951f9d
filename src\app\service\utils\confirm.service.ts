import { Injectable } from '@angular/core';
import { MatDialog, MatDialogRef } from '@angular/material/dialog';
import { ConfirmDialogComponent } from '../../dialog/confirm-dialog/confirm-dialog.component';
import { DialogType } from '../../enum/dialogType.enum';

@Injectable({
  providedIn: 'root',
})
export class ConfirmService {
  constructor(private matDialog: MatDialog) {}

  showError(
    message: string,
    statusMessage?: string,
    groupMessage?: string[]
  ): MatDialogRef<ConfirmDialogComponent> {
    return this.matDialog.open(ConfirmDialogComponent, {
      width: '40vw',
      autoFocus: false,
      disableClose: true,
      data: {
        dialogType: DialogType.ERROR,
        statusMsg: statusMessage,
        mainMsg: message,
        groupMessage: groupMessage,
      },
    });
  }

  showSUCCESS(
    message: string,
    statusMessage?: string,
    groupMessage?: string[]
  ): MatDialogRef<ConfirmDialogComponent> {
    return this.matDialog.open(ConfirmDialogComponent, {
      width: '40vw',
      autoFocus: false,
      disableClose: true,
      data: {
        dialogType: DialogType.SUCCESS,
        statusMsg: statusMessage,
        mainMsg: message,
        groupMessage: groupMessage,
      },
    });
  }

  showWARN(
    message: string,
    statusMessage?: string,
    groupMessage?: string[]
  ): MatDialogRef<ConfirmDialogComponent> {
    return this.matDialog.open(ConfirmDialogComponent, {
      width: '40vw',
      autoFocus: false,
      disableClose: true,
      data: {
        dialogType: DialogType.WARN,
        statusMsg: statusMessage,
        mainMsg: message,
        groupMessage: groupMessage,
      },
    });
  }
}
