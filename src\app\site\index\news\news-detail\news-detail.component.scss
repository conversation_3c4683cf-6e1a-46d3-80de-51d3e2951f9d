.news-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    .news-title {
        font-size: 1.5em;
    }
}
.news-box {
    display: flex;
}
.news-content {
    min-width: 100%;
    border: solid gray 1px;
    border-radius: 10px;
    padding: 1em;
    margin: 0 0.5em;
}
.news-img {
    img {
        width: 300px;
    }
}

.breadcrumb-layout {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
}

.breadcrumb-item {
    cursor: pointer;
}
