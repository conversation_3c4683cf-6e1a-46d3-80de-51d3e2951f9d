<main class="master-pages-container-layout">
    <div class="master-pages-container-cont">
        <div class="cont pages-cont-layout">
            <div class="pages-cont-list-layout">

                <!--路徑列-->
                <div class="breadcrumb-layout">
                    <nav class="breadcrumb-cont">
                        <ol class="breadcrumb">
                            <li class="breadcrumb-item">
                                <a href="" (click)="$event.defaultPrevented" [routerLink]="'/home'">
                                    <span>首頁</span>
                                    <span class="material-symbols-outlined">keyboard_arrow_right</span>
                                </a>
                            </li>
                            <li class="breadcrumb-item">
                                <a href="" (click)="$event.defaultPrevented">
                                    <span>{{ethnicity}}</span>
                                    <span class="material-symbols-outlined">keyboard_arrow_right</span>
                                </a>
                            </li>
                            <li class="breadcrumb-item active"><span>詞項列表</span></li>
                        </ol>
                    </nav>
                </div>
                <div class="word-description-container">
                    <div class="advanced-container">
                        <div class="second-search-list">
                            <div class="second-search-box">
                                <div class="second-search-title" (click)="isKeyboard=!isKeyboard">
                                    <div class="second-search-h4 font_w">請選擇欲查詢的書寫符號</div>
                                </div>
                                @if(isKeyboard){
                                <mat-icon class="material-symbols-outlined font_w"
                                    (click)="isKeyboard=false">keyboard_arrow_down</mat-icon>
                                }@else{
                                <mat-icon class="material-symbols-outlined font_w"
                                    (click)="isKeyboard=true">keyboard_arrow_up</mat-icon>
                                }
                            </div>
                            @if(isKeyboard){
                            <div class="second-search-box2">
                                <div class="keyboard-group">
                                    <div class="keyboard-box">
                                        @for ( item of keyboardList; track $index) {
                                        <button class="keyboard-btn" type="button" (click)="keyboardUp(item.id)">
                                            <span class="keyboard_font">{{item.name}}</span>
                                        </button>
                                        }
                                    </div>
                                </div>
                            </div>
                            }
                        </div>
                        <div class="second-search-list">
                            <div class="second-search-box">
                                <div class="second-search-title" (click)="isMark=!isMark">
                                    <div class="second-search-h4 font_w">查詢字母顯示如下</div>
                                </div>
                                @if(isMark){
                                <mat-icon class="material-symbols-outlined font_w"
                                    (click)="isMark=false">keyboard_arrow_down</mat-icon>
                                }@else{
                                <mat-icon class="material-symbols-outlined font_w"
                                    (click)="isMark=true">keyboard_arrow_up</mat-icon>
                                }
                            </div>
                            @if(isMark){
                            <div class="second-search-box2">
                                @if(wordMarkList.length>0){
                                <div class="word-list">
                                    @for ( item of wordMarkList; track $index) {
                                    <span [ngClass]="{'active': selectId === item.id}"
                                        (click)="getWorItem(item)">{{item.name}}</span>
                                    }
                                </div>
                                }@else{
                                <span>請選擇欲查詢的查詢字母</span>
                                }

                            </div>
                            }
                        </div>
                    </div>

                    <div id="word-description-list" class="word-description-list-layout">
                        <!--單詞內容區-->
                        @if(wordItem.id!==''){
                        <div class="word-description-layout">
                            <!--族語單詞-->
                            <mat-accordion multi>
                                <mat-expansion-panel [expanded]="true">
                                    <mat-expansion-panel-header class="panel-header" [style.font-size.em]="1">
                                        <div class="panel-header-group">
                                            <div class="panel-header-name">
                                                <div class="panel-header-name-group"
                                                    style="display: flex;align-items: center;">
                                                    <span
                                                        class="material-symbols-outlined font_g text_r">language</span>
                                                    <span
                                                        class="panel-header-tag">{{wordItem.dialect?wordItem.dialect:wordItem.tribe}}
                                                    </span>
                                                    <span class="word-description-cont-font">{{wordItem.name}}</span>
                                                    @for (audioItem of wordItem.audioItems; track audioItem) {
                                                    <span class="material-symbols-outlined word-description-cont-icon"
                                                        (click)="play(audioItem)" (click)="$event.stopPropagation()">
                                                        volume_up</span>{{audioItem.audioClass}}
                                                    }
                                                </div>
                                            </div>
                                        </div>
                                    </mat-expansion-panel-header>
                                    <!--功能區塊-->
                                    <div class="function-block-background" [style.font-size.em]="fontSize">
                                        <!--詞根-->
                                        <div class="term-list-box2 bg_0">
                                            <div class="term-list-title text_r100">
                                                <span class="material-symbols-outlined text_r">emergency</span>
                                                <div class="term-list-h4 font_bold">詞根：</div>
                                                <div class="term-list-h4 font_bold">
                                                    {{wordItem.derivativeRoot?wordItem.derivativeRoot:'無'}}
                                                </div>
                                            </div>
                                            <div class="term-list-title">
                                                <span class="material-symbols-outlined text_r">book_4</span>
                                                <div class="term-list-h4 font_bold">收錄來源：</div>
                                                <div class="term-list-h4 font_bold">
                                                    {{wordItem.sources&&wordItem.sources!.length>0?wordItem.sources!.join('|'):'無'}}
                                                </div>
                                            </div>
                                        </div>
                                        <!--社群-->
                                        <div class="term-list-box2">
                                            <div class="term-list-title">
                                                <div class="term-list-btns bg_fb font_w"
                                                    (click)="share(shareType.FB,wordItem.id)">
                                                    <img src="icons/facebook-icon.svg" alt="fb">
                                                    <h4>Facebook</h4>
                                                </div>
                                                <div class="term-list-btns bg_line font_w"
                                                    (click)="share(shareType.LINE,wordItem.id)">
                                                    <img src="icons/line-icon.svg" alt="line">
                                                    <h4>LINE</h4>
                                                </div>
                                                <div class="term-list-btns bg_opinion font_w"
                                                    (click)="question(wordItem)">
                                                    <span class="material-symbols-outlined">chat</span>
                                                    <h4>意見回饋</h4>
                                                </div>
                                                @if(isDerivativeRoot){
                                                <div class="term-list-btns bg_root font_w"
                                                    (click)="getRootStructure(wordItem.id)">
                                                    <span class="material-symbols-outlined">device_hub</span>
                                                    <h4>詞根結構</h4>
                                                </div>
                                                }
                                                @if(isImage){
                                                <div class="term-list-btns bg_picture font_w"
                                                    (click)="openImage(wordItem)">
                                                    <span class="material-symbols-outlined">imagesmode</span>
                                                    <h4>圖片</h4>
                                                </div>
                                                }
                                            </div>
                                        </div>
                                    </div>
                                    <!--單詞說明-->
                                    <div class="word-description-explain-layout">
                                        @for (explanationItems of wordItem.explanationItems; track $index) {
                                        <!--解釋1-->
                                        <div class="word-description-explain-list">
                                            <!--說明-->
                                            <div class="word-description-explain-item" [style.font-size.em]="fontSize">
                                                <!--說明文-->
                                                <div class="word-description-explain">
                                                    <div>
                                                        <span class="word-description-explain-tag tag">解釋{{$index+1}}</span>
                                                        <span
                                                            class="word-description-explain-text">{{explanationItems.chineseExplanation}}</span>&nbsp;&nbsp;
                                                    </div>
                                                    <div>
                                                        <label>
                                                            焦點 :&nbsp;
                                                            {{explanationItems.focus.length>0?explanationItems.focus!.join('|'):'無'}}&nbsp;|
                                                            &nbsp; 範疇 :
                                                            {{explanationItems.category.length>0?explanationItems.category.join('|'):'無'}}&nbsp;|&nbsp;
                                                            詞類 :
                                                            {{explanationItems.partOfSpeech.length>0?explanationItems.partOfSpeech.join('|'):'無'}}
                                                        </label>
                                                    </div>
                                                </div>
                                                <!--範例-->
                                                @for ( sentenceItems of explanationItems.sentenceItems; track $index) {
                                                <div class="word-description-sentence">
                                                    <span class="word-description-sentence-text">
                                                        @for ( anaphoraSentenceItem of
                                                        sentenceItems.anaphoraSentence;
                                                        track $index) {
                                                        <div style="position: relative;" (copy)="onCopy($event)">
                                                            <span [ngClass]="{ 'bottom-border': anaphoraSentenceItem.id!== null,
                                                            'bottom-border-hidden': anaphoraSentenceItem.id=== null,
                                                            'highlighted': isNameMatched(anaphoraSentenceItem.name, wordItem.name)  }"
                                                                (click)="clickAnaphoraSentence(anaphoraSentenceItem.id)">
                                                                <span [innerHTML]="sanitizeExplanation(anaphoraSentenceItem.name)"></span>
                                                            </span>
                                                        </div>&nbsp;
                                                        }
                                                        @for ( audioItems of sentenceItems.audioItems; track audioItems)
                                                        {
                                                        <span
                                                            class="material-symbols-outlined word-description-cont-icon"
                                                            (click)="play(audioItems)">volume_up</span>{{audioItems.audioClass}}

                                                        }
                                                    </span>
                                                    <span
                                                        class="word-description-sentence-translate">{{sentenceItems.chineseSentence}}</span>
                                                </div>
                                                }
                                            </div>
                                            @if(explanationItems.isImage){
                                            <div class="carousel-block">
                                                <owl-carousel-o [options]="customOptions">
                                                    @for (item of explanationItems.imageUrl; track item;let i=$index) {
                                                    <ng-template carouselSlide>
                                                        <img [src]="item" [alt]="'圖'+i">
                                                    </ng-template>
                                                    }
                                                </owl-carousel-o>
                                            </div>
                                            }
                                        </div>
                                        }
                                    </div>
                                </mat-expansion-panel>
                            </mat-accordion>
                        </div>
                        }@else{
                        <div class="notfound-group">
                            <span class="notfound-text">
                                目前查無符合條件之資料！
                            </span>
                        </div>
                        }
                    </div>
                </div>
            </div>
        </div>
    </div>
</main>