import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import {
  getLinkListReq,
  getLinkListResp,
} from '../../interface/link.interface';
import { Observable } from 'rxjs';

@Injectable({
  providedIn: 'root',
})
export class LinkService {
  constructor(private httpClient: HttpClient) {}

  getLinkList(req: getLinkListReq): Observable<getLinkListResp> {
    return this.httpClient.post<getLinkListResp>(
      'api/app/related-link/search-related-link-list',
      req
    );
  }
}
