import { Routes } from '@angular/router';

export const routes: Routes = [
  { path: '', redirectTo: 'home', pathMatch: 'full' },
  {
    path: '',
    loadComponent: () =>
      import('./site/index/index.component').then((m) => m.IndexComponent),
    children: [
      {
        path: 'home',
        loadComponent: () =>
          import('./site/index/home/<USER>').then(
            (m) => m.HomeComponent
          ),
      },
      {
        path: 'search',
        loadComponent: () =>
          import('./site/index/search-page/search-page.component').then(
            (m) => m.SearchPageComponent
          ),
      },
      {
        path: 'sharePage',
        loadComponent: () =>
          import('./site/index/share-page/share-page.component').then(
            (m) => m.SharePageComponent
          ),
      },
      {
        path: 'about',
        loadComponent: () =>
          import('./site/index/about/about.component').then(
            (m) => m.AboutComponent
          ),
      },
      { path: 'news', redirectTo: 'news/list', pathMatch: 'full' },
      {
        path: 'news',
        children: [
          {
            path: 'list',
            loadComponent: () =>
              import('./site/index/news/news-list/news-list.component').then(
                (m) => m.NewsListComponent
              ),
          },
          {
            path: 'detail',
            loadComponent: () =>
              import(
                './site/index/news/news-detail/news-detail.component'
              ).then((m) => m.NewsDetailComponent),
          },
        ],
      },
      {
        path: 'question',
        children: [
          { path: '', redirectTo: 'list', pathMatch: 'full' },
          {
            path: 'list',
            loadComponent: () =>
              import(
                './site/index/question/question-list/question-list.component'
              ).then((m) => m.QuestionListComponent),
          },
          {
            path: 'addQuestion',
            loadComponent: () =>
              import(
                './site/index/question/add-question/add-question.component'
              ).then((m) => m.AddQuestionComponent),
          },
          {
            path: 'sysList',
            loadComponent: () =>
              import(
                './site/index/question/sys-question-list/sys-question-list.component'
              ).then((m) => m.SysQuestionListComponent),
          },
          {
            path: 'addSysQuestion',
            loadComponent: () =>
              import(
                './site/index/question/add-sys-question/add-sys-question.component'
              ).then((m) => m.AddSysQuestionComponent),
          },
        ],
      },
      {
        path: 'link',
        loadComponent: () =>
          import('./site/index/link/link.component').then(
            (m) => m.LinkComponent
          ),
      },
      {
        path: 'termRelease',
        loadComponent: () =>
          import('./site/index/term-release/term-release.component').then(
            (m) => m.TermReleaseComponent
          ),
      },
      {
        path: 'subscribe',
        loadComponent: () =>
          import('./site/index/subscribe/subscribe.component').then(
            (m) => m.SubscribeComponent
          ),
      },
      {
        path: 'revisionNotice',
        children: [
          { path: '', redirectTo: 'revisionList', pathMatch: 'full' },
          {
            path: 'revisionList',
            loadComponent: () =>
              import('./site/index/revision-list/revision-list.component').then(
                (m) => m.RevisionListComponent
              ),
          },
          {
            path: 'writingSystem',
            loadComponent: () =>
              import(
                './site/index/writing-system/writing-system.component'
              ).then((m) => m.WritingSystemComponent),
          },
        ],
      },
    ],
  },
  {
    path: '',
    loadComponent: () =>
      import('./site/singleTribe/single-tribe-index.component').then(
        (m) => m.SingleTribeIndexComponent
      ),
    children: [
      {
        path: 'singleSearch',
        loadComponent: () =>
          import(
            './site/singleTribe/single-tribe-search/single-tribe-search.component'
          ).then((m) => m.SingleTribeSearchComponent),
      },
      {
        path: 'singleQuestion',
        loadComponent: () =>
          import(
            './site/singleTribe/single-tribe-question/single-tribe-question.component'
          ).then((m) => m.SingleTribeQuestionComponent),
      },
      {
        path: 'singleWordRec',
        loadComponent: () =>
          import(
            './site/singleTribe/single-word-rec/single-word-rec.component'
          ).then((m) => m.SingleWordRecComponent),
      },
      {
        path: 'wordSubmission',
        loadComponent: () =>
          import(
            './site/singleTribe/word-submission/word-submission.component'
          ).then((m) => m.WordSubmissionComponent),
      },
      {
        path: 'wordList',
        loadComponent: () =>
          import('./site/singleTribe/word-list/word-list.component').then(
            (m) => m.WordListComponent
          ),
      },
      {
        path: 'wordComment',
        loadComponent: () =>
          import('./site/singleTribe/word-comment/word-comment.component').then(
            (m) => m.WordCommentComponent
          ),
      },
      {
        path: 'queryDownload',
        loadComponent: () =>
          import(
            './site/singleTribe/download/query-download/query-download.component'
          ).then((m) => m.QueryDownloadComponent),
      },
      {
        path: 'cardDownload',
        loadComponent: () =>
          import(
            './site/singleTribe/download/card-download/card-download.component'
          ).then((m) => m.CardDownloadComponent),
      },
      {
        path: 'textDownload',
        loadComponent: () =>
          import(
            './site/singleTribe/download/text-download/text-download.component'
          ).then((m) => m.TextDownloadComponent),
      },
      {
        path: 'onlineLearning',
        loadComponent: () =>
          import(
            './site/singleTribe/onlineLearning/online-learning/online-learning.component'
          ).then((m) => m.OnlineLearningComponent),
      },
      {
        path: 'onlineTest',
        loadComponent: () =>
          import(
            './site/singleTribe/onlineLearning/online-test/online-test.component'
          ).then((m) => m.OnlineTestComponent),
      },
      {
        path: 'introduction',
        loadComponent: () =>
          import('./site/singleTribe/introduction/introduction.component').then(
            (m) => m.IntroductionComponent
          ),
      },
      {
        path: 'newWord',
        loadComponent: () =>
          import('./site/singleTribe/new-word/new-word.component').then(
            (m) => m.NewWordComponent
          ),
      },
    ],
  },
];
