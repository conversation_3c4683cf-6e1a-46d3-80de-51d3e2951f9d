<div mat-dialog-title class="success-title">
    <mat-icon class="dialog-close-btn" tabindex="0" (click)="close()">close</mat-icon>
</div>
<mat-dialog-content>
    <div class="text-center">
        <!-- @switch (data.dialogType) {
        @case (dialogType.SUCCESS) {
        <mat-icon class="font-success">check_circle_outline</mat-icon>
        }
        @case (dialogType.WARN) {
        <mat-icon class="font-warn">warning_amber</mat-icon>
        }
        @case (dialogType.ERROR) {
        <mat-icon class="font-error">cancel</mat-icon>
        }
        } -->
        <div class="font-deep" style="margin-top:3em">
            <p class="status-msg"> {{data.statusMsg}}</p>
            <p class="main-msg">{{ data.mainMsg }}</p>
            <!-- 群組訊息 -->
        </div>
        @if(data.groupMessage&& data.groupMessage.length>0){
        <div class="font-deep group-msg">
            @for ( item of data.groupMessage; track $index) {
            <p class="main-msg">{{item}}</p>
            }
        </div>
        }
    </div>
    <mat-dialog-actions [align]="'center'">
        <button class="btn-list btn-primary-line" [mat-dialog-close]="false">取消</button>
        <button class="btn-list btn-primary-solid" [mat-dialog-close]="true">確定</button>
    </mat-dialog-actions>
</mat-dialog-content>