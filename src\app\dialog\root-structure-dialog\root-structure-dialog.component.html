<div mat-dialog-title class="success-title">
    <mat-icon class="dialog-close-btn" tabindex="0" (click)="close()">close</mat-icon>
</div>
<mat-dialog-content>
    @if(isLoading){
    <div class="spinner-wrapper-index">
        <mat-spinner class="mat-spinner-color"></mat-spinner>
    </div>
    }@else{
    <div class="text-center">
        <div class="group-msg">
            <span class="title">
                {{ rootStructure!.derivativeItems[0].derivative}}
            </span>
        </div>
        <div class="font-deep group-msg">
            <label>詞根:
                <label tabindex="0" class="bottom-border"
                    (click)="goto(rootStructure?.tribeId!,rootStructure?.root!,rootStructure?.tribeName!);">{{rootStructure?.root}}
                </label>
            </label>
            @for ( item of rootStructure?.rootExplanation; let index=$index; track $index) {
            <span>{{index+1}}.{{item.length>0?item:'無'}}</span>
            }
        </div>
        @for (item of rootStructure?.derivativeItems;let index=$index; track item) {
        <div class="font-deep group-msg">
            <label>衍生字{{index+1}}:
                <label class="bottom-border"
                    (click)="goto(item.tribeId,item.derivative,item.tribeName);">{{item.derivative}}
                </label>
            </label>
            @for ( item of item.derivativeExplanation; let derivativeExplanationIndex=$index; track $index) {
            <span>{{derivativeExplanationIndex+1}}.{{item.length>0?item:'無'}}</span>
            }
        </div>
        }

    </div>
    }
    <mat-dialog-actions [align]="'center'">
        <button class="btn-list btn-primary-solid" [mat-dialog-close]="true">關閉</button>
    </mat-dialog-actions>
</mat-dialog-content>