<main class="master-pages-container-layout">
    <div class="master-pages-container-cont">
        <div class="cont pages-cont-layout">
            <div class="pages-cont-list-layout">

                <!--路徑列-->
                <div class="breadcrumb-layout">
                    <div>
                        <nav class="breadcrumb-cont">
                            <ol class="breadcrumb">
                                <li class="breadcrumb-item">
                                    <a href="" (click)="$event.defaultPrevented" [routerLink]="'/home'">
                                        <span>首頁</span>
                                        <span class="material-symbols-outlined">keyboard_arrow_right</span>
                                    </a>
                                </li>
                                <li class="breadcrumb-item">
                                    <a href="" (click)="$event.defaultPrevented">
                                        <span>{{ethnicity}}</span>
                                        <span class="material-symbols-outlined">keyboard_arrow_right</span>
                                    </a>
                                </li>
                                <li class="breadcrumb-item active"><span>檢索</span></li>
                            </ol>
                        </nav>
                    </div>
                    <div>
                        <nav class="breadcrumb-cont">
                            <ol class="breadcrumb">
                                <li class="breadcrumb-item">
                                    <a hre="" (click)="back($event)">
                                        <span>&lt;&lt;回上一頁</span>
                                    </a>
                                </li>
                            </ol>
                        </nav>
                    </div>
                </div>
                <div class="word-description-container">
                    <div class="advanced-container">
                        <main class="second-search-layout">
                            <div class="search-group">
                                <div class="search-all" #searchAll>
                                    <div class="search-box">
                                        <input class="search-bar font_18" type="text" title="keyword"
                                            placeholder="請輸入族語或中文" [(ngModel)]="keyword"
                                            (ngModelChange)="onKeywordChange($event)"
                                            (keydown.enter)="search(); keywordInput.blur()" #keywordInput>
                                        <a class="search-a1" href=""
                                            (click)="$event.preventDefault();keyword='';autoInputList=[];">
                                            <span class="material-symbols-outlined">close</span>
                                        </a>
                                    </div>
                                    <!--搜尋框架-->
                                    @if(autoInputList.length>0){
                                    <div class="search-frame">
                                        @for (value of autoInputList; track value) {
                                        <div class="search-frame-info" (click)="selectValue(value)">
                                            <span class="search-frame-name">{{value}}</span>
                                        </div>
                                        }
                                    </div>
                                    }
                                </div>
                                <div class="btns">
                                    <input class="btn-box btn-primary-color" value="搜尋" type="button"
                                        (click)="search()">
                                </div>
                            </div>
                            <!--鍵盤-->
                            <div class="keyboard-group">
                                <div class="keyboard-box">
                                    @for (key of keyboardList; track key) {
                                    <button class="keyboard-btn" (click)="keyboardUp(key,keywordInput)">
                                        <span class="keyboard_font">{{key}}</span>
                                    </button>
                                    }
                                </div>
                            </div>
                            <div class="second-search-list">
                                <div class="second-search-box">
                                    <div class="second-search-title" (click)="isDialect=!isDialect">
                                        <div class="second-search-h4 font_w">請選擇語別 (可多選)</div>
                                    </div>
                                    @if(isDialect){
                                    <mat-icon class="material-symbols-outlined font_w"
                                        (click)="isDialect=false">keyboard_arrow_down</mat-icon>
                                    }@else{
                                    <mat-icon class="isDialect-symbols-outlined font_w"
                                        (click)="isDialect=true">keyboard_arrow_up</mat-icon>
                                    }
                                </div>
                                @if(isDialect){
                                <div class="second-search-box2">
                                    <div class="advanced-search-cont-layout">
                                        <ul class="advanced-search-cont">
                                            @if(dialectList.length>0){
                                            @for ( item of dialectList; let i=$index; track i) {
                                            <li class="advanced-search-item">
                                                <span class="checkbox-list">
                                                    <input id="dialectCheckbox{{i}}" type="checkbox" [value]="item.id"
                                                        [(ngModel)]="item.selected" (change)="dialectListChange(item)">
                                                    <label for="dialectCheckbox{{i}}">{{item.name}}</label>
                                                </span>
                                            </li>
                                            }
                                            }@else{
                                            <div style="width: 100%;text-align: center;">
                                                <span>無語別</span>
                                            </div>
                                            }
                                        </ul>
                                    </div>
                                </div>
                                }
                            </div>

                            <!--下拉選單3 -->
                            <div class="second-search-list">
                                <div class="second-search-box">
                                    <div class="second-search-title" (click)="isAdvanced=!isAdvanced">
                                        <div class="second-search-h4 font_w">進階搜尋</div>
                                    </div>
                                    @if(isAdvanced){
                                    <mat-icon class="material-symbols-outlined font_w"
                                        (click)="isAdvanced=false">keyboard_arrow_down</mat-icon>
                                    }@else{
                                    <mat-icon class="isDialect-symbols-outlined font_w"
                                        (click)="isAdvanced=true">keyboard_arrow_up</mat-icon>
                                    }
                                </div>
                                @if(isAdvanced){
                                <div class="second-search-box2">
                                    <div class="input-group">
                                        <span class="second-search-item">
                                            書寫系統
                                        </span>
                                        <div class="select-group">
                                            <select title="symbolStart" class="select-box" [(ngModel)]="symbolStart">
                                                @for (option of symbolList; track option) {
                                                <option [ngValue]="option.id">{{option.name}}</option>
                                                }
                                            </select>&nbsp;<span class="font_to">~</span>&nbsp;
                                            <select title="symbolEnd" class="select-box" [(ngModel)]="symbolEnd">
                                                @for (option of symbolList; track option) {
                                                <option [ngValue]="option.id">{{option.name}}</option>
                                                }
                                            </select>
                                        </div>
                                    </div>
                                    <div class="input-group">
                                        <span class="second-search-item">
                                            範疇分類
                                        </span>
                                        <select title="category" [(ngModel)]="category">
                                            @for (option of categoryList; track option) {
                                            <option [ngValue]="option.id">{{option.name}}</option>
                                            }
                                        </select>
                                    </div>
                                    <div class="input-group">
                                        <span class="second-search-item">
                                            詞類
                                        </span>
                                        <select title="partOfSpeech" [(ngModel)]="partOfSpeech">
                                            @for (option of partOfSpeechList; track option) {
                                            <option [ngValue]="option.id">{{option.name}}</option>
                                            }
                                        </select>
                                    </div>
                                    <div class="input-group">
                                        <span class="second-search-item">
                                            僅搜尋
                                        </span>
                                        <select title="restrictSearch" [(ngModel)]="restrictSearch">
                                            @for (option of restrictSearchList; track option) {
                                            <option [ngValue]="option.value">{{option.name}}</option>
                                            }
                                        </select>
                                    </div>
                                    <div class="input-group">
                                        <span class="second-search-item">
                                            收錄來源
                                        </span>
                                        <div class="checkbox-group">
                                            <ul class="advanced-search-cont">
                                                @for ( item of sourceList; let i=$index; track i) {
                                                <li class="advanced-search-item">
                                                    <span class="checkbox-list">
                                                        <input id="sourceCheckbox{{i}}" type="checkbox"
                                                            [value]="item.id" [(ngModel)]="item.selected"
                                                            (change)="sourceListChange(item)">
                                                        <label for="sourceCheckbox{{i}}">{{item.name}}</label>
                                                    </span>
                                                </li>
                                                }
                                            </ul>
                                        </div>
                                    </div>
                                </div>
                                }
                            </div>

                        </main>
                    </div>

                    <div class="word-description-list-layout">
                        <mat-tab-group (selectedTabChange)="selectTab($event)" #tabGroup>
                            @for ( item of searchGroup; track $index) {
                            <mat-tab [label]="item" [disabled]="!this.keyword">
                                @if(tabGroup.selectedIndex === $index){
                                <ng-template mat-tab-label>{{item}}</ng-template>
                                @if(totalCount>0){
                                <div class="word-description-layout-title">{{item}}查詢條件：所有族語「{{showKeyword}}」 之查詢結果
                                    (共{{totalCount}}筆)
                                </div>
                                }@else{
                                <div class="word-description-layout-title">{{item}}查詢條件：目前查無資料
                                </div>
                                }
                                <!--單詞內容區-->
                                @if(item!==SearhModel.Example){
                                @if(dictionaryList.length>0){
                                <div class="word-description-layout">
                                    <!--族語單詞-->
                                    <mat-accordion multi>
                                        @for (item of dictionaryList; track $index) {
                                        <mat-expansion-panel [expanded]="item.isOpenPanel"
                                            (opened)="onPanelOpened(item)" (closed)="onPanelClose(item)">
                                            <mat-expansion-panel-header class="panel-header" [style.font-size.em]="1">
                                                <div class="panel-header-group">
                                                    <div class="panel-header-name" style="padding-right: 10px;">
                                                        <div class="panel-header-name-group"
                                                            style="display: flex;align-items: center;">
                                                            <div class="panel-header-tag-group"
                                                                style=" display: flex;align-items: center">
                                                                <span
                                                                    class="material-symbols-outlined font_g text_r">language</span>
                                                                <span
                                                                    class="panel-header-tag">{{item.dialect?item.dialect:item.tribe}}
                                                                </span>
                                                            </div>
                                                            <div class="word-description-cont-font name-box">
                                                                <span>{{item.name}} </span>
                                                            </div>
                                                            @for (audioItem of item.audioItems; track audioItem) {
                                                            <div style="display: flex;align-items: center;">
                                                                <span
                                                                    class="material-symbols-outlined word-description-cont-icon"
                                                                    (click)="play(audioItem)"
                                                                    (click)="$event.stopPropagation()">
                                                                    volume_up</span>{{audioItem.audioClass}}
                                                            </div>
                                                            }
                                                        </div>
                                                    </div>
                                                    <div class="panel-header-explanation">
                                                        <div style="display: flex; align-items:center; line-height: 1;">
                                                            <span class="panel-header-description-tag tag2">解釋</span>
                                                            <span class="word-description-cont-font"
                                                                [innerHTML]="sanitizeExplanation(item.chineseExplanation)                                                                ">
                                                            </span>
                                                        </div>
                                                    </div>
                                                </div>
                                            </mat-expansion-panel-header>
                                            @if(item.loading){
                                            <div class="spinner-wrapper-index">
                                                <mat-spinner class="mat-spinner-color"></mat-spinner>
                                            </div>
                                            }@else{
                                            <!--功能區塊-->
                                            <div class="function-block-background" [style.font-size.em]="fontSize">
                                                <!--詞根-->
                                                <div class="term-list-box2 bg_0">
                                                    <div class="term-list-title text_r100">
                                                        <span class="material-symbols-outlined text_r">emergency</span>
                                                        <div class="term-list-h4 font_bold">詞根：</div>
                                                        <div class="term-list-h4 font_bold">
                                                            {{item.derivativeRoot?item.derivativeRoot:'無'}}
                                                        </div>
                                                    </div>
                                                    <div class="term-list-title">
                                                        <span class="material-symbols-outlined text_r">book_4</span>
                                                        <div class="term-list-h4 font_bold">收錄來源：</div>
                                                        <div class="term-list-h4 font_bold">
                                                            {{item.wordItem?.sources&&item.wordItem?.sources!.length>0?item.wordItem?.sources!.join('|'):'無'}}
                                                        </div>
                                                    </div>
                                                </div>
                                                <!--社群-->
                                                <div class="term-list-box2">
                                                    <div class="term-list-title">
                                                        <div class="term-list-btns bg_fb font_w"
                                                            (click)="share(shareType.FB,item.id)">
                                                            <img src="icons/facebook-icon.svg" alt="fb">
                                                            <h4>Facebook</h4>
                                                        </div>
                                                        <div class="term-list-btns bg_line font_w"
                                                            (click)="share(shareType.LINE,item.id)">
                                                            <img src="icons/line-icon.svg" alt="line">
                                                            <h4>LINE</h4>
                                                        </div>
                                                        <div class="term-list-btns bg_opinion font_w"
                                                            (click)="question(item)">
                                                            <span class="material-symbols-outlined">chat</span>
                                                            <h4>意見回饋</h4>
                                                        </div>
                                                        @if(item.isDerivativeRoot){
                                                        <div class="term-list-btns bg_root font_w"
                                                            (click)="getRootStructure(item.id)">
                                                            <span class="material-symbols-outlined">device_hub</span>
                                                            <h4>詞根結構</h4>
                                                        </div>
                                                        }
                                                        @if(item.isImage){
                                                        <div class="term-list-btns bg_picture font_w"
                                                            (click)="openImage(item)">
                                                            <span class="material-symbols-outlined">imagesmode</span>
                                                            <h4>圖片</h4>
                                                        </div>
                                                        }
                                                    </div>
                                                </div>
                                            </div>
                                            <!--單詞說明-->
                                            <div class="word-description-explain-layout">
                                                @for (wordItem of item.wordItem?.explanationItems; track $index) {
                                                <!--解釋1-->
                                                <div class="word-description-explain-list">
                                                    <!--說明-->
                                                    <div class="word-description-explain-item"
                                                        [style.font-size.em]="fontSize">
                                                        <!--說明文-->
                                                        <div class="word-description-explain">
                                                            <div>
                                                                <span
                                                                    class="word-description-explain-tag tag">解釋{{$index+1}}</span>
                                                                <span
                                                                    class="word-description-explain-text">{{wordItem.chineseExplanation}}</span>&nbsp;&nbsp;
                                                            </div>
                                                            <div>
                                                                <label>
                                                                    焦點 :&nbsp;
                                                                    {{wordItem.focus.length>0?wordItem.focus!.join('|'):'無'}}&nbsp;|
                                                                    &nbsp; 範疇 :
                                                                    {{wordItem.category.length>0?wordItem.category.join('|'):'無'}}&nbsp;|&nbsp;
                                                                    詞類 :
                                                                    {{wordItem.partOfSpeech.length>0?wordItem.partOfSpeech.join('|'):'無'}}
                                                                </label>
                                                            </div>
                                                        </div>
                                                        <!--範例-->
                                                        @for ( sentenceItems of wordItem.sentenceItems; track $index) {
                                                        <div class="word-description-sentence">
                                                            <span class="word-description-sentence-text">
                                                                @for ( anaphoraSentenceItem of
                                                                sentenceItems.anaphoraSentence;
                                                                track $index) {
                                                                <div style="position: relative;"
                                                                    (copy)="onCopy($event)">
                                                                    <span [ngClass]="{ 'bottom-border': anaphoraSentenceItem.id!== null,
                                                    'bottom-border-hidden': anaphoraSentenceItem.id=== null, 'highlighted': isNameMatched(anaphoraSentenceItem.name, item.name) }"
                                                                        (click)="clickAnaphoraSentence(anaphoraSentenceItem.id)">
                                                                        <span [innerHTML]="sanitizeExplanation(anaphoraSentenceItem.name)"></span>
                                                                    </span>
                                                                </div>&nbsp;
                                                                }
                                                                @for ( audioItems of sentenceItems.audioItems; track
                                                                audioItems) {
                                                                <span
                                                                    class="material-symbols-outlined word-description-cont-icon"
                                                                    (click)="play(audioItems)">volume_up</span>{{audioItems.audioClass}}
                                                                }
                                                            </span>
                                                            <span
                                                                class="word-description-sentence-translate">{{sentenceItems.chineseSentence}}</span>
                                                        </div>
                                                        }
                                                    </div>
                                                    @if(wordItem.isImage){
                                                    <div class="carousel-block">
                                                        <owl-carousel-o [options]="customOptions">
                                                            @for (item of wordItem.imageUrl; track item;let i=$index ) {
                                                            <ng-template carouselSlide>
                                                                <img [src]="item" [alt]="'圖'+i">
                                                            </ng-template>
                                                            }
                                                        </owl-carousel-o>
                                                    </div>
                                                    }
                                                </div>
                                                }
                                            </div>
                                            }
                                        </mat-expansion-panel>
                                        }
                                    </mat-accordion>
                                </div>
                                }@else{
                                <div class="notfound-group">
                                    @if(!showKeyword){
                                    <span class="notfound-text default">請先輸入族語或中文</span>
                                    }@else{
                                    <span class="notfound-text">
                                        目前查無符合條件之資料！
                                    </span>
                                    <span class="notfound-text">
                                        是否要建議欲新增的詞項?
                                    </span>
                                    <button class="btn-list btn-danger-solid" (click)="goWantContribute()">我要投稿</button>
                                    }
                                </div>
                                }
                                @if(dictionaryList.length>0){
                                <app-paginator [pageSize]="pageSize" [totalRecords]="totalCount"
                                    [pageShowCount]="pageShowCount" [nowPage]="nowPage"
                                    currentPageReportTemplate="第 {first} 到 {last} 筆，共 {totalRecords} 筆"
                                    (clickPageEvent)="getPageFromPaginator($event)"
                                    (pageSizeChangeEvent)="getPageSizeFromPaginator($event)"></app-paginator>
                                }
                                }@else{
                                @if(exampleList.length>0){
                                <div class="word-description-layout">
                                    <!--族語單詞-->
                                    <mat-accordion multi>
                                        @for (item of exampleList; track $index) {
                                        <mat-expansion-panel [expanded]="item.isOpenPanel">
                                            <mat-expansion-panel-header class="panel-header">
                                                <div class="panel-header-group">
                                                    <div class="panel-header-name">
                                                        <div style="display: flex;align-items: center;">
                                                            <span class=" panel-header-tag">來源詞項
                                                            </span>
                                                            <span
                                                                class="word-description-cont-font">{{item.word}}</span>
                                                        </div>
                                                    </div>
                                                </div>
                                            </mat-expansion-panel-header>
                                            @if(item.loading){
                                            <div class="spinner-wrapper-index">
                                                <mat-spinner class="mat-spinner-color"></mat-spinner>
                                            </div>
                                            }@else{
                                            <!--單詞說明-->
                                            <div class="word-description-explain-layout">
                                                <!--解釋1-->
                                                <div class="word-description-explain-list">
                                                    <!--說明-->
                                                    <div class="word-description-explain-item">
                                                        <!--說明文-->
                                                        <!--範例-->
                                                        <div class="word-description-sentence">
                                                            <span class="word-description-sentence-text">
                                                                <div style="position: relative;">
                                                                    <span
                                                                        [innerHTML]="sanitizeExplanation(item.originalSentence)">
                                                                        &nbsp;
                                                                    </span>
                                                                </div>
                                                            </span>
                                                            <span class="word-description-sentence-translate"
                                                                [innerHTML]="sanitizeExplanation(item.chineseSentence)"></span>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            }
                                        </mat-expansion-panel>
                                        }
                                    </mat-accordion>
                                </div>
                                }@else{
                                <div class="notfound-group">
                                    <span class="notfound-text">
                                        目前查無符合條件之資料！
                                    </span>
                                    <span class="notfound-text">
                                        是否要建議欲新增的詞項?
                                    </span>
                                    <button class="btn-list btn-danger-solid">我要投稿</button>
                                </div>
                                }
                                @if(exampleList.length>0){
                                <app-paginator [pageSize]="pageSize" [totalRecords]="totalCount"
                                    [pageShowCount]="pageShowCount" [nowPage]="nowPage"
                                    currentPageReportTemplate="第 {first} 到 {last} 筆，共 {totalRecords} 筆"
                                    (clickPageEvent)="getPageFromPaginator($event)"
                                    (pageSizeChangeEvent)="getPageSizeFromPaginator($event)"></app-paginator>
                                }
                                }
                                }
                            </mat-tab>
                            }
                        </mat-tab-group>
                    </div>
                </div>
            </div>
        </div>
    </div>
</main>