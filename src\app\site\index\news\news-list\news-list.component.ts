import { Component, OnInit } from '@angular/core';
import { NewsService } from '../../../../service/curl/news.service';
import { Router, RouterLink } from '@angular/router';
import {
  getNewsListReq,
  getNewsListResp,
} from '../../../../interface/news.interface';
import { UtilsService } from '../../../../service/utils/utils.service';
import { FormsModule } from '@angular/forms';
import { PaginatorComponent } from '../../../../utils/paginator/paginator.component';
import { DatePipe } from '@angular/common';

@Component({
    selector: 'app-news-list',
    templateUrl: './news-list.component.html',
    styleUrl: './news-list.component.scss',
    imports: [
        RouterLink,
        FormsModule,
        PaginatorComponent,
        DatePipe,
    ],
})
export class NewsListComponent implements OnInit {
  keyword: string = '';
  newsList: { id: string; title: string; date: string; creator: string }[] = [];

  pageSize: number = 10; //一頁幾筆資料
  nowPage: number = 1;
  totalCount: number = 0; //總筆數
  pageShowCount: number = 5; //分頁器秀幾個

  list: {
    column: string;
    title: string;
    width: string;
    sort: boolean;
  }[] = [
    {
      column: 'index',
      title: '項次',
      width: '',
      sort: false,
    },
    {
      column: 'creationTime',
      title: '建立日期',
      width: '',
      sort: true,
    },
    {
      column: 'categoryName',
      title: '標題',
      width: '',
      sort: true,
    },
    {
      column: 'categoryName',
      title: '發布單位',
      width: '',
      sort: true,
    },
  ];

  constructor(private router: Router, private newsService: NewsService,
    private utils:UtilsService
  ) {}

  ngOnInit(): void {
    this.utils.setTitle('最新消息')
    this.getNewsList();
  }

  search() {
    this.nowPage = 1;
    this.getNewsList();
  }

  getNewsList() {
    let req: getNewsListReq = {
      page: this.nowPage,
      pageSize: this.pageSize,
      keyword: this.keyword,
    };
    this.newsService.getNewsList(req).subscribe({
      next: (resp: getNewsListResp) => {
        this.newsList = resp.data.newsItems;
        this.totalCount = resp.data.itemTotalCount;
      },
      error: () => {},
    });
  }

  /**
   * 第幾頁
   * @param item number
   */
  getPageFromPaginator(nowPage: number) {
    this.nowPage = nowPage;
    this.getNewsList();
  }
  /**
   * 每頁資料數量
   * @param item number
   */
  getPageSizeFromPaginator(pageSize: number) {
    this.pageSize = pageSize;
    this.nowPage = 1;
    this.getNewsList();
  }

  view(id: string) {
    this.router.navigate(['news/detail'], {
      queryParams: {
        id: id,
      },
    });
  }
  back(event: Event) {
    event.preventDefault();
    history.back();
  }
}
