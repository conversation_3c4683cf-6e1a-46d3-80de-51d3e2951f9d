<main class="master-pages-container-layout">
    <div class="master-pages-container-cont">
        <div class="cont pages-cont-layout">
            <div class="pages-cont-list-layout">

                <!--路徑列-->
                <div class="breadcrumb-layout">
                    <div>
                        <nav class="breadcrumb-cont">
                            <ol class="breadcrumb">
                                <li class="breadcrumb-item">
                                    <a href="" (click)="$event.defaultPrevented" [routerLink]="'/home'">
                                        <span>首頁</span>
                                        <span class="material-symbols-outlined">keyboard_arrow_right</span>
                                    </a>
                                </li>
                                <li class="breadcrumb-item">
                                    <a href="" (click)="$event.defaultPrevented">
                                        <span>{{ethnicity}}</span>
                                        <span class="material-symbols-outlined">keyboard_arrow_right</span>
                                    </a>
                                </li>
                                <li class="breadcrumb-item active">線上學習</li>
                            </ol>
                        </nav>
                    </div>
                    <div>
                        <nav class="breadcrumb-cont">
                            <ol class="breadcrumb">
                                <li class="breadcrumb-item">
                                    <a hre="" (click)="back($event)">
                                        <span>&lt;&lt;回上一頁</span>
                                    </a>
                                </li>
                            </ol>
                        </nav>
                    </div>
                </div>
                <!--內容區-->
                <div class="word-description-container">
                    <div class="advanced-container">
                        <main class="second-search-layout">
                            <div class="second-search-list">
                                <div class="second-search-box">
                                    <div class="second-search-title" (click)="isLanguard=!isLanguard">
                                        <div class="second-search-h4 font_w">請選擇語別 (可多選)</div>
                                    </div>
                                    @if(isLanguard){
                                    <mat-icon class="material-symbols-outlined font_w"
                                        (click)="isLanguard=false">keyboard_arrow_down</mat-icon>
                                    }@else{
                                    <mat-icon class="material-symbols-outlined font_w"
                                        (click)="isLanguard=true">keyboard_arrow_up</mat-icon>
                                    }
                                </div>
                                @if(isLanguard){
                                <div class="second-search-box2">
                                    <div class="advanced-search-cont-layout">
                                        <ul class="advanced-search-cont">
                                            @if(searchOption.dialectItems.length>0)
                                            {
                                            @for ( item of searchOption.dialectItems; let i=$index; track i) {
                                            <li class="advanced-search-item">
                                                <span class="checkbox-list">
                                                    <input id="languageCheckbox{{i}}" type="checkbox" [value]="item.id"
                                                        [(ngModel)]="item.selected" (change)="selectDialect( item.id)">
                                                    <label for="languageCheckbox{{i}}">{{item.name}}</label>
                                                </span>
                                            </li>
                                            }
                                            }@else{
                                            <span>無語別</span>
                                            }

                                        </ul>
                                    </div>
                                </div>
                                }
                            </div>
                            <div class="second-search-list">
                                <div class="second-search-box">
                                    <div class="second-search-title" (click)="isCategory=!isCategory">
                                        <div class="second-search-h4 font_w">請選擇類別(可多選)</div>
                                    </div>
                                    @if(isCategory){
                                    <mat-icon class="material-symbols-outlined font_w"
                                        (click)="isCategory=false">keyboard_arrow_down</mat-icon>
                                    }@else{
                                    <mat-icon class="material-symbols-outlined font_w"
                                        (click)="isCategory=true">keyboard_arrow_up</mat-icon>
                                    }
                                </div>
                                @if(isCategory){
                                <div class="second-search-box2">
                                    <div class="advanced-search-cont-layout">
                                        <ul class="advanced-search-cont">
                                            @for ( item of searchOption.categoryItems; let i=$index; track i) {
                                            <li class="advanced-search-item">
                                                <span class="checkbox-list">
                                                    <input id="dialectCheckbox{{i}}" type="checkbox" [value]="item.id"
                                                        (change)="selectCategory(item.id)">
                                                    <label for="dialectCheckbox{{i}}">{{item.name}}</label>
                                                </span>
                                            </li>
                                            }
                                        </ul>
                                    </div>
                                </div>
                                }
                            </div>
                        </main>
                        <div class="online-learning-list">
                            <!--主題-->
                            <!--族族辭典-->
                            <div class="online-learning-box">
                                <div class="online-learning-title" (click)="openIsQuestion()">
                                    <div class="online-learning-h4 font_w">大展身手</div>
                                </div>
                                @if(isQuestion){
                                <mat-icon class="material-symbols-outlined font_w"
                                    (click)="openIsQuestion()">keyboard_arrow_down</mat-icon>
                                }@else{
                                <mat-icon class="material-symbols-outlined font_w"
                                    (click)="openIsQuestion()">keyboard_arrow_up</mat-icon>
                                }
                            </div>
                            @if(isQuestion){

                            <div class="online-learning-box2">
                                @if(isQuestionSpinner){
                                <div class="spinner-wrapper-index">
                                    <mat-spinner class="mat-spinner-color"></mat-spinner>
                                </div>
                                <div class="online-learning-cont">
                                    <button style="width: 100%;" class="btn-list btn-primary-solid" type="button"
                                        (click)="getQuestion()">
                                        下一題
                                    </button>
                                </div>
                                }@else{
                                <img [src]="questionList.quizImageUrl" alt="問題圖">
                                <div class="online-learning-cont">
                                    @for (item of questionList.questionItems; track item) {
                                    <button class="btn-choose btn-correct-color" type="button"
                                        [ngClass]="checkIsCorrect(item.isAns,item.isCorrect)" (click)="send(item.id)">
                                        <span class="test_font">{{item.dictionaryName}}</span>
                                        @if(item.isAns&&item.isCorrect){
                                        <span class="material-symbols-outlined">check</span>
                                        }@if(item.isAns&&!item.isCorrect){
                                        <span class="material-symbols-outlined">close</span>
                                        }
                                    </button>
                                    }
                                    <button style="width: 100%;" class="btn-list btn-primary-solid" type="button"
                                        (click)="getQuestion()">
                                        下一題
                                    </button>
                                </div>
                                }

                            </div>
                            }
                        </div>
                    </div>

                    <div class="word-description-list-layout">
                        <table class="table-list-layout rwd-table03">
                            <tbody>
                                <tr class="bg_g1">
                                    @for (item of list; track $index) {
                                    <th class="th_no table_g" [style.width]="item.width">{{item.title}}</th>
                                    }
                                </tr>@if(learningDataList.length>0){
                                @for (item of learningDataList;let index=$index; track item) {
                                <tr>
                                    <td class="text_c">
                                        <span class="rwd-th">項次</span>
                                        {{index+1+(nowPage>1?(nowPage-1)*pageSize:0)}}
                                    </td>
                                    <td class="text_c">
                                        <span class="rwd-th">語別</span>
                                        {{item.dialect}}
                                    </td>
                                    <td class="text_c">
                                        <span class="rwd-th">類別</span>
                                        {{item.category}}
                                    </td>
                                    <td class="text_c">
                                        <span class="rwd-th">詞項</span>
                                        {{item.name}}
                                    </td>
                                    <td class="text_c">
                                        <span class="rwd-th">中文解釋</span>
                                        {{item.chineseExplanation}}
                                    </td>
                                    <td class="text_c">
                                        <span class="rwd-th">功能</span>
                                        <input class="btn-list btn-primary-table-color" value="詳細" type="button"
                                            (click)="detail(item.id)">
                                    </td>
                                </tr>
                                }
                                }@else{
                                <tr>
                                    <td colspan="6" style="text-align: center;">沒有找到符合條件的資料</td>
                                </tr>
                                }
                            </tbody>
                        </table>
                        @if(learningDataList.length>0){
                        <app-paginator [pageSize]="pageSize" [totalRecords]="totalCount" [pageShowCount]="pageShowCount"
                            [nowPage]="nowPage" currentPageReportTemplate="第 {first} 到 {last} 筆，共 {totalRecords} 筆"
                            (clickPageEvent)="getPageFromPaginator($event)"
                            (pageSizeChangeEvent)="getPageSizeFromPaginator($event)"></app-paginator>
                        }
                    </div>
                </div>
            </div>
        </div>
    </div>
</main>