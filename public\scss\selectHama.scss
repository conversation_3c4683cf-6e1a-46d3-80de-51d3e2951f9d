// ================================
// Scss Document
//變數
$white-color: #fff;
$bg-gray-100: rgba(243, 244, 246, 1);
$black-color: #000;
$blue-color: #3367ac;
$red-color: #d02820;

//排版
.flex-center {
    display: flex;
    align-items: center;
}

.mb-12px {
    margin-bottom: 12px;
}

.row {
    display: flex;
    flex-wrap: wrap;
}

.col {
    flex-basis: 0;
    flex-grow: 1;
    max-width: 100%;
}

.col-auto {
    flex: 0 0 auto;
    width: auto !important;
    max-width: 100%;
}

.gutter-16px {
    margin-right: 2px;
    margin-left: 2px;
}

.w-full {
    width: 98%;
}

.relative {
    position: relative;
}

.absolute {
    position: absolute;
}

.hidden {
    display: none;
}

.bg-gray-100 {
    background-color: $bg-gray-100;
}

.radius-card {
    border-radius: 22px;
}

@media (min-width: 992px) {
    .col-lg {
        flex-basis: 0;
        flex-grow: 1;
        max-width: 100%;
    }


    .col-3 {
        width: 25% !important;
        position: relative;
        max-width: 25%;
        flex: 0 0 25%;
    }

}

@media (max-width: 992px) {
    .col-12 {
        width: 100% !important;
        position: relative;
        max-width: 100%;
        flex: 0 0 100%;
    }
}

.search-layout {
    .input-style {
        width: 100%;
        max-width: 100%;
    }
}

//下拉選單元件
.select-style {
    select.select {
        -webkit-appearance: none;
        -moz-appearance: none;
        appearance: none;
        margin: 0;
        padding: 0;
        border-radius: 5px;
        background-color: #fff;
        border: 0;
        box-shadow: 0 1px 1px rgba(0, 0, 0, 0) inset;
        box-sizing: border-box;
        transition:
            border-color 0.15s ease-in-out 0s,
            box-shadow 0.15s ease-in-out 0s;

        font-size: 1rem;
        line-height: 1.625;
        letter-spacing: 0.125rem;
        font-weight: 700;
        height: 44px;
        background: transparent;
        padding-right: 52px;
        cursor: pointer;
        padding-left: 22px;
        line-height: 44px;
        -webkit-appearance: none;
        -moz-appearance: none;
        appearance: none;
        z-index: 1;
        position: relative;
        border: 0;

        &.text-center {
            text-indent: 0.125rem;
        }

        &:focus,
        &:focus-visible {
            border: 4px solid $blue-color;
            border-radius: 44px;
            padding-right: 48px;
            padding-left: 18px;
            line-height: 36px;
            outline: 0;
        }

        option {
            background: #f3f3f5;
            -webkit-appearance: none;
            -moz-appearance: none;
            appearance: none;
        }
    }

    .block-select-bg {
        pointer-events: none;
        -webkit-user-select: none;
        -moz-user-select: none;
        user-select: none;
        z-index: 0;
        left: 0;
        right: 0;
        top: 0;
        height: 44px;
        width: 100%;
    }

    .block-select-bg-rect {
        height: 44px;
        background-color: $bg-gray-100;
    }
}

.button-dot {
    width: 44px;
    height: 44px;
    border-radius: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
    color: $black-color;
    background-color: $bg-gray-100;
    transition: background-color 0.1s ease;
    border: 0;

    * {
        pointer-events: none;
        -webkit-user-select: none;
        -moz-user-select: none;
        user-select: none;
    }

    &:hover {
        background-color: $bg-gray-100;
    }

    &:active,
    &.is-active {
        background-color: $bg-gray-100;
    }

    &:disabled,
    &.is-disabled {
        opacity: 0.3;
        pointer-events: none;
    }

    &.style-sm {
        width: 24px;
        height: 24px;

        svg {
            transform: scale(0.6);
        }
    }

    &.style-tabbar {
        width: 42px;
        height: 42px;
    }
}

.style-focus-group-outline {
    &:hover {
        .button-dot {
            background-color: $bg-gray-100;
        }
    }

    &:active {
        .button-dot {
            background-color: $bg-gray-100;

            &.bg-gray-100 {
                background-color: $bg-gray-100;
                color: $white-color;

                .button-round-icon {
                    color: $white-color;
                }
            }
        }
    }

    &:disabled {

        .button-dot,
        .style-focus-group-outlineis-disabled .button-dot {
            opacity: 0.3;
            pointer-events: none;
        }
    }
}

//輸入欄位
.input-style {
    font-size: 1rem;
    line-height: 1.625;
    letter-spacing: 0.125rem;
    padding: 9px 2px 9px 16px;
    border-radius: 40px;
    font-weight: 700;
    width: 260px;
    max-width: 100%;
    -webkit-appearance: none;
    border: 1px solid $bg-gray-100;
    background: $bg-gray-100;

    &.text-center {
        text-indent: 0.125rem;
    }

    &[type="search"]::-webkit-search-cancel-button {
        -webkit-appearance: none;
        height: 32px;
        width: 32px;
        border-radius: 50%;
        // background: url("images/icon-cross-black.svg") center center no-repeat;
        cursor: pointer;
    }

    &.bg-white {
        background: $white-color;
    }

    &:focus,
    &:focus-visible {
        padding: 6px 0 6px 13px;
        border: 4px solid $blue-color;
        outline: 0;
    }

    &.style-dark {
        background: rgba(243, 243, 245, 0.2);
        border: 1px solid $white-color;

        &:focus {
            padding: 6px 0 6px 13px;
            border: 4px solid $blue-color;
        }

        &::-moz-placeholder,
        &::placeholder {
            color: rgba(255, 255, 255, 0.5);
        }
    }
}

.is-form-error .input {
    padding: 8px 2px 8px 15px;
    border: 2px solid $red-color;

    &:focus,
    &:focus-visible {
        padding: 6px 0 6px 13px;
        border: 4px solid $blue-color;
        outline: 0;
    }
}