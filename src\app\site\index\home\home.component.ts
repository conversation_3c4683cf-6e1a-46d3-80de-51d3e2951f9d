import {
  After<PERSON>iew<PERSON>nit,
  Component,
  ElementRef,
  OnDestroy,
  OnInit,
  Renderer2,
  ViewChild,
} from '@angular/core';
import {
  FormGroup,
  FormBuilder,
  FormsModule,
  ReactiveFormsModule,
} from '@angular/forms';
import { MatDialog } from '@angular/material/dialog';
import { Router, RouterLink } from '@angular/router';
import { AskSearchDialogComponent } from '../../../dialog/ask-search-dialog/ask-search-dialog.component';
import { apiStatus } from '../../../enum/apiStatus.enum';
import { getDialectAndLanguageListResp } from '../../../interface/language.interface';
import { LanguageService } from '../../../service/curl/language.service';
import { ConfirmService } from '../../../service/utils/confirm.service';
import { NewsService } from '../../../service/curl/news.service';
import { HttpErrorResponse } from '@angular/common/http';
import { ShareService } from '../../../service/curl/share.service';
import { debounceTime, distinctUntilChanged, forkJoin } from 'rxjs';
import {
  autoSearchResp,
  KeyboardList,
} from '../../../interface/share.interface';
import { MatIcon } from '@angular/material/icon';
import { DatePipe } from '@angular/common';
declare var FB: any; // 確保 FB 可用

@Component({
  selector: 'app-home',
  templateUrl: './home.component.html',
  styleUrl: './home.component.scss',
  imports: [FormsModule, ReactiveFormsModule, MatIcon, RouterLink, DatePipe],
})
export class HomeComponent implements OnInit, AfterViewInit, OnDestroy {
  @ViewChild('keywordInput') keywordInput!: ElementRef;
  @ViewChild('searchAll') searchAll!: ElementRef;

  form: FormGroup;
  isLanguage: boolean = false;
  isKeyboard: boolean = false;
  keyboardList = KeyboardList;
  newsList: { id: string; title: string; date: string }[] = [];
  todaySentence: {
    id: string;
    tribe: string;
    originalSentence: string;
    chineseSentence: string;
  } = {
    id: '',
    tribe: '',
    originalSentence: '',
    chineseSentence: '',
  };
  languageList: { id: string; name: string; active?: boolean }[] = [];
  dialectList: {
    id: string;
    name: string;
    active: boolean;
    disabled: boolean;
    tribeId: string;
  }[] = [];
  groupList: { id: string; name: string; src?: string }[] = [];
  selectList: string[] = [];
  autoInputList: string[] = [];

  constructor(
    private newsService: NewsService,
    private languageService: LanguageService,
    private formBuilder: FormBuilder,
    private confirmService: ConfirmService,
    private shareService: ShareService,
    private router: Router,
    private matDialog: MatDialog,
    private renderer: Renderer2
  ) {
    this.form = this.formBuilder.group({
      keyword: [''],
    });
  }

  ngOnInit(): void {
    this.getDialectAndLanguageList();
    this.initialization();
    this.form
      .get('keyword')!
      .valueChanges.pipe(
        debounceTime(500), // 停止輸入 1 秒後觸發
        distinctUntilChanged() // 相同值不重複觸發
      )
      .subscribe((value: any) => {
        if (value) {
          this.callAutoInputAPI(value);
        } else {
          this.autoInputList = [];
        }
      });
  }

  // 監聽文檔點擊事件
  private documentClickListener: () => void = () => {};

  private setupDocumentClickListener() {
    setTimeout(() => {
      this.documentClickListener = this.renderer.listen(
        'document',
        'click',
        (event: MouseEvent) => {
          // 這裡需要先檢查 searchAll.nativeElement 是否存在
          if (
            this.searchAll?.nativeElement &&
            !this.searchAll.nativeElement.contains(event.target)
          ) {
            this.autoInputList = [];
          }
        }
      );
    });
  }

  // 組件銷毀時移除監聽器
  ngOnDestroy() {
    this.documentClickListener?.();
  }

  // 當搜尋框獲得焦點時重新設置監聽器
  onSearchFocus() {
    this.setupDocumentClickListener();
  }

  callAutoInputAPI(value: string): void {
    this.shareService.autoSearch(value, null).subscribe({
      next: (resp: autoSearchResp) => {
        this.autoInputList = resp.data.candidateWords;
      },
      error: () => {},
    });
  }

  ngAfterViewInit(): void {
    this.setupDocumentClickListener();
    if (typeof FB !== 'undefined' && FB.XFBML) {
      FB.XFBML.parse();
    }
  }

  openLanguageList(event: Event) {
    event.preventDefault();
    this.isLanguage = !this.isLanguage;
  }
  openKeyboard(event: Event) {
    event.preventDefault();
    this.isKeyboard = !this.isKeyboard;
  }

  initialization() {
    forkJoin({
      getNews: this.newsService.getNews(),
      getTodaySentence: this.shareService.getTodaySentence(),
    }).subscribe({
      next: (result) => {
        this.newsList = result.getNews.data.newsItems.slice(0, 6);
        this.todaySentence = result.getTodaySentence.data;
      },
      error: (err: HttpErrorResponse) => {
        console.error(err);
      },
    });
  }

  getDialectAndLanguageList() {
    if (
      this.languageList.length < 1 ||
      this.dialectList.length < 1 ||
      this.groupList.length < 1
    ) {
      this.languageService.getDialectAndLanguageList().subscribe({
        next: (resp: getDialectAndLanguageListResp) => {
          if (resp.status === apiStatus.SUCCESS) {
            const { tribes } = resp.data;
            this.languageList = tribes.map((tribe) => ({
              ...tribe,
              active: false,
            }));
            this.dialectList = tribes.flatMap((tribe) =>
              tribe.dialectList.map((dialect) => ({
                ...dialect,
                disabled: true,
                active: false,
              }))
            );
            this.groupList = tribes.map((tribe) => ({
              ...tribe,
              src: `image/${tribe.id}.png`,
            }));
          }
        },
        error: () => {},
      });
    }
  }

  search(event: Event) {
    event.preventDefault();
    const keyword = this.form.value.keyword?.trim();
    if (!keyword) {
      this.confirmService.showWARN('請務必輸入中文或族語，才能進行搜尋');
      return;
    }
    const selectedDialectList = this.dialectList
      .filter((item) => item.active)
      .map((item) => item.id);
    const selectedLanguageList = this.languageList
      .filter((item) => item.active)
      .map((item) => item.id);

    const indexSearch = {
      keyword,
      selectedDialectList,
      selectedLanguageList,
    };
    sessionStorage.setItem('indexSearch', JSON.stringify(indexSearch));

    this.matDialog
      .open(AskSearchDialogComponent, { autoFocus: false, disableClose: true })
      .afterClosed()
      .subscribe((result) => {
        if (result) {
          this.router.navigate(['search'], {
            state: {
              indexSearch,
            },
          });
        }
      });
  }

  singleTribeSearch(event: Event, item: { id: string; name: string }) {
    event.preventDefault();
    sessionStorage.setItem('singleTribe', JSON.stringify(item));
    this.router.navigate(['singleSearch'], {
      queryParams: {
        tribeId: item.id,
        tribeName: item.name,
      },
      state: {
        singleTribe: item,
      },
    });
  }

  keyboardUp(value: string, inputElement: HTMLInputElement) {
    const currentKeyword = this.form.value.keyword || '';
    const start = inputElement.selectionStart ?? currentKeyword.length;
    const end = inputElement.selectionEnd ?? currentKeyword.length;
    const newKeyword =
      currentKeyword.slice(0, start) + value + currentKeyword.slice(end);
    this.form.get('keyword')?.patchValue(newKeyword);
    const cursorPos = start + value.length;
    setTimeout(() => {
      inputElement.focus();
      inputElement.setSelectionRange(cursorPos, cursorPos);
    });
  }

  clear(event: Event) {
    event.preventDefault();
    this.form.get('keyword')?.patchValue('');
  }

  languageListChange(event: Event, categoryID: string) {
    const isChecked = (event.target as HTMLInputElement).checked;

    // 更新語言列表
    this.updateLanguageActiveState(categoryID, isChecked);

    // 更新方言列表
    this.dialectList.forEach((dialect) => {
      if (dialect.tribeId === categoryID) {
        dialect.active = isChecked;
        dialect.disabled = !isChecked;
      }
    });

    this.updateSelectList();
  }

  dialectListChange(event: Event, categoryID: string, tribeId: string) {
    const isChecked = (event.target as HTMLInputElement).checked;

    // 更新方言狀態
    this.dialectList.forEach((dialect) => {
      if (dialect.id === categoryID) {
        dialect.active = isChecked;
      }
    });

    // 更新語言列表狀態
    const hasActiveDialect = this.dialectList.some(
      (dialect) => dialect.tribeId === tribeId && dialect.active
    );

    if (!hasActiveDialect) {
      this.disableLanguageAndDialects(tribeId);
    }

    this.updateSelectList();
  }

  private updateLanguageActiveState(categoryID: string, isActive: boolean) {
    const language = this.languageList.find((lang) => lang.id === categoryID);
    if (language) {
      language.active = isActive;
    }
  }

  private disableLanguageAndDialects(tribeId: string) {
    const language = this.languageList.find((lang) => lang.id === tribeId);
    if (language) {
      language.active = false;
    }

    this.dialectList.forEach((dialect) => {
      if (dialect.tribeId === tribeId) {
        dialect.disabled = true;
        dialect.active = false;
      }
    });
  }

  private updateSelectList() {
    this.selectList = [
      ...this.languageList
        .filter((language) => language.active)
        .map((language) => language.name),
      ...this.dialectList
        .filter((dialect) => dialect.active)
        .map((dialect) => dialect.name),
    ];
  }

  selectValue(value: string) {
    this.form.patchValue({ keyword: value }, { emitEvent: false });
    this.autoInputList = [];
  }
}
