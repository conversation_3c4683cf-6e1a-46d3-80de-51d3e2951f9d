//鍵盤
.keyboard-group {
    margin: 10px 0;
    padding: 10px;
    width: 100%;
    box-sizing: border-box;
    background-color: #d8eed4;
    border-radius: 10px;
    box-shadow: 0px 0px 5px 2px rgba(0, 0, 0, 0.2);

    .keyboard-box {
        width: 100%;
        margin: 0;
        padding: 0;
        display: flex;
        flex-direction: row;
        align-items: center;
        justify-content: flex-start;
        flex-wrap: wrap;

        .keyboard-btn {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            background-color: #fff;
            border: 1px solid #949494;
            border-radius: 5px;
            box-shadow: 0px 2px 2px 1px rgba(0, 0, 0, 0.2);
            max-width: 60px;
            width: 100%;
            margin: 10px;
            padding: 5px 0;
            &:hover,
            &:focus {
                background: #255d1c;
                border-color: #255d1c;
                color: #fff;
                // opacity: 0.5;
            }
            &:active,
            &.active {
                background-color: #255d1c;
                border-color: #255d1c;
            }
            .keyboard_font {
                font-size: 1em;
            }
        }
    }
}

.keyboard-group2 {
    margin: 10px 0;
    padding: 10px;
    width: 100%;
    box-sizing: border-box;
    background-color: #d8eed4;
    border-radius: 10px;
    box-shadow: 0px 0px 5px 2px rgba(0, 0, 0, 0.2);
}
.keyboard-group2 .keyboard-box {
    width: 100%;
    margin: 0;
    padding: 0;
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: flex-start;
    flex-wrap: wrap;
}
.keyboard-group2 .keyboard-box .keyboard-btn {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    background-color: #fff;
    border: 1px solid #949494;
    border-radius: 5px;
    box-shadow: 0px 2px 2px 1px rgba(0, 0, 0, 0.2);
    max-width: 40px;
    width: 100%;
    margin: 10px;
    padding: 0 5px;
}
.keyboard-group2 .keyboard-box .keyboard-btn:hover,
.keyboard-group .keyboard-box .keyboard-btn:focus {
    background: #255d1c;
    border-color: #255d1c;
    color: #fff;
}
.keyboard-group2 .keyboard-box .keyboard-btn:active,
.keyboard-group .keyboard-box .keyboard-btn.active {
    background-color: #255d1c;
    border-color: #255d1c;
}
.keyboard-group2 .keyboard-box .keyboard-btn .keyboard_font {
    font-size: 0.75em;
    line-height: 2;
}
