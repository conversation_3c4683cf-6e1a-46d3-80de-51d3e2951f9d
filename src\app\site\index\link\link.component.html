<main class="master-pages-container-layout">
    <div class="master-pages-container-cont">
        <div class="cont pages-cont-layout">
            <div class="pages-cont-list-layout">

                <!--路徑列-->
                <div class="breadcrumb-layout">
                    <div>
                        <nav class="breadcrumb-cont">
                            <ol class="breadcrumb">
                                <li class="breadcrumb-item">
                                    <a href="" (click)="$event.defaultPrevented" [routerLink]="'/home'">
                                        <span>首頁</span>
                                        <span class="material-symbols-outlined">keyboard_arrow_right</span>
                                    </a>
                                </li>
                                <li class="breadcrumb-item active">相關連結</li>
                            </ol>
                        </nav>
                    </div>
                    <div>
                        <nav class="breadcrumb-cont">
                            <ol class="breadcrumb">
                                <li class="breadcrumb-item">
                                    <a href="" (click)="back($event)">
                                        <span>&lt;&lt;回上一頁</span>
                                    </a>
                                </li>
                            </ol>
                        </nav>
                    </div>
                </div>
                <div style="width: 100%;">
                    <!--內容區-->
                    <div class="search-group">
                        <input type="text" class="form-control" title="keyword" placeholder="請輸入關鍵字"
                            [(ngModel)]="keyword"> &nbsp;&nbsp;
                        <button class="btn-list btn-primary-solid" (click)="search()">搜尋</button>
                    </div>
                    <div class="table-box">
                        <table class="table-list-layout  table-list-style rwd-table01">
                            <tbody>
                                <tr class="th-no">
                                    @for (item of list; track $index) {
                                    <th>
                                        {{item.title}}
                                    </th>
                                    }
                                </tr>
                                @if(linkList.length>0){
                                @for (item of linkList; let index= $index;track index) {
                                <tr>
                                    <td class="text-c">
                                        <span class="rwd-th">項次</span>
                                        {{index+1+(nowPage>1?(nowPage-1)*pageSize:0)}}
                                    </td>
                                    <td class="text-l">
                                        <span class="rwd-th">網站名稱</span>
                                        {{item.websiteName}}
                                    </td>
                                    <td class="text-l">
                                        <span class="rwd-th">網址</span>
                                        <a [href]="safeUrl(item.websiteLink)" target="_blank">{{item.websiteLink}}</a>
                                    </td>
                                </tr>
                                }
                                }@else{
                                <tr>
                                    <td colspan="3" style="text-align: center;">沒有找到符合條件的資料</td>
                                </tr>
                                }
                            </tbody>
                        </table>
                        @if(linkList.length>0){
                        <div>
                            <app-paginator [pageSize]="pageSize" [nowPage]="nowPage" [totalRecords]="totalCount"
                                [pageShowCount]="pageShowCount"
                                currentPageReportTemplate="第 {first} 到 {last} 筆，共 {totalRecords} 筆"
                                (clickPageEvent)="getPageFromPaginator($event)"
                                (pageSizeChangeEvent)="getPageSizeFromPaginator($event)"></app-paginator>
                        </div>
                        }
                    </div>


                </div>
            </div>
        </div>
    </div>
</main>