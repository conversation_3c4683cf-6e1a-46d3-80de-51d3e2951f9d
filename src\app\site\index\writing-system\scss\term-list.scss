.term-list-layout {
    margin: 0;
    padding: 20px 50px;
    // padding:  20px 20px;
    width: 100%;
    box-sizing: border-box;
    // border: 1px solid #65676B;
    // border-radius: 5px;
}
.term-list-list {
    display: flex;
    flex-direction: column;
    // flex-wrap: wrap;
    align-items: stretch;
    border: 1px solid #65676b;
    border-radius: 5px;
    margin: 20px 0;
}
.term-list-box {
    display: flex;
    flex-direction: row;
    // justify-content: space-between;
    flex-wrap: nowrap;
    align-items: center;
    padding: 20px 20px;
    border-radius: 5px;
    background-color: #fff;
    overflow: hidden;
    // max-height: 80px;
    // width: 97%;
    .term-list-card {
        display: contents;
        width: calc(100% - 24px);
        height: 100%;
        // min-height: 80px;
        line-height: 1.5;
    }
}
.term-list-box2 {
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
    justify-content: flex-start;
    padding: 10px 20px;
    background-color: #e7e8e9;
}
.term-list-box3 {
    display: flex;
    flex-direction: column;
    flex-wrap: wrap;
    align-items: stretch;
    padding: 10px 20px;
    border-radius: 5px;
    background-color: #fff;
    .term-list-cont1 {
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        flex-wrap: wrap;
        align-items: center;
        border-radius: 5px;
    }
    .term-list-cont2 {
        display: flex;
        flex-direction: column;
        flex-wrap: wrap;
        align-items: flex-start;
        border-radius: 5px;
        padding-top: 10px;
    }
    img {
        width: 100%;
    }
}
.term-list-box4 {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    flex-wrap: wrap;
    align-items: center;
    padding: 20px 20px;
    background-color: #4a7f42;
}
.term-list-title {
    display: flex;
    flex-direction: row;
    // justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    .term-list-language {
        font-size: 1.13em;
        color: #65676b;
    }
    .term-list-h3 {
        font-size: 1.5em;
        font-weight: bold;
        // padding: 0 10px;
    }
    .material-symbols-outlined {
        font-variation-settings:
            "FILL" 1,
            "wght" 400,
            "GRAD" 0,
            "opsz" 24;
    }
    .term-list-h4 {
        font-size: 1.13em;
        // font-weight: bold;
        // padding: 0 5px;
    }
    .explain {
        background-color: #4a7f42;
        padding: 0px 20px;
        border-radius: 50px;
        margin: 5px 10px 5px 0;
        h4 {
            font-size: 1.13em;
            color: #fff;
            display: contents;
        }
    }
    .term-list-btns {
        // background-color: #4A7F42;
        cursor: pointer;
        padding: 5px 15px;
        border-radius: 50px;
        display: flex;
        flex-direction: row;
        align-items: center;
        justify-content: center;
        // width: 120px;
        margin: 5px;
        h4 {
            font-size: 1.13em;
            color: #fff;
            margin: 0;
            padding: 0 5px;
        }
    }
    .term-list-h5 {
        font-size: 1em;
        color: #65676b;
    }
}
.term-list-border {
    border: 0.5px solid #949494;
    width: 100%;
    margin-top: 10px;
}
.term-list-cont {
    font-size: 1.13em;
    color: #000;
}
a {
    text-decoration: none;
}

//寬度
.width_20 {
    width: 20%;
    // max-width: 300px;
    // min-width: max-content;
}
.width_60 {
    width: 60%;
    // max-width: 600px;
    // min-width: max-content;
}
//字體粗細
.font_bold {
    font-weight: bold;
}
//字體顏色
.font_g {
    color: #4a7f42;
}
.font_b {
    color: #000;
}
.font_w {
    color: #fff;
}

//背景顏色

.bg_fb {
    background-color: #1876f2;
}
.bg_fb:hover,
.bg_fb:focus {
    background: #176bd9;
}
.bg_fb:active,
.bg_fb.active {
    background-color: #176bd9;
    border-color: #176bd9;
}

.bg_line {
    background-color: #07b53b;
}
.bg_line:hover,
.bg_line:focus {
    background: #07a436;
}
.bg_line:active,
.bg_line.active {
    background-color: #07a436;
    border-color: #07a436;
}

.bg_opinion {
    background-color: #f0524b;
}
.bg_opinion:hover,
.bg_opinion:focus {
    background: #e04c45;
}
.bg_opinion:active,
.bg_opinion.active {
    background-color: #e04c45;
    border-color: #e04c45;
}

.bg_root {
    background-color: #000;
}
.bg_root:hover,
.bg_root:focus {
    background-color: #333333;
}
.bg_root:active,
.bg_root.active {
    background-color: #333333;
    border-color: #333333;
}

.bg_picture {
    background-color: #976530;
}
.bg_picture:hover,
.bg_picture:focus {
    background: #895d2e;
}
.bg_picture:active,
.bg_picture.active {
    background-color: #895d2e;
    border-color: #895d2e;
}
//空格
.text_r {
    padding-right: 10px;
}
.text_r100 {
    padding-right: 100px;
}
.text_l {
    padding-left: 10px;
}

@media (max-width: 640px) {
    .term-list-box {
        display: flex;
        flex-direction: row;
        flex-wrap: nowrap;
        align-items: center;
        padding: 20px 20px;
        max-height: 100%;

        .term-list-card {
            display: flex;
            justify-content: space-around;
            flex-direction: column;
            align-content: flex-start;
            align-items: flex-start;
        }
    }
    .term-list-title {
        padding: 5px 0;
        width: 100%;

        .term-list-h3 {
            padding: 0;
        }
    }
}
