.text-download-layout {
	margin: 0;
	width: 100%;
	box-sizing: border-box;
}
.table-download {
	display: flex;
	justify-content: center;
	flex-direction: row;
	flex-wrap: nowrap;
	align-items: center;
}
.checkbox-group {
	width: 100%;
	display: flex;
	flex-direction: row;
	flex-wrap: wrap;
}
.checkbox-group2 {
	width: 100%;
	display: flex;
	.input-list {
		width: calc(100% - 100px);
	}
}
.checkbox-menu {
	display: flex;
	align-items: stretch;
}
.material-symbols-outlined {
	font-variation-settings:
		"FILL" 1,
		"wght" 400,
		"GRAD" 0,
		"opsz" 24;
}
.btn-list {
	margin: 10px 0 10px 10px;
	padding: 14px 30px;
	border-radius: 5px;
	-moz-user-select: none;
	background-image: none;
	border: 1px solid transparent;
	border-radius: 5px;
	cursor: pointer;
	display: inline-block;
	font-weight: 400;
	line-height: 1.2;
	text-align: center;
	font-size: 1em;
	white-space: nowrap;
}
.table-download {
	a {
		padding: 0 30px;
		text-decoration: none;
		display: block !important;
	}
}

.btn-list2 {
	margin: 10px 0;
	padding: 20px 30px;
	border-radius: 5px;
	-moz-user-select: none;
	background-image: none;
	border: 1px solid transparent;
	border-radius: 5px;
	cursor: pointer;
	display: inline-block;
	font-weight: 400;
	line-height: 1.2;
	text-align: center;
	font-size: 1em;
	white-space: nowrap;
}

.btn-primary-color {
	background: #4a7f42;
	color: #fff;
	&:hover,
	&:focus {
		background: #255d1c;
		// opacity: 0.5;
	}
	&:active,
	&.active {
		background-color: #255d1c;
		border-color: #255d1c;
	}
}

//字體顏色
.font_r {
	color: #e41e3f;
}
.font_pdf {
	color: #c91328;
}
.font_odt {
	color: #337ab7;
}

//字體排版
.text_c {
	text-align: center;
}
.text_l {
	text-align: left;
}
.text_r {
	text-align: right;
}
//字體大小
.a-font {
	font-size: 0.9em;
	font-weight: bold;
}

//表格通用
tr {
	&:nth-child(odd) {
		background-color: #e5efe3 !important;
	}
}

.table_g {
	background-color: #4a7f42;
	color: #fff;
}

@media (max-width: 640px) {
	.rwd-table03 {
		width: 100%;
		tr,
		th,
		td {
			display: block;
			width: auto !important;
			text-align: center;
			&.th_no {
				display: none;
			}
		}
		tr {
			margin: 0 0 5px;
			border-width: 1px;
			border-style: solid;
			tr {
				margin: 0;
				border: 0;
			}
		}
		td {
			border: 0px !important;
		}
		.rwd-th02 {
			color: #fff;
		}
	}
}
