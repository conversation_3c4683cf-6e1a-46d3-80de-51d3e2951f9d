import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';

@Injectable({
  providedIn: 'root',
})
export class FileService {
  constructor(private httpClient: HttpClient) {}

  getAudioFile(fileId: string): Observable<string> {
    return this.httpClient.get(`api/app/file/download-file/${fileId}`, {
      responseType: 'text',
    });
  }
}
