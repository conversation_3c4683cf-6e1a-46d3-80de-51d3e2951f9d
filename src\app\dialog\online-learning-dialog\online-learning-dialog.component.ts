import { Component, Inject } from '@angular/core';
import {
  MAT_DIALOG_DATA,
  MatDialog,
  MatDialogRef,
  MatDialogTitle,
  MatDialogContent,
  MatDialogActions,
  MatDialogClose,
} from '@angular/material/dialog';
import {
  audioItem,
  dictionaryItem,
  getShareDetailResp,
  shareDetailWordItem,
  wordItem,
} from '../../interface/language.interface';
import { ShareType } from '../../site/singleTribe/single-tribe-search/single-tribe-search.component';
import { HttpErrorResponse } from '@angular/common/http';
import { environment } from '../../../environments/environment';
import { FileService } from '../../service/curl/file.service';
import { LanguageService } from '../../service/curl/language.service';
import { ConfirmService } from '../../service/utils/confirm.service';
import { AnaphoraSentenceDialogComponent } from '../anaphora-sentence-dialog/anaphora-sentence-dialog.component';
import { OwlOptions, CarouselModule } from 'ngx-owl-carousel-o';
import { Router } from '@angular/router';
import { RootStructureDialogComponent } from '../root-structure-dialog/root-structure-dialog.component';
import { MatIcon } from '@angular/material/icon';
import { CdkScrollable } from '@angular/cdk/scrolling';
import { MatProgressSpinner } from '@angular/material/progress-spinner';
import {
  MatExpansionPanel,
  MatExpansionPanelHeader,
} from '@angular/material/expansion';
import { NgClass } from '@angular/common';
import { UtilsService } from '../../service/utils/utils.service';
import { DomSanitizer, SafeHtml } from '@angular/platform-browser';
declare const FB: any;

@Component({
  selector: 'app-online-learning-dialog',
  templateUrl: './online-learning-dialog.component.html',
  styleUrl: './online-learning-dialog.component.scss',
  imports: [
    MatDialogTitle,
    MatIcon,
    CdkScrollable,
    MatDialogContent,
    MatProgressSpinner,
    MatExpansionPanel,
    MatExpansionPanelHeader,
    NgClass,
    CarouselModule,
    MatDialogActions,
    MatDialogClose,
  ],
})
export class OnlineLearningDialogComponent {
  isLoading: boolean = false;
  isImage: boolean = false;
  isDerivativeRoot: boolean = false;
  isDr: boolean = false;
  wordItem: shareDetailWordItem = {
    audioItems: [],
    id: '',
    name: '',
    pinyin: '',
    variant: '',
    formationWord: '',
    derivativeRoot: '',
    frequency: 0,
    hit: 0,
    dictionaryNote: '',
    sources: [],
    explanationItems: [],
    dialect: '',
    tribeId: '',
    tribe: '',
    isDerivativeRoot: false,
  };
  shareType = ShareType;

  private currentMediaElement: HTMLAudioElement | null = null;
  customOptions: OwlOptions = {
    loop: false,
    navSpeed: 700,
    dots: true,
    items: 1,
    center: true,
  };

  fontSize: number = 1;

  constructor(
    @Inject(MAT_DIALOG_DATA)
    public data: {
      id: string;
    },
    private dialogRef: MatDialogRef<OnlineLearningDialogComponent>,
    private router: Router,
    private matDialog: MatDialog,
    private confirmService: ConfirmService,
    private languageService: LanguageService,
    private fileService: FileService,
    private utils: UtilsService,
    private sanitizer: DomSanitizer,
  ) { }
  close() {
    this.dialogRef.close();
  }

  ngOnInit(): void {
    this.utils.fontSize$.subscribe((size) => {
      this.fontSize = size === 0 ? 1 : size;
    });
    if (!this.data.id) {
      this.confirmService
        .showError('詞語不存在', '錯誤')
        .afterClosed()
        .subscribe(() => {
          this.router.navigate(['home']);
        });
    } else {
      this.getWorItem(this.data.id);
    }
  }

  ngOnDestroy(): void {
    this.stopMusic();
  }

  getWorItem(id: string) {
    this.isLoading = true;
    this.languageService.getShareDetail(id).subscribe({
      next: (resp: getShareDetailResp) => {
        this.isLoading = false;
        this.isDerivativeRoot = resp.data.isDerivativeRoot;
        this.isImage = resp.data.isImage;
        this.wordItem = resp.data.word;
      },
      error: (err: HttpErrorResponse) => {
        this.isLoading = false;
      },
    });
  }

  onPanelClose(item: dictionaryItem) {
    item.isOpenPanel = false;
  }

  clickAnaphoraSentence(id: string | null) {
    if (id === null) {
      return;
    }
    this,
      this.matDialog.open(AnaphoraSentenceDialogComponent, {
        disableClose: true,
        autoFocus: false,
        width: '60%',

        data: {
          id: id,
        },
      });
  }

  share(type: ShareType, id: string) {
    const shareUrl = `${environment.sitePath}/sharePage?id=${id}`;
    switch (type) {
      case this.shareType.FB:
        FB.ui(
          {
            method: 'share',
            href: shareUrl,
          },
          (response: any) => {
            if (response && !response.error_message) {
              console.log('分享成功');
            } else {
              console.error('分享失敗或取消', response);
            }
          }
        );
        break;
      case this.shareType.LINE:
        window.open(
          `https://social-plugins.line.me/lineit/share?url=${environment.sitePath}/sharePage?id=${id}`,
          '_blank',
          'noopener,noreferrer'
        );
        break;
      case this.shareType.IG:
        window.open('https://example.com', '_blank', 'noopener,noreferrer');
        break;
    }
  }

  question(item: wordItem) {
    window.open(
      `${environment.sitePath}/singleQuestion?id=${item.id}&tribeId=${item.tribeId}`,
      '_blank'
    );
  }

  getRootStructure(dictionaryId: string) {
    this.matDialog.open(RootStructureDialogComponent, {
      disableClose: true,
      autoFocus: false,
      width: '60%',
      data: {
        dictionaryId: dictionaryId,
      },
    });
  }

  play(item: audioItem) {
    if (this.currentMediaElement) {
      this.currentMediaElement.pause();
      this.currentMediaElement.remove();
    }
    this.fileService.getAudioFile(item.fileId).subscribe({
      next: (resp: string) => {
        let mediaElement: HTMLAudioElement = document.createElement('audio');
        mediaElement.style.display = 'none'; // 這行讓音頻播放器隱藏
        mediaElement.setAttribute('src', resp); // 設置音頻源
        mediaElement.setAttribute('controls', 'true'); // 加入控制條
        document.body.appendChild(mediaElement);
        mediaElement.play();
        mediaElement.addEventListener('ended', () => {
          this.currentMediaElement = null; // 重置當前音樂播放器
        });

        this.currentMediaElement = mediaElement; // 儲存當前的音樂播放器
      },
      error: () => { },
    });
  }

  stopMusic() {
    if (this.currentMediaElement) {
      this.currentMediaElement.pause();
      this.currentMediaElement.remove();
      this.currentMediaElement = null;
    }
  }

  onCopy(event: ClipboardEvent) {
    event.preventDefault(); // 阻止預設複製行為
    const selection = window.getSelection();
    if (selection) {
      let copiedText = selection.toString()
        .replace(/\r?\n/g, ' ')  // 移除換行
        .replace(/\s+/g, ' ');   // 將多個空格替換為單一空格
      event.clipboardData?.setData('text/plain', copiedText);
    }
  }

  openImage(item: shareDetailWordItem) {
    item.explanationItems.map((item) => {
      return (item.isImage = !item.isImage);
    });
  }

  sanitizeExplanation(rawHtml: string): SafeHtml {
    return this.sanitizer.bypassSecurityTrustHtml(rawHtml);
  }

  isNameMatched(a: string, b: string): boolean {
    return a?.toLowerCase() === b?.toLowerCase();
  }

}
