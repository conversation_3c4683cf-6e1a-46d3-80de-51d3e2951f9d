/* Sass Document */
.master-pages-container-layout{
	//height: calc(100% - 126px)
	padding: 180px 0 0;
	display: flex;
	flex-direction: column;
	justify-content: space-between;
}
	
//主內容放置區
.master-pages-container-cont-layout{
	margin: 0 auto;
	padding: 0;
	width: 100%;
	height: 100%;
	background-color: transparent;
	flex-grow: 1;
}
	
.master-pages-container-cont{
	margin: 0 auto;
	padding: 0;
	width: 100%;
	height: 100%;
	flex-grow: 1;
}

//版型配置
.cont-row {
    margin: 0 auto;
    padding: 0;
    display: flex;
    flex-wrap: nowrap;
	width: 100%;
}

.cont{
    margin: 0 auto;
    padding: 10px;
    flex-grow: 1;
    max-width: calc(100% - 35px);
	box-sizing: border-box;
}


.pages-cont-layout{
	margin: 0 auto;
    padding: 0;
	display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: flex-start;
	max-width: 100%;
}

.pages-cont-list-layout{
	margin: 0 auto;
    padding: 0;
	display: flex;
    flex-direction: column;
    align-items: flex-start;
    justify-content: flex-start;
	max-width: 1600px;
	width: 100%;
}

//標題
.master-index-title{
	margin: 0 auto;
	padding: 0 0 20px;
	width: 100%;
	height: 100%;
	font-size: 2.188em;
	font-weight: bold;
	text-align: center;
}

.master-pages-title{
	margin: 0;
	padding: 0;
	display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: flex-start;
	font-size: 1.75em;
	&:before{
		content: '';
		padding: 0 5px 0 0;
		display: block;
		border-left-style: solid;
		border-left-width: 8px;
		font-weight: bold;
		height: 24px;
	}
}


@media (max-width: 905px){
	.master-pages-container-layout{
		padding: 62px 0 0;
	}
	.cont-row {
		flex-direction: column
	}
	.cont{
		max-width: 100%;
	}
}
	