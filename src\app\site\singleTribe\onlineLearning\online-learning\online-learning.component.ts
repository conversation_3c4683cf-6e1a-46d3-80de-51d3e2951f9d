import { Component, OnInit } from '@angular/core';
import { GetEthnicityService } from '../../../../service/utils/get-ethnicity.service';
import { forkJoin } from 'rxjs';
import { OnlineLearningService } from '../../../../service/curl/online-learning.service';
import {
  getLearningDataReq,
  getLearningDataResp,
  getQuestionResp,
  sendAnswerResp,
  wordItem,
} from '../../../../interface/onlineLearning.interface';
import { MatDialog } from '@angular/material/dialog';
import { OnlineLearningDialogComponent } from '../../../../dialog/online-learning-dialog/online-learning-dialog.component';
import { UtilsService } from '../../../../service/utils/utils.service';
import { RouterLink } from '@angular/router';
import { MatIcon } from '@angular/material/icon';
import { NgClass } from '@angular/common';
import { PaginatorComponent } from '../../../../utils/paginator/paginator.component';
import { FormsModule } from '@angular/forms';
import { MatProgressSpinner } from '@angular/material/progress-spinner';

@Component({
  selector: 'app-online-learning',
  templateUrl: './online-learning.component.html',
  styleUrl: './online-learning.component.scss',
  imports: [
    RouterLink,
    MatIcon,
    NgClass,
    PaginatorComponent,
    FormsModule,
    MatProgressSpinner,
  ],
})
export class OnlineLearningComponent implements OnInit {
  tribeId: string | null = null;
  ethnicity: string | null = null;
  isLanguard: boolean = true;
  isCategory: boolean = false;
  isQuestion: boolean = false;
  isQuestionSpinner: boolean = false;
  isAnswer: boolean = false;
  searchOption: {
    mainDialectId?: string | null;
    dialectItems: {
      selected?: boolean;
      id: string;
      name: string;
    }[];
    categoryItems: {
      id: string;
      name: string;
    }[];
  } = {
    mainDialectId: null, // 默认值为 null
    dialectItems: [],
    categoryItems: [],
  };

  dialectList: string[] = [];
  categoryList: string[] = [];

  list: {
    column: string;
    title: string;
    width: string;
  }[] = [
    {
      column: '',
      title: '項次',
      width: '5%',
    },
    {
      column: '',
      title: '語別',
      width: '20%',
    },
    {
      column: '',
      title: '類別',
      width: '30%',
    },
    {
      column: '',
      title: '詞項',
      width: '',
    },
    {
      column: '',
      title: '中文解釋',
      width: '20%',
    },
    {
      column: '',
      title: '功能',
      width: '15%',
    },
  ];

  learningDataList: wordItem[] = [];
  questionList: {
    questionItems: {
      id: string;
      dictionaryName: string;
      isAns?: boolean;
      isCorrect?: boolean;
    }[];
    quizImageUrl: string;
  } = {
    questionItems: [],
    quizImageUrl: '',
  };

  ansList?: { correct: boolean; correctAnswer: string };

  pageSize: number = 10;
  nowPage: number = 1;
  totalCount: number = 0;
  pageShowCount: number = 5; //分頁器秀幾個

  constructor(
    private getEthnicityService: GetEthnicityService,
    private onlineLearningService: OnlineLearningService,
    private matDialog: MatDialog,
    private utils: UtilsService
  ) {}

  ngOnInit(): void {
    this.tribeId = this.getEthnicityService.GetEthnicityId();
    this.ethnicity = this.getEthnicityService.GetEthnicityName();
    this.utils.setTitle(`${this.ethnicity}-線上學習`);
    this.initialization();
  }
  initialization() {
    forkJoin({
      searchOption: this.onlineLearningService.getSearchOption(
        this.tribeId as string
      ),
      // question: this.onlineLearningService.getQuestion(this.tribeId as string),
    }).subscribe({
      next: (result) => {
        this.searchOption = result.searchOption.data;
        // this.questionList = result.question.data;
        // 根据 mainDialectId 默认选中复选框
        if (this.searchOption.mainDialectId) {
          const selectedDialect = this.searchOption.dialectItems.find(
            (item) => item.id === this.searchOption.mainDialectId
          );

          if (selectedDialect) {
            selectedDialect.selected = true; // 设置选中状态为 true
            this.selectDialect(selectedDialect.id); // 调用方法处理选中逻辑
          }
        } else {
          // console.log('沒有主語別')
          this.searchLearningData();
        }
      },
    });
  }

  getQuestion() {
    this.isAnswer = false; //初始化作答情形
    this.isQuestionSpinner = true;
    this.onlineLearningService.getQuestion(this.tribeId as string).subscribe({
      next: (resp: getQuestionResp) => {
        this.isQuestionSpinner = false;
        this.questionList = resp.data;
      },
      error: () => {
        this.isQuestionSpinner = false;
      },
    });
  }

  selectDialect(id: string) {
    this.dialectList = this.dialectList.includes(id)
      ? this.dialectList.filter((item) => item !== id) // 移除
      : [...this.dialectList, id]; // 新增
    this.nowPage = 1;
    this.searchLearningData();
  }
  selectCategory(id: string) {
    this.categoryList = this.categoryList.includes(id)
      ? this.categoryList.filter((item) => item !== id) // 移除
      : [...this.categoryList, id]; // 新增
    this.nowPage = 1;
    this.searchLearningData();
  }

  searchLearningData() {
    let req: getLearningDataReq = {
      page: this.nowPage,
      pageSize: this.pageSize,
      tribeId: this.tribeId as string,
      dialectId: this.dialectList,
      categories: this.categoryList,
    };
    this.learningDataList = [];
    this.onlineLearningService.getLearningData(req).subscribe({
      next: (resp: getLearningDataResp) => {
        this.learningDataList = resp.data.wordItems;
        this.totalCount = resp.data.itemTotalCount;
      },
    });
  }

  send(id: string) {
    if (this.isAnswer) {
      return;
    }
    this.onlineLearningService.sendAnswer(id).subscribe({
      next: (resp: sendAnswerResp) => {
        const targetItem = this.questionList.questionItems.find(
          (item) => item.id === id
        );

        const correcttargetItem = this.questionList.questionItems.find(
          (item) => item.id === resp.data.correctAnswer
        );

        if (targetItem) {
          targetItem.isCorrect = resp.data.correct;
          targetItem.isAns = true;
        }

        if (correcttargetItem) {
          correcttargetItem.isCorrect = true;
          correcttargetItem.isAns = true;
        }

        this.isAnswer = true;
      },
    });
  }

  checkIsCorrect(isAns?: boolean, isCorrect?: boolean) {
    return isAns ? (isCorrect ? 'question-correct' : 'question-error') : '';
  }

  /**
   * 第幾頁
   * @param item number
   */
  getPageFromPaginator(nowPage: number) {
    this.nowPage = nowPage;
    this.searchLearningData();
    window.scrollTo({
      top: 0,
      behavior: 'smooth',
    });
  }
  /**
   * 每頁資料數量
   * @param item number
   */
  getPageSizeFromPaginator(pageSize: number) {
    this.pageSize = pageSize;
    this.nowPage = 1;
    this.searchLearningData();
    window.scrollTo({
      top: 0,
      behavior: 'smooth',
    });
  }

  detail(id: string) {
    this.matDialog.open(OnlineLearningDialogComponent, {
      disableClose: true,
      autoFocus: false,
      width: '60%',
      data: {
        id: id,
      },
    });
  }

  back(event: Event) {
    event.preventDefault();
    history.back();
  }

  openIsQuestion() {
    this.isQuestion = !this.isQuestion;
    this.getQuestion();
  }
}
