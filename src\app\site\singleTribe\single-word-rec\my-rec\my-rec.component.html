<div style="width: 100%;">
    <form [formGroup]="form">
        <div class="question-group">
            <div class="question-dec">
                <span>族語辭典需要大家用心灌溉，若發現自己知道的族語單詞尚未收錄其中，或是想學習的族語查無結果，歡迎熱心貢獻、提出寶貴建議造福族人！所貢獻的族語單詞一經本會採納，將收錄至本辭典並且列入族語貢獻者名單。
                </span>
            </div>
            <div class="question-input-box">
                <span>詞項</span>
                <div class="input-list" style="display: flex;">
                    <div class="search-box">
                        <input style="width: 100%" class="search-bar font_18" type="text" title="dictionary"
                            formControlName="dictionary" placeholder="請輸入族語" #keywordInput>&nbsp;&nbsp;
                        @if(form.value.dictionary&&form.value.dictionary.length>0){
                        <a class="search-a1" href="" (click)="$event.preventDefault()"><span
                                class="material-symbols-outlined" (click)="clearDictionary()">close</span></a>
                        }
                    </div>
                    <button class="btn-list btn-primary-solid" style="margin: 11px 0;"
                        (click)="isKeyboard=!isKeyboard">虛擬鍵盤</button>
                </div>
                @if(isKeyboard){
                <div class="keyboard-group2">
                    <div class="keyboard-box">
                        @for (item of keyboardList; track item) {
                        <button class="keyboard-btn" type="button" (click)="keyboardUp(item,keywordInput)">
                            <label class="keyboard_font">{{item}}</label>
                        </button>
                        }
                    </div>
                </div>
                }
            </div>
            <div class="question-input-box">
                <span><span>*</span>解釋</span>
                <input class="form-control" type="text" title="chineseExplanation" formControlName="chineseExplanation"
                    placeholder="請輸入中文解釋">
            </div>
            <div class="question-input-box">
                <span>語別</span>
                <select title="dialect" formControlName="dialect">
                    <option [ngValue]="null" disabled selected>請選語別</option>
                    @for (option of dialectList; track option) {
                    <option [ngValue]="option.id">{{option.name}}</option>
                    }
                </select>
            </div>
            <div class="question-input-box">
                <span>備註</span>
                <textarea class="form-control" formControlName="dictionaryNote" placeholder="例如：一種傳統的狩獵工具"
                    maxlength="500" style="font-size: 1em;"></textarea>
                <div style="display: flex;">
                    <span style="margin-left: auto;"><span
                            style="color:red">{{this.form.value.dictionaryNote?this.form.value.dictionaryNote.length:0}}</span>/500</span>
                </div>
            </div>
            <div class="question-input-box">
                <span><span>*</span>推薦者</span>
                <input class="form-control" type="text" title="name" formControlName="name" placeholder="請輸入姓名或暱稱">
            </div>
            <div class="question-input-box">
                <span><span>*</span>Email(前端不顯示)</span>
                <input class="form-control" type="text" title="email" formControlName="email" placeholder="請輸入Email">
            </div>
            <div class="question-input-box">
                <span><span>*</span>連絡電話(前端不顯示)</span>
                <input class="form-control" type="text" title="phone" formControlName="phone" placeholder="請輸入連絡電話">
            </div>
            <div class="question-input-box">
                <span><span>*</span>驗證碼</span>
                <div class="captcha-box">
                    <input class="form-control" type="text" title="captcha" formControlName="captcha"
                        placeholder="請輸入驗證碼">&nbsp;&nbsp;
                    <img [src]="img" alt="captcha">
                    <mat-icon (click)="getCaptcha()">refresh</mat-icon>
                    <mat-icon (click)="play()">volume_up</mat-icon>
                </div>
            </div>
            <div class="btn-group">
                <button class="btn-list btn-primary-solid" (click)="send()">送出</button>
            </div>
        </div>
    </form>
</div>