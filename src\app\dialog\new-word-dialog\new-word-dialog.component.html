<div mat-dialog-title class="success-title">
    <mat-icon class="dialog-close-btn" tabindex="0" (click)="close()">close</mat-icon>
</div>
<mat-dialog-content>
    @if(isLoading){
    <div class="spinner-wrapper-index">
        <mat-spinner class="mat-spinner-color"></mat-spinner>
    </div>
    }@else{
    <div class="text-center">
        <div class="font-deep">
        </div>
        <div class="font-deep group-msg">
            @if(newWordDetailItem?.originalSentence||newWordDetailItem?.chineseSentence){
            <span [innerHTML]="getFormattedSentence(newWordDetailItem?.originalSentence || '')"></span>
            <span>{{newWordDetailItem?.chineseSentence}}</span>
            }@else{
            <div class="text-center">
                <span>無</span>
            </div>
            }
            <!-- @for ( item of anaphoraDetail?.explanationItems;let index=$index; track item) {
            <div style="display: flex;flex-direction: column;border-bottom: solid 2px gray;;">
                <>解釋{{index+1}}:{{item.chineseExplanation.length>0?item.chineseExplanation:'無'}}</span>
                <span>範疇{{index+1}}:{{item.category.length>0?item.category:'無'}}</span>
            </div>
            } -->
        </div>
    </div>
    }
    <mat-dialog-actions [align]="'center'">
        <button class="btn-list btn-primary-solid" [mat-dialog-close]="true">關閉</button>
    </mat-dialog-actions>
</mat-dialog-content>