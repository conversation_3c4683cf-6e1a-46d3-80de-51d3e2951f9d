import { Component, Input, <PERSON><PERSON><PERSON>roy, OnInit } from '@angular/core';
import {
  FormBuilder,
  FormGroup,
  Validators,
  FormsModule,
  ReactiveFormsModule,
} from '@angular/forms';
import { SafeHtml } from '@angular/platform-browser';
import { ProcessingBlobFilesService } from '../../../../service/utils/processing-blob-files.service';
import { QuestionService } from '../../../../service/curl/question.service';
import { LanguageService } from '../../../../service/curl/language.service';
import { forkJoin } from 'rxjs';
import { GetEthnicityService } from '../../../../service/utils/get-ethnicity.service';
import { SingleWordRecService } from '../../../../service/curl/single-word-rec.service';
import { postMyRecReq } from '../../../../interface/single-word-rec.interface';
import { ConfirmService } from '../../../../service/utils/confirm.service';
import { defaultItem } from '../../../../interface/share.interface';
import { apiStatus } from '../../../../enum/apiStatus.enum';
import { MatTabChangeEvent, MatTabGroup } from '@angular/material/tabs';
import { HttpErrorResponse } from '@angular/common/http';
import { MatIcon } from '@angular/material/icon';

@Component({
  selector: 'app-my-rec',
  templateUrl: './my-rec.component.html',
  styleUrl: './my-rec.component.scss',
  imports: [FormsModule, ReactiveFormsModule, MatIcon],
})
export class MyRecComponent implements OnInit, OnDestroy {
  @Input() tabGroup!: MatTabGroup;
  tribeId: string | null = null;
  form: FormGroup;
  img!: SafeHtml;
  audio!: string;
  sessionId: string = '';

  isKeyboard: boolean = false;
  keyboardList: string[] = []; //虛擬鍵盤list
  dialectList: { id: string; name: string }[] = [];

  private currentMediaElement: HTMLAudioElement | null = null;
  constructor(
    private formBuilder: FormBuilder,
    private confirmService: ConfirmService,
    private languageService: LanguageService,
    private questionService: QuestionService,
    private getEthnicityService: GetEthnicityService,
    private processingBlobFilesService: ProcessingBlobFilesService,
    private singleWordRecService: SingleWordRecService
  ) {
    this.form = this.formBuilder.group({
      dictionary: [''],
      chineseExplanation: ['', Validators.required],
      dialect: [null],
      dictionaryNote: [''],
      name: ['', Validators.required],
      email: ['', Validators.required],
      phone: ['', Validators.required],
      captcha: ['', Validators.required],
    });
  }

  ngOnInit(): void {
    window.scrollTo({
      top: 0,
      behavior: 'smooth',
    });
    this.getCaptcha();
    this.initialization();
  }

  ngOnDestroy(): void {
    // 離開頁面時停止並移除播放器
    this.stopMusic();
  }

  initialization() {
    this.tribeId = this.getEthnicityService.GetEthnicityId();
    forkJoin({
      ethnicityLanguage: this.getEthnicityLanguage(),
      ethnicityKeyboard: this.getEthnicityKeyboard(),
    }).subscribe({
      next: (result) => {
        const ethnicityLanguage = result.ethnicityLanguage;
        const ethnicityKeyboard = result.ethnicityKeyboard;
        this.keyboardList = ethnicityKeyboard.data.symbolList;
        this.dialectList = ethnicityLanguage.data.items;
      },
    });
  }

  getEthnicityLanguage() {
    return this.languageService.getEthnicityLanguage(this.tribeId as string);
  }

  getEthnicityKeyboard() {
    return this.languageService.getEthnicityKeyboard(this.tribeId as string);
  }

  keyboardUp(value: string, inputElement: HTMLInputElement) {
    const currentKeyword = this.form.value.dictionary || '';
    const start = inputElement.selectionStart ?? currentKeyword.length;
    const end = inputElement.selectionEnd ?? currentKeyword.length;
    const newKeyword =
      currentKeyword.slice(0, start) + value + currentKeyword.slice(end);
    this.form.get('dictionary')?.patchValue(newKeyword);
    const cursorPos = start + value.length;
    setTimeout(() => {
      inputElement.focus();
      inputElement.setSelectionRange(cursorPos, cursorPos);
    });
  }

  clearDictionary() {
    this.form.patchValue({ dictionary: '' });
  }

  stopMusic() {
    if (this.currentMediaElement) {
      this.currentMediaElement.pause();
      this.currentMediaElement.remove();
      this.currentMediaElement = null;
    }
  }

  getCaptcha() {
    this.stopMusic();
    this.questionService.getCaptcha().subscribe({
      next: (resp: Blob) => {
        const reader = new FileReader();

        // 使用 FileReader 读取 Blob 数据
        reader.onload = () => {
          const data = reader.result as ArrayBuffer;

          // 转换为 Uint8Array 以便处理二进制数据
          const uint8Array = new Uint8Array(data);

          // 定义边界标记
          const boundary = new TextEncoder().encode('--file_boundary\r\n');

          // 分割多部分内容
          const parts = this.processingBlobFilesService.splitByBoundary(
            uint8Array,
            boundary
          );

          // 处理每一部分内容
          parts.forEach((part) => {
            const contentType =
              this.processingBlobFilesService.extractContentType(part);
            const contentData =
              this.processingBlobFilesService.extractContentData(part);

            if (contentType === 'image/png') {
              // 创建图片 Blob 并显示图片
              const imageBlob = new Blob([contentData], { type: 'image/png' });
              this.img =
                this.processingBlobFilesService.getSafeImageUrl(imageBlob);
            } else if (contentType === 'audio/mpeg') {
              // 创建音频 Blob 并播放音频
              const audioBlob = new Blob([contentData], { type: 'audio/mpeg' });
              this.audio = URL.createObjectURL(audioBlob);
            } else {
              // 嘗試解析 sessionId
              const sessionText = new TextDecoder().decode(contentData);
              if (
                sessionText.trim().startsWith('{') &&
                sessionText.trim().endsWith('}')
              ) {
                // 嘗試解析 JSON
                const json = JSON.parse(sessionText);
                if (json.sessionId) {
                  this.sessionId = json.sessionId;
                }
              }
            }
          });
        };

        reader.readAsArrayBuffer(resp);
      },
      error: () => {},
    });
  }

  play() {
    this.stopMusic();
    let mediaElement: HTMLAudioElement = document.createElement('audio');
    mediaElement.style.display = 'none'; // 這行讓音頻播放器隱藏
    mediaElement.setAttribute('src', this.audio); // 設置音頻源
    mediaElement.setAttribute('controls', 'true'); // 加入控制條
    document.body.appendChild(mediaElement);
    mediaElement.play();
    this.currentMediaElement = mediaElement; // 儲存當前的音樂播放器
  }
  send() {
    if (!this.form.valid) {
      this.confirmService.showWARN('必填欄位尚未填寫');
      return;
    }
    let req: postMyRecReq = {
      dictionaryName: this.form.value.dictionary,
      chineseExplanation: this.form.value.chineseExplanation,
      tribeId: this.tribeId as string,
      dialectId: this.form.value.dialect,
      dictionaryNote: this.form.value.dictionaryNote,
      name: this.form.value.name,
      email: this.form.value.email,
      phone: this.form.value.phone,
      verifyCode: this.form.value.captcha,
      sessionId: this.sessionId,
    };
    this.singleWordRecService.postMyRec(req).subscribe({
      next: (resp: defaultItem) => {
        if (resp.status === apiStatus.SUCCESS) {
          this.confirmService
            .showSUCCESS('您的詞項推薦將收錄推薦列表頁面，您可進行查看')
            .afterClosed()
            .subscribe(() => {
              this.form.reset();
              this.tabGroup.selectedIndex = 2;
            });
        } else {
          this.confirmService.showError(resp.message, '錯誤');
        }
      },
      error: (err: HttpErrorResponse) => {
        console.log(err.error.error.details);
        this.confirmService.showError(err.error.error.details, '錯誤');
      },
    });
  }
}
